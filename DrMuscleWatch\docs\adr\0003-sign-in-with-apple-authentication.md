# ADR-0003: Sign in with Apple Authentication

*   **Status:** Accepted
*   **Date:** {{CURRENT_DATE}}
*   **Deciders:** Project Lead
*   **Consulted:** AI Assistant
*   **Informed:** Development Team

## Context and Problem Statement

The standalone watch app requires users to authenticate to access their workout plans and sync data. We need a secure and user-friendly authentication method suitable for the watch interface, avoiding manual email/password entry.

## Decision Drivers

*   **User Experience:** Authentication should be seamless and require minimal input on the watch.
*   **Security:** Leverage robust, platform-provided authentication mechanisms.
*   **Platform Integration:** Use standard Apple frameworks.
*   **Avoid Watch Keyboard:** Direct text input on the watch is cumbersome.

## Considered Options

*   **Option 1: Sign in with Apple:** Utilize Apple's native authentication service directly on the watch.
    *   *Pros:* Extremely smooth UX on Apple devices, highly secure, platform standard, avoids password management for the user, handles private email relay.
    *   *Cons:* Requires backend support for validating Apple ID tokens, users without existing accounts linked to Apple ID might need a separate linking step (handled outside V1 watch app).
*   **Option 2: Device Code Flow:** Watch displays a code, user enters it on a web page after logging in via phone/desktop.
    *   *Pros:* Works for any authentication system on the backend, avoids direct password entry on the watch.
    *   *Cons:* Requires user to switch devices, multi-step process, less seamless than Sign in with Apple.
*   **Option 3: Phone-Based Auth Transfer:** Require the user to authenticate on the iPhone app first, which then securely transfers a token to the watch via WatchConnectivity.
    *   *Pros:* Leverages existing phone app authentication.
    *   *Cons:* Violates the "standalone" principle, requires WatchConnectivity, doesn't work if the phone isn't nearby/connected during initial setup.
*   **Option 4: Direct Email/Password:** Ask user to type credentials on the watch.
    *   *Pros:* Conceptually simple.
    *   *Cons:* Terrible UX on the watch keyboard, less secure if tokens aren't handled properly.

## Decision Outcome

**Chosen Option:** Sign in with Apple.

This provides the best combination of security and user experience for a standalone watchOS app. It leverages native platform capabilities and avoids cumbersome input methods. The backend requirement is acceptable, and the account linking for pre-existing users can be handled outside the V1 watch app scope (e.g., via website or main mobile app).

### Negative Consequences

*   Requires backend implementation to validate Apple ID tokens.
*   Users with existing non-Apple ID accounts need a separate process to link them if they choose to use Sign in with Apple on the watch.

## Links

*   `docs/architecture.md`
*   `docs/plan.md`