import XCTest
@testable import DrMuscleWatchApp

class ErrorHandlingServiceTests: XCTestCase {
    
    func testShowError() {
        // Given
        let service = ErrorHandlingService.shared
        let message = "Test error message"
        
        // When
        service.showError(message: message)
        
        // Then
        XCTAssertEqual(service.currentError, message)
        XCTAssertTrue(service.isShowingError)
    }
    
    func testDismissError() {
        // Given
        let service = ErrorHandlingService.shared
        service.showError(message: "Test error message")
        
        // When
        service.dismissError()
        
        // Then
        XCTAssertFalse(service.isShowingError)
        XCTAssertNil(service.currentError)
    }
    
    func testAutoDismissError() {
        // Given
        let service = ErrorHandlingService.shared
        let message = "Test error message"
        let expectation = XCTestExpectation(description: "Error should auto-dismiss")
        
        // When
        service.showError(message: message, autoDismiss: true, dismissAfter: 0.1)
        
        // Then
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            XCTAssertFalse(service.isShowingError)
            XCTAssertNil(service.currentError)
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 0.3)
    }
    
    func testHandleAPIError() {
        // Given
        let service = ErrorHandlingService.shared
        let error = APIError.serverError(statusCode: 500)
        
        // When
        service.handleAPIError(error)
        
        // Then
        XCTAssertEqual(service.currentError, "Server error with status code: 500")
        XCTAssertTrue(service.isShowingError)
    }
    
    func testHandleNetworkError() {
        // Given
        let service = ErrorHandlingService.shared
        let error = NSError(domain: NSURLErrorDomain, code: NSURLErrorNotConnectedToInternet, userInfo: nil)
        
        // When
        service.handleError(error)
        
        // Then
        XCTAssertEqual(service.currentError, "No internet connection. Please check your network settings.")
        XCTAssertTrue(service.isShowingError)
    }
    
    func testHandleGenericError() {
        // Given
        let service = ErrorHandlingService.shared
        let error = NSError(domain: "TestDomain", code: 123, userInfo: [NSLocalizedDescriptionKey: "Test error"])
        
        // When
        service.handleError(error)
        
        // Then
        XCTAssertEqual(service.currentError, "Test error")
        XCTAssertTrue(service.isShowingError)
    }
}
