﻿<?xml version="1.0" encoding="utf-8" ?>
<toolkit:PopupPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
               xmlns:t="clr-namespace:DrMaxMuscle.Layout"
               CloseWhenBackgroundIsClicked="True"
                   BackgroundColor="Transparent"
               xmlns:app="clr-namespace:DrMaxMuscle.Constants"
               xmlns:toolkit="clr-namespace:RGPopup.Maui.Pages;assembly=RGPopup.Maui"
             x:Class="DrMaxMuscle.Views.CustomPromptConfig">
 
    <Border
        x:Name="customPopup"
        Stroke="Transparent"
        HorizontalOptions="FillAndExpand"
        VerticalOptions="Center"
        Margin="{OnPlatform Android='25,0',iOS='35,0'}"
        Style="{StaticResource GradientBorderStyleBlue}"
            Padding="0">

            <Border.StrokeShape>
                <RoundRectangle CornerRadius="14"/>
                </Border.StrokeShape>
        <StackLayout
      HorizontalOptions="FillAndExpand"
      VerticalOptions="Fill"
      Style="{StaticResource GradientStackStyleBlue}"
      Padding="0,20,0,0"
      Margin="0">
            <Label Text="" x:Name="titleLbl" FontSize="18" FontAttributes="Bold" TextColor="White" Margin="20,0,20,10"/>
            <Label Text="" IsVisible="false" x:Name="subtitleLbl" FontSize="15"  TextColor="White" Margin="20,0"/>
            <Border
                HorizontalOptions="FillAndExpand"
                x:Name="entryLblStack"
                IsVisible="false"
                Stroke="Transparent"
                Padding="{OnPlatform Android='8,3',iOS='8,10'}"
                Margin="20,5,20,0"
                BackgroundColor="#567A92">
                <Border.StrokeShape>
                    <RoundRectangle CornerRadius="5"/>
                </Border.StrokeShape>
                <t:DrMuscleEntry x:Name="entryLbl"
                                 TextChanged="entryLbl_TextChanged"
                                 HorizontalOptions="FillAndExpand"
                                 TextColor="White"
                                 PlaceholderColor="#26262B"/>
            </Border>

            <StackLayout
                x:Name="horizontalButtonsView"
                BackgroundColor="Transparent"
                Padding="0,1,0,0"
                Margin="-2,15,-2,-2"
                VerticalOptions="End"
                HorizontalOptions="Fill"
                Orientation="Horizontal"
                Spacing="0">

                <t:DrMuscleButton
                    Text="Cancel"
                    TextTransform="Uppercase"
                    BackgroundColor="Transparent"
                    x:Name="cancelBtn"
                    Padding="0"
                    HeightRequest="60"
                    FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                    FontAttributes="None"
                    BorderColor="Gray"
                    BorderWidth="1"
                    HorizontalOptions="FillAndExpand"
                    LineBreakMode="TailTruncation"
                    Clicked="Cancel_Btn_Clicked"
                    VerticalOptions="CenterAndExpand"
                    TextColor="White"/>

                <t:DrMuscleButton
                    TextTransform="Uppercase"
                    BackgroundColor="Transparent"
                    Text="Ok"
                    Padding="0"
                    x:Name="okBtn"
                    HeightRequest="60"
                    FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                    FontAttributes="Bold"
                    BorderColor="Gray"
                    Margin="-1,0,0,0"
                    BorderWidth="1"
                    HorizontalOptions="FillAndExpand"
                    LineBreakMode="TailTruncation"
                    Clicked="OK_Btn_Clicked"
                    VerticalOptions="CenterAndExpand"
                    TextColor="White"/>
            </StackLayout>
        </StackLayout>
    </Border>
</toolkit:PopupPage>
