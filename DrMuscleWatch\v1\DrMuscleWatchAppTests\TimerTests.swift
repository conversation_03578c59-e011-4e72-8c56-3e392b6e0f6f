import XCTest
@testable import DrMuscleWatchApp

class TimerTests: XCTestCase {
    
    func testTimerButtonInitialState() {
        // Given
        let viewModel = SetViewModel(
            exerciseId: "123",
            workoutId: "456",
            apiClient: MockAPIClient(),
            storageService: MockStorageService()
        )
        
        // When
        let timerButton = TimerButton(
            title: "Save",
            percentage: 0.0,
            showPercentage: true,
            isTimerActive: false,
            secondsRemaining: 60,
            action: {}
        )
        
        // Then
        XCTAssertFalse(timerButton.isTimerActive)
        XCTAssertEqual(timerButton.secondsRemaining, 60)
        XCTAssertEqual(timerButton.title, "Save")
    }
    
    func testTimerButtonTimerActiveState() {
        // Given
        let timerButton = TimerButton(
            title: "Save",
            percentage: 0.0,
            showPercentage: true,
            isTimerActive: true,
            secondsRemaining: 90,
            action: {}
        )
        
        // Then
        XCTAssertTrue(timerButton.isTimerActive)
        XCTAssertEqual(timerButton.secondsRemaining, 90)
        XCTAssertEqual(timerButton.formattedTime, "Rest 1:30")
    }
    
    func testTimerButtonFormatting() {
        // Test various time formats
        
        // 1 minute 30 seconds
        var timerButton = TimerButton(
            title: "Save",
            percentage: 0.0,
            showPercentage: true,
            isTimerActive: true,
            secondsRemaining: 90,
            action: {}
        )
        XCTAssertEqual(timerButton.formattedTime, "Rest 1:30")
        
        // 0 minutes 45 seconds
        timerButton = TimerButton(
            title: "Save",
            percentage: 0.0,
            showPercentage: true,
            isTimerActive: true,
            secondsRemaining: 45,
            action: {}
        )
        XCTAssertEqual(timerButton.formattedTime, "Rest 0:45")
        
        // 2 minutes 5 seconds
        timerButton = TimerButton(
            title: "Save",
            percentage: 0.0,
            showPercentage: true,
            isTimerActive: true,
            secondsRemaining: 125,
            action: {}
        )
        XCTAssertEqual(timerButton.formattedTime, "Rest 2:05")
        
        // 0 seconds (timer complete)
        timerButton = TimerButton(
            title: "Save",
            percentage: 0.0,
            showPercentage: true,
            isTimerActive: true,
            secondsRemaining: 0,
            action: {}
        )
        XCTAssertEqual(timerButton.formattedTime, "Rest 0:00")
    }
    
    func testSetViewModelTimerInitialization() {
        // Given
        let viewModel = SetViewModel(
            exerciseId: "123",
            workoutId: "456",
            apiClient: MockAPIClient(),
            storageService: MockStorageService()
        )
        
        // When - simulate loading a set with rest duration
        let mockSet = SetRecommendationModel(
            reps: 10,
            weight: WeightModel(value: 100, unit: .lbs),
            isWarmup: false,
            isFirstWorkSet: false,
            isLastPlannedSet: false,
            restDurationSeconds: 90
        )
        
        // Then
        XCTAssertEqual(viewModel.restDurationSeconds, 0) // Initial state
        
        // When - update with mock set
        viewModel.updateCurrentSetDetails(mockSet)
        
        // Then
        XCTAssertEqual(viewModel.restDurationSeconds, 90)
    }
    
    func testSetViewTimerActivation() {
        // This would be a UI test, but we can test the logic
        // Given
        let viewModel = SetViewModel(
            exerciseId: "123",
            workoutId: "456",
            apiClient: MockAPIClient(),
            storageService: MockStorageService()
        )
        
        // When - simulate loading a set with rest duration
        let mockSet = SetRecommendationModel(
            reps: 10,
            weight: WeightModel(value: 100, unit: .lbs),
            isWarmup: false,
            isFirstWorkSet: false,
            isLastPlannedSet: false,
            restDurationSeconds: 90
        )
        viewModel.updateCurrentSetDetails(mockSet)
        
        // Then
        XCTAssertEqual(viewModel.restDurationSeconds, 90)
        XCTAssertFalse(viewModel.isTimerActive)
        
        // When - simulate starting timer
        viewModel.startTimer()
        
        // Then
        XCTAssertTrue(viewModel.isTimerActive)
        XCTAssertEqual(viewModel.secondsRemaining, 90)
    }
    
    func testTimerCountdown() {
        // Given
        let viewModel = SetViewModel(
            exerciseId: "123",
            workoutId: "456",
            apiClient: MockAPIClient(),
            storageService: MockStorageService()
        )
        
        // When - simulate loading a set with rest duration and starting timer
        let mockSet = SetRecommendationModel(
            reps: 10,
            weight: WeightModel(value: 100, unit: .lbs),
            isWarmup: false,
            isFirstWorkSet: false,
            isLastPlannedSet: false,
            restDurationSeconds: 90
        )
        viewModel.updateCurrentSetDetails(mockSet)
        viewModel.startTimer()
        
        // Then
        XCTAssertTrue(viewModel.isTimerActive)
        XCTAssertEqual(viewModel.secondsRemaining, 90)
        
        // When - simulate timer tick
        viewModel.timerTick()
        
        // Then
        XCTAssertEqual(viewModel.secondsRemaining, 89)
        
        // When - simulate multiple ticks
        for _ in 0..<10 {
            viewModel.timerTick()
        }
        
        // Then
        XCTAssertEqual(viewModel.secondsRemaining, 79)
    }
    
    func testTimerCompletion() {
        // Given
        let viewModel = SetViewModel(
            exerciseId: "123",
            workoutId: "456",
            apiClient: MockAPIClient(),
            storageService: MockStorageService()
        )
        
        // When - simulate loading a set with rest duration and starting timer
        let mockSet = SetRecommendationModel(
            reps: 10,
            weight: WeightModel(value: 100, unit: .lbs),
            isWarmup: false,
            isFirstWorkSet: false,
            isLastPlannedSet: false,
            restDurationSeconds: 3
        )
        viewModel.updateCurrentSetDetails(mockSet)
        viewModel.startTimer()
        
        // Then
        XCTAssertTrue(viewModel.isTimerActive)
        XCTAssertEqual(viewModel.secondsRemaining, 3)
        
        // When - simulate timer ticks until completion
        viewModel.timerTick() // 2 seconds remaining
        viewModel.timerTick() // 1 second remaining
        viewModel.timerTick() // 0 seconds remaining
        
        // Then
        XCTAssertEqual(viewModel.secondsRemaining, 0)
        XCTAssertFalse(viewModel.isTimerActive) // Timer should stop automatically
    }
    
    func testSkipTimer() {
        // Given
        let viewModel = SetViewModel(
            exerciseId: "123",
            workoutId: "456",
            apiClient: MockAPIClient(),
            storageService: MockStorageService()
        )
        
        // When - simulate loading a set with rest duration and starting timer
        let mockSet = SetRecommendationModel(
            reps: 10,
            weight: WeightModel(value: 100, unit: .lbs),
            isWarmup: false,
            isFirstWorkSet: false,
            isLastPlannedSet: false,
            restDurationSeconds: 90
        )
        viewModel.updateCurrentSetDetails(mockSet)
        viewModel.startTimer()
        
        // Then
        XCTAssertTrue(viewModel.isTimerActive)
        XCTAssertEqual(viewModel.secondsRemaining, 90)
        
        // When - simulate skipping timer
        viewModel.skipTimer()
        
        // Then
        XCTAssertFalse(viewModel.isTimerActive)
    }
}

// Mock classes for testing
class MockAPIClient: APIClientProtocol {
    func fetchWorkouts() async throws -> [WorkoutModel] {
        return []
    }
    
    func fetchWorkoutDetails(workoutId: String) async throws -> WorkoutDetailModel {
        return WorkoutDetailModel(id: "123", name: "Test Workout", exercises: [])
    }
    
    func fetchExerciseDetails(exerciseId: String) async throws -> ExerciseDetailModel {
        return ExerciseDetailModel(id: "123", name: "Test Exercise", sets: [])
    }
}

class MockStorageService: StorageServiceProtocol {
    func saveWorkout(workout: WorkoutDetailModel) throws -> String {
        return "123"
    }
    
    func saveExercise(exercise: ExerciseDetailModel, workoutID: String) throws -> String {
        return "456"
    }
    
    func saveSetLog(exerciseID: String, workoutID: String, reps: Int16, weight: Double, weightUnit: String, rir: Int16?, timestamp: Date, isWarmup: Bool, needsSync: Bool) throws -> String {
        return "789"
    }
    
    func fetchWorkout(id: String) throws -> WorkoutDetailModel? {
        return nil
    }
    
    func fetchExercise(id: String) throws -> ExerciseDetailModel? {
        return nil
    }
    
    func fetchSetLogs(exerciseID: String) throws -> [SetLogModel] {
        return []
    }
    
    func fetchSetLogsNeedingSync() throws -> [SetLogModel] {
        return []
    }
    
    func markSetLogAsSynced(id: String) throws {
        // No-op for mock
    }
    
    func deleteWorkout(id: String) throws {
        // No-op for mock
    }
}
