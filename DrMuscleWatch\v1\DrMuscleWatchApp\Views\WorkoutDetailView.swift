import SwiftUI

struct WorkoutDetailView: View {
    @ObservedObject var viewModel: WorkoutDetailViewModel
    @State private var isStartingWorkout = false
    @State private var navigateToSetView = false

    var body: some View {
        content
            .navigationTitle("Workout Details")
            .task {
                await viewModel.fetchWorkoutDetails()
            }
    }

    @ViewBuilder
    private var content: some View {
        switch viewModel.state {
        case .loading:
            loadingView
        case .loaded:
            workoutDetailView
        case .error(let message):
            errorView(message: message)
        }
    }

    private var loadingView: some View {
        ProgressView("Loading workout details...")
            .progressViewStyle(CircularProgressViewStyle(tint: AppTheme.primaryTextColor))
    }

    private var workoutDetailView: some View {
        VStack(alignment: .leading, spacing: 16) {
            if let workout = viewModel.workout {
                // Workout header
                workoutHeader(workout: workout)

                // Exercise list
                exerciseList(exercises: workout.exercises)

                // Start workout button
                startWorkoutButton()
            }
        }
        .padding()
        .background(AppTheme.backgroundColor)
        .navigationDestination(isPresented: $navigateToSetView) {
            if let workout = viewModel.workout, !workout.exercises.isEmpty {
                SetView(viewModel: SetViewModel(exerciseId: workout.exercises[0].id, workoutId: workout.id))
            }
        }
    }

    private func workoutHeader(workout: WorkoutTemplateModel) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(workout.name)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(AppTheme.primaryTextColor)

            Text("\(workout.exercises.count) exercises · \(totalSets(in: workout)) total sets")
                .font(.subheadline)
                .foregroundColor(AppTheme.secondaryTextColor)
        }
        .padding(.bottom, 8)
    }

    private func exerciseList(exercises: [ExerciseModel]) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Exercises:")
                .font(.headline)
                .foregroundColor(AppTheme.primaryTextColor)

            ScrollView {
                LazyVStack(alignment: .leading, spacing: 12) {
                    ForEach(exercises, id: \.id) { exercise in
                        exerciseRow(exercise: exercise)
                    }
                }
            }
        }
    }

    private func exerciseRow(exercise: ExerciseModel) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(exercise.name)
                    .font(.body)
                    .foregroundColor(AppTheme.primaryTextColor)

                HStack(spacing: 4) {
                    let warmupSets = exercise.sets.filter { $0.isWarmup }.count
                    let workSets = exercise.sets.filter { !$0.isWarmup }.count

                    if warmupSets > 0 {
                        Text("\(warmupSets) warm-up")
                            .font(.caption)
                            .foregroundColor(AppTheme.secondaryTextColor)
                    }

                    if warmupSets > 0 && workSets > 0 {
                        Text("•")
                            .font(.caption)
                            .foregroundColor(AppTheme.secondaryTextColor)
                    }

                    if workSets > 0 {
                        Text("\(workSets) work sets")
                            .font(.caption)
                            .foregroundColor(AppTheme.secondaryTextColor)
                    }
                }
            }

            Spacer()

            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(AppTheme.secondaryTextColor)
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }

    private func startWorkoutButton() -> some View {
        Button(action: {
            isStartingWorkout = true
            Task {
                let success = await viewModel.startWorkout()
                isStartingWorkout = false
                if success {
                    navigateToSetView = true
                }
            }
        }) {
            if isStartingWorkout {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
            } else {
                Text("Start Workout")
                    .font(.headline)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
            }
        }
        .buttonStyle(AppTheme.primaryButtonStyle())
        .disabled(isStartingWorkout)
        .padding(.top, 16)
        .frame(maxWidth: .infinity)
    }

    // Helper function to calculate total sets in a workout
    private func totalSets(in workout: WorkoutTemplateModel) -> Int {
        workout.exercises.reduce(0) { $0 + $1.sets.count }
    }

    private func errorView(message: String) -> some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle")
                .font(.largeTitle)
                .foregroundColor(.red)

            Text(message)
                .font(.headline)
                .multilineTextAlignment(.center)
                .foregroundColor(AppTheme.primaryTextColor)

            Button(action: {
                Task {
                    await viewModel.fetchWorkoutDetails()
                }
            }) {
                Text("Retry")
                    .font(.headline)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
            }
            .buttonStyle(AppTheme.secondaryButtonStyle())
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(AppTheme.backgroundColor)
    }
}

#Preview {
    NavigationView {
        WorkoutDetailView(viewModel: WorkoutDetailViewModel(workoutId: 1))
    }
}
