# Dr. Muscle Watch App - Core Tasks (V1)

## Purpose

This file lists the core infrastructure development tasks for the V1 Dr. Muscle Apple Watch app, including setup, authentication, API client basics, and local storage.

## Instructions

*   Mark tasks as "Pending", "In Progress", or "Completed".
*   Reference related ADRs or specific requirements where applicable.
*   Follow the TDD cycle (Red, Green, Refactor, Documentation) for each scenario within a task, as defined in `docs/rules.md`.
*   For API-related tasks, refer to `docs/api-reference.md` for detailed endpoint specifications, data models, and implementation patterns.
*   For UI-related tasks, refer to `docs/ui-components.md` for detailed component documentation, styling guidelines, and implementation patterns. The actual UI components can be found in `v1/DrMuscleWatchApp/Components/` and views in `v1/DrMuscleWatchApp/Views/`.

## Core Tasks

*   **Task ID:** SETUP-01
*   **Status:** Completed
*   **Task:** Initialize project structure, dependencies (SwiftUI App lifecycle), and basic navigation structure.
    *   **Acceptance Criteria:**
        *   **Scenario:** Project Compiles
            *   Given a new watchOS project is created using Swift and SwiftUI
            *   When the project is built
            *   Then the build succeeds without errors.
        *   **Scenario:** Initial View Structure
            *   Given the project structure is initialized
            *   When the app launches
            *   Then a basic ContentView is displayed (which will later contain the login or workout view).
    *   **Completion Date:** May 1, 2024
    *   **Implementation Notes:** Created basic project structure with SwiftUI App lifecycle and ContentView. Project files are ready to be imported into Xcode on the Mac for building and testing.

*   **Task ID:** AUTH-01
*   **Status:** Completed
*   **Task:** Implement "Sign in with Apple" flow (UI, AuthenticationServices interaction, token handling).
    *   **Acceptance Criteria:**
        *   **Scenario:** Display Sign In Button
            *   Given the user is not authenticated
            *   When the app launches and presents the initial view
            *   Then the "Sign in with Apple" button is displayed.
        *   **Scenario:** Initiate Sign In
            *   Given the "Sign in with Apple" button is displayed
            *   When the user taps the "Sign in with Apple" button
            *   Then the native Apple Sign In sheet is presented.
        *   **Scenario:** Successful Sign In (New User)
            *   Given the native Apple Sign In sheet is presented
            *   When the user successfully authenticates with their Apple ID
            *   And the app receives a valid identity token and user identifier from Apple
            *   And the backend confirms this Apple ID is not linked to an existing Dr. Muscle account
            *   Then the app securely stores the authentication state (e.g., token/user ID).
            *   And the app transitions to the main authenticated view (Workout List).
        *   **Scenario:** Successful Sign In (Existing Linked User)
            *   Given the native Apple Sign In sheet is presented
            *   When the user successfully authenticates with their Apple ID
            *   And the app receives a valid identity token and user identifier from Apple
            *   And the backend confirms this Apple ID *is* linked to an existing Dr. Muscle account
            *   Then the app securely stores the authentication state.
            *   And the app transitions to the main authenticated view (Workout List).
        *   **Scenario:** User Cancels Sign In
            *   Given the native Apple Sign In sheet is presented
            *   When the user cancels the sign-in process
            *   Then the sheet is dismissed.
            *   And the user remains on the initial view with the "Sign in with Apple" button visible.
            *   And no authentication state is stored.
        *   **Scenario:** Sign In Fails (Apple Error)
            *   Given the native Apple Sign In sheet is presented
            *   When the sign-in process fails due to an error from Apple's services (e.g., network issue, invalid credentials)
            *   Then the sheet is dismissed.
            *   And an appropriate error message is displayed to the user (e.g., "Sign in failed. Please try again.").
            *   And the user remains on the initial view with the "Sign in with Apple" button visible.
        *   **Scenario:** Sign In Fails (Backend Error)
            *   Given the user successfully authenticates with Apple
            *   And the app sends the token to the backend for validation/linking
            *   When the backend returns an error (e.g., server unavailable, validation failed)
            *   Then an appropriate error message is displayed to the user (e.g., "Could not connect to Dr. Muscle. Please try again.").
            *   And the user remains on the initial view with the "Sign in with Apple" button visible.
            *   And no authentication state is stored.
        *   **Scenario:** App Launch When Already Authenticated
            *   Given the user has previously authenticated successfully using "Sign in with Apple"
            *   And the authentication state is securely stored
            *   When the user launches the app
            *   Then the app verifies the stored authentication state (potentially checking token validity if applicable)
            *   And the app bypasses the login screen.
            *   And the app transitions directly to the main authenticated view (Workout List).
    *   **Depends On:** SETUP-01
    *   **ADR:** `docs/adr/0003-sign-in-with-apple-authentication.md`
    *   **Completion Date:** May 4, 2024
    *   **Implementation Notes:** Implemented AuthenticationManager to handle Sign in with Apple authentication, created LoginViewModel to manage the login flow, implemented LoginView with custom Sign in with Apple button, updated ContentView to show LoginView when not authenticated and HomeView when authenticated, and added environment objects for authentication state management. Created comprehensive documentation in AuthenticationFlow.md.

*   **Task ID:** API-01
*   **Status:** Completed
*   **Task:** Implement basic API Client structure and Authentication header injection.
    *   **Acceptance Criteria:**
        *   **Scenario:** API Client Initialization
            *   Given the application needs to communicate with the backend
            *   When the API Client service is initialized
            *   Then it is configured with the correct base URL for the Dr. Muscle API.
        *   **Scenario:** Injecting Auth Token into Requests
            *   Given the user is authenticated
            *   And the app has a valid authentication token
            *   When the API Client prepares to send a request to a protected endpoint
            *   Then the request includes the necessary Authorization header (e.g., `Authorization: Bearer <token>`).
        *   **Scenario:** Making a Request Without Auth Token
            *   Given the user is not authenticated
            *   When the API Client prepares to send a request to a protected endpoint
            *   Then the request does not include an Authorization header.
            *   *(Note: The API should reject this request, but the client's responsibility here is just not sending the header)*.
        *   **Scenario:** Basic GET Request Structure
            *   Given the API Client is initialized
            *   When a function is called to perform a GET request to a specific endpoint (e.g., `/workouts/today`)
            *   Then the client constructs and executes a URLRequest with the correct HTTP method (GET) and URL.
        *   **Scenario:** Basic POST Request Structure
            *   Given the API Client is initialized
            *   And data needs to be sent to the backend (e.g., saving a set)
            *   When a function is called to perform a POST request with specific data payload
            *   Then the client constructs and executes a URLRequest with the correct HTTP method (POST), URL, `Content-Type: application/json` header, and the data encoded as JSON in the request body.
    *   **Completion Date:** May 2, 2024
    *   **Implementation Notes:** Created the DrMuscleAPIClient class with proper authentication token handling using the Keychain, implemented API endpoint methods for all required functionality, and added tests for the client initialization and token handling.
    *   **Reference:** See `docs/api-reference.md` for detailed API client structure, authentication token storage, and endpoint specifications.

*   **Task ID:** STORAGE-01
*   **Status:** Completed
*   **Task:** Set up Core Data stack for local storage.
    *   **Acceptance Criteria:**
        *   **Scenario:** Core Data Model Initialization
            *   Given the application needs local persistence
            *   When the Core Data stack is initialized
            *   Then a data model (`.xcdatamodeld`) exists defining entities for Workout, Exercise, SetLog (including attributes for reps, weight, rir, timestamp, exerciseID, workoutID, needsSync flag, etc.).
            *   And the persistent container (`NSPersistentContainer`) loads successfully without errors.
        *   **Scenario:** Saving Context
            *   Given the Core Data stack is initialized
            *   And changes have been made to the managed object context (e.g., a new SetLog entity created)
            *   When the save context operation is called
            *   Then the changes are persisted to the store without errors.
        *   **Scenario:** Fetching Data
            *   Given data has been saved to the Core Data store (e.g., a SetLog)
            *   When a fetch request is executed for that data type with appropriate predicates (e.g., fetch SetLog for workoutID X)
            *   Then the fetch request executes successfully.
            *   And the expected managed objects are returned.
    *   **Depends On:** SETUP-01
    *   **Completion Date:** May 3, 2024
    *   **Implementation Notes:** Created Core Data model with entities for Workout, Exercise, and SetLog. Implemented PersistenceController to manage the Core Data stack and StorageService to provide a higher-level API for working with Core Data. Added tests for Core Data model initialization and basic CRUD operations. Created documentation for the Core Data models in CoreDataModels.md.