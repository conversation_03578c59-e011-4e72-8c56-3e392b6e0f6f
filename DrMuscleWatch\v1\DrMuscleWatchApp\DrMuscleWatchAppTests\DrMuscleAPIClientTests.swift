import XCTest
@testable import <PERSON><PERSON>uscle<PERSON>atchApp

final class DrMuscleAPIClientTests: XCTestCase {
    
    // Test that the API client initializes correctly
    func testAPIClientInitialization() {
        // Given the application needs to communicate with the backend
        let apiClient = DrMuscleAPIClient.shared
        
        // When the API Client service is initialized
        // (This happens automatically with the shared instance)
        
        // Then it is configured with the correct base URL for the Dr. Muscle API
        // (We can't directly test the private baseURL property, but we can verify the client exists)
        XCTAssertNotNil(apiClient)
    }
    
    // Test token storage and retrieval
    func testTokenStorageAndRetrieval() {
        // Given the user is authenticated
        let apiClient = DrMuscleAPIClient.shared
        let testToken = "test_token_\(UUID().uuidString)"
        
        // When the token is stored
        apiClient.storeToken(testToken)
        
        // Then the token should be stored in the keychain
        // (We can't directly test the private token property, but we can clear it and verify behavior)
        
        // Clear the token
        apiClient.clearToken()
        
        // Verify the token was cleared (indirectly by checking that no error is thrown)
        XCTAssertNoThrow(apiClient.clearToken())
    }
    
    // Note: We would typically add more tests for the actual API calls,
    // but those would require mocking the URLSession which is beyond the scope
    // of this initial implementation. In a real-world scenario, we would use
    // a protocol-based approach to make the URLSession injectable for testing.
}
