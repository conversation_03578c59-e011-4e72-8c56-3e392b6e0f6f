import Foundation

/// Model for a group of workout templates
struct WorkoutTemplateGroupModel: Codable, Identifiable {
    let id: Int64
    let userId: String?
    let label: String
    let workoutTemplates: [WorkoutTemplateModel]?
    let isSystemExercise: Bool
    let requiredWorkoutToLevelUp: Int
    let level: Int?
    let remainingToLevelUp: Int?
    let nextProgramId: Int?
    let programId: Int64
    
    enum CodingKeys: String, CodingKey {
        case id = "Id"
        case userId = "UserId"
        case label = "Label"
        case workoutTemplates = "WorkoutTemplates"
        case isSystemExercise = "IsSystemExercise"
        case requiredWorkoutToLevelUp = "RequiredWorkoutToLevelUp"
        case level = "Level"
        case remainingToLevelUp = "RemainingToLevelUp"
        case nextProgramId = "NextProgramId"
        case programId = "ProgramId"
    }
}
