import SwiftUI

/// A picker for Reps In Reserve (RIR) with descriptive options
struct RIRPicker: View {
    @Binding var selectedRIR: Int
    let onConfirm: (Int) -> Void
    @Environment(\.dismiss) private var dismiss

    // RIR options with descriptive text
    private let rirOptions: [(text: String, value: Int)] = [
        ("Very hard (0 left)", 0),
        ("Could do 1-2 more", 1),
        ("Could do 3-4 more", 3),
        ("Could do 5-6 more", 5),
        ("Could do 7+ more", 7)
    ]

    var body: some View {
        VStack(spacing: 20) {
            Text("How hard was this set?")
                .font(AppTheme.titleStyle)
                .foregroundColor(AppTheme.primaryTextColor)
                .multilineTextAlignment(.center)
                .padding(.top, 20)

            ScrollView {
                VStack(spacing: 12) {
                    ForEach(rirOptions, id: \.value) { option in
                        Button(action: {
                            selectedRIR = option.value
                            onConfirm(option.value)
                        }) {
                            Text(option.text)
                                .font(.system(size: 18, weight: .medium))
                                .foregroundColor(AppTheme.primaryTextColor)
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 16)
                                .background(
                                    RoundedRectangle(cornerRadius: 10)
                                        .fill(Color.gray.opacity(0.2))
                                )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(.horizontal, 16)
            }

            Button("Cancel") {
                dismiss()
            }
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(Color.gray)
            .padding(.bottom, 20)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(AppTheme.backgroundColor)
    }
}
