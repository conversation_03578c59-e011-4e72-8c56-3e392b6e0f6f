# ADR-0006: RIR Capture After First Work Set via Picker

*   **Status:** Accepted
*   **Date:** {{CURRENT_DATE}}
*   **Deciders:** Project Lead
*   **Consulted:** AI Assistant
*   **Informed:** Development Team

## Context and Problem Statement

Capturing Reps In Reserve (RIR) is a core mechanic of the Dr. Muscle methodology. The watch app needs to prompt the user for RIR at the appropriate time and using a clear input method. Based on requirements, RIR should only be captured after the *first work set* of each exercise.

## Decision Drivers

*   **Core Functionality:** RIR is essential for <PERSON><PERSON> Mu<PERSON>cle's logic.
*   **Timing:** Prompt must occur at the correct point in the workout flow (only after first work set).
*   **Clarity:** Input method must be unambiguous on the watch.
*   **Usability:** Minimize disruption to the workout flow.

## Considered Options

*   **Option 1: Prompt via Picker After First Work Set:** After saving the first work set, present a full-screen modal picker with descriptive RIR options (e.g., "Very hard", "1-2 more"). Capture selection before proceeding to rest/next step.
    *   *Pros:* Clear prompt, unambiguous input via descriptive text, occurs at the specified time, aligns with alpha implementation pattern.
    *   *Cons:* Requires API to clearly identify the "first work set", adds a mandatory step after that specific set.
*   **Option 2: Prompt After Every Set:** Prompt for RIR after every single set (warm-up and work sets).
    *   *Pros:* Simpler conditional logic (always show).
    *   *Cons:* Violates the requirement to only ask after the first work set, adds unnecessary friction after warm-ups and subsequent work sets.
*   **Option 3: Optional RIR Button:** Provide a button somewhere on the screen to optionally log RIR.
    *   *Pros:* Less intrusive.
    *   *Cons:* Users might forget, RIR data might be missing, doesn't enforce capture at the desired point.
*   **Option 4: Numeric RIR Input:** Use a number picker (0-5) instead of descriptive text.
    *   *Pros:* Potentially faster input for experienced users.
    *   *Cons:* Less clear meaning for novice users compared to descriptive text, less aligned with alpha pattern.

## Decision Outcome

**Chosen Option:** Prompt via Picker After First Work Set (Option 1).

This directly implements the requirement to capture RIR only after the first work set. Using a descriptive picker (e.g., "Very hard", "1-2 more", "3-4 more", "5-6 more", "7+ more" - based on alpha `InterfaceController.cs`) provides the clearest input method for the user on the watch. This requires a dependency on the API providing accurate flags to identify the first work set.

### Negative Consequences

*   Adds an explicit step/screen after the first work set, slightly interrupting the flow compared to other sets.
*   Critically dependent on the API correctly identifying the `isFirstWorkSet`. If the API flag is missing or incorrect, this core feature will fail.

## Links

*   `docs/plan.md`
*   `docs/architecture.md` (API Requirements section)
*   Reference: Alpha `InterfaceController.cs` RIR picker items.