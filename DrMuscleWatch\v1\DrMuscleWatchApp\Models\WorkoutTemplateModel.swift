import Foundation

/// Model for a workout template
struct WorkoutTemplateModel: Codable, Identifiable {
    let id: Int64
    let userId: String?
    let label: String
    let exercises: [ExerciseModel]?
    let isSystemExercise: Bool
    
    enum CodingKeys: String, CodingKey {
        case id = "Id"
        case userId = "UserId"
        case label = "Label"
        case exercises = "Exercises"
        case isSystemExercise = "IsSystemExercise"
    }
}

/// Model for an exercise within a workout
struct ExerciseModel: Codable, Identifiable {
    let id: Int64
    let label: String
    let bodyPartId: Int64?
    let isBodyweight: Bool
    let isWeighted: Bool
    let isAssisted: Bool
    let isTimeBased: Bool
    let isUnilateral: Bool
    
    enum CodingKeys: String, CodingKey {
        case id = "Id"
        case label = "Label"
        case bodyPartId = "BodyPartId"
        case isBodyweight = "IsBodyweight"
        case isWeighted = "IsWeighted"
        case isAssisted = "IsAssisted"
        case isTimeBased = "IsTimeBased"
        case isUnilateral = "IsUnilateral"
    }
}
