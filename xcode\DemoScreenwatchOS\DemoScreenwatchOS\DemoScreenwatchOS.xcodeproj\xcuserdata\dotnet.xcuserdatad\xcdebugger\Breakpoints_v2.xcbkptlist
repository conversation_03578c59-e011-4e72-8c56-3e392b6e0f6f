<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "1A918BAA-7E0D-4C94-8C26-2ED4253D11F2"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "72AB4094-BBF2-40EE-8079-03933E0B6A10"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "DemoScreenwatchOS Watch App/DatabaseManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "61"
            endingLineNumber = "61"
            landmarkName = "saveExerciseSet(repsValue:weightValue:unitValue:nameValue:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "20FC1574-B996-4DFD-809C-7E9A6777F26A"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "DemoScreenwatchOS Watch App/DatabaseManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "74"
            endingLineNumber = "74"
            landmarkName = "fetchExerciseSets()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C1CB4B2D-4F14-465C-8441-B0BD37A07CFD"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "DemoScreenwatchOS Watch App/RMCalculation.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "57"
            endingLineNumber = "57"
            landmarkName = "calculateSetTitle(models:currentReps:currentWeight:lastReps:lastWeight:isKg:userBodyWeight:isWeighted:isBodyweight:isAssisted:)"
            landmarkType = "9">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "78AC2062-BA87-4542-A793-E576F1131557"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "DemoScreenwatchOS Watch App/RMCalculation.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "29"
            endingLineNumber = "29"
            landmarkName = "calculateSetTitle(models:currentReps:currentWeight:lastReps:lastWeight:isKg:userBodyWeight:isWeighted:isBodyweight:isAssisted:)"
            landmarkType = "9">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B5FC3E66-**************-97833771385C"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "DemoScreenwatchOS Watch App/ContentView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "141"
            endingLineNumber = "141"
            landmarkName = "body"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "531E69A4-71BF-4EED-B42C-1A14C86B56CB"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "DemoScreenwatchOS Watch App/ContentView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "152"
            endingLineNumber = "152"
            landmarkName = "body"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "55C8C4F8-79A1-48F5-9FAC-0BFC3404EC98"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "DemoScreenwatchOS Watch App/ContentView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "154"
            endingLineNumber = "154"
            landmarkName = "body"
            landmarkType = "24">
            <Locations>
               <Location
                  uuid = "55C8C4F8-79A1-48F5-9FAC-0BFC3404EC98 - 71f50e1f816b381c"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "DemoScreenwatchOS_Watch_App.ContentView.body.getter : some"
                  moduleName = "DemoScreenwatchOS Watch App"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/DemoScreenwatchOS/DemoScreenwatchOS%20Watch%20App/ContentView.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "154"
                  endingLineNumber = "154"
                  offsetFromSymbolStart = "2187">
               </Location>
               <Location
                  uuid = "55C8C4F8-79A1-48F5-9FAC-0BFC3404EC98 - 847f5a57ddb9ab9a"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #4 (Swift.Int) -&gt; () in DemoScreenwatchOS_Watch_App.ContentView.body.getter : some"
                  moduleName = "DemoScreenwatchOS Watch App"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/DemoScreenwatchOS/DemoScreenwatchOS%20Watch%20App/ContentView.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "154"
                  endingLineNumber = "154"
                  offsetFromSymbolStart = "43">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
