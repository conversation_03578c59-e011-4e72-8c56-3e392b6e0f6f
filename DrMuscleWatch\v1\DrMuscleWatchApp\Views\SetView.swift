import SwiftUI

/// The main Set screen view that displays exercise details and allows logging sets
struct SetView: View {
    // MARK: - Properties

    @ObservedObject var viewModel: SetViewModel
    @State private var showRIRPicker: Bool = false
    @State private var selectedRIR: Int = 0
    @State private var isRepsPickerPresented: Bool = false
    @State private var isWeightPickerPresented: Bool = false

    // MARK: - Initialization

    init(viewModel: SetViewModel) {
        self.viewModel = viewModel
    }

    // MARK: - Body

    var body: some View {
        content
            .task {
                await viewModel.fetchExerciseDetails()
            }
    }

    @ViewBuilder
    private var content: some View {
        switch viewModel.state {
        case .loading:
            loadingView
        case .loaded:
            exerciseView
        case .error(let message):
            errorView(message: message)
        }
    }

    private var loadingView: some View {
        ProgressView("Loading exercise details...")
            .progressViewStyle(CircularProgressViewStyle(tint: AppTheme.primaryTextColor))
            .background(AppTheme.backgroundColor)
            .edgesIgnoringSafeArea(.all)
    }

    private var exerciseView: some View {
        ZStack {
            // Main content
            VStack(spacing: 8) {
                // Exercise header with set information
                exerciseHeaderView

                Spacer(minLength: 3)

                // Show different content based on whether we're showing the choice screen
                if viewModel.shouldShowChoiceScreen() {
                    // Choice screen content
                    choiceScreenContent
                } else {
                    // Normal set screen content
                    weightAndRepsSection
                }

                Spacer()

                // Save button or Next Exercise button
                if viewModel.shouldShowChoiceScreen() {
                    nextExerciseButton
                } else {
                    saveButton
                }

                Spacer()
            }
            .padding()

            // RIR Picker overlay
            if showRIRPicker {
                RIRPicker(selectedRIR: $selectedRIR) { rir in
                    showRIRPicker = false
                    saveSetWithRIR(rir)
                }
            }

            // Checkmark animation overlay
            if viewModel.shouldShowCheckmarkAnimation() {
                CheckmarkView {
                    viewModel.onCheckmarkAnimationComplete()
                }
            }
        }
        .background(AppTheme.backgroundColor)
        .edgesIgnoringSafeArea(.all)
        .sheet(isPresented: $isRepsPickerPresented, onDismiss: {
            // No action needed on dismiss - the binding will update the value if changed
            // If dismissed without selection, the original value is preserved
        }) {
            CustomPicker(
                title: "Select Reps",
                range: 1...100,
                selectedValue: Binding(
                    get: { Int(viewModel.currentReps) },
                    set: { viewModel.updateReps($0) }
                )
            )
        }
        .sheet(isPresented: $isWeightPickerPresented, onDismiss: {
            // No action needed on dismiss - the binding will update the value if changed
            // If dismissed without selection, the original value is preserved
        }) {
            CustomPicker(
                title: "Select Weight",
                range: 0...500,
                selectedValue: Binding(
                    get: { Int(viewModel.currentWeight) },
                    set: { viewModel.updateWeight(Double($0)) }
                )
            )
        }
    }

    private func errorView(message: String) -> some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle")
                .font(.largeTitle)
                .foregroundColor(.red)

            Text(message)
                .font(.headline)
                .multilineTextAlignment(.center)
                .foregroundColor(AppTheme.primaryTextColor)

            Button(action: {
                Task {
                    await viewModel.fetchExerciseDetails()
                }
            }) {
                Text("Retry")
                    .font(.headline)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
            }
            .buttonStyle(AppTheme.secondaryButtonStyle())
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(AppTheme.backgroundColor)
        .edgesIgnoringSafeArea(.all)
    }

    // MARK: - Helper Methods

    /// Save the current set and start timer
    private func saveSet() {
        // Show RIR picker only if this is the first work set (not a warmup set)
        if viewModel.shouldShowRIRPicker() {
            showRIRPicker = true
        } else {
            saveSetWithoutRIR()
        }
    }

    /// Save the set without RIR and start the timer
    private func saveSetWithoutRIR() {
        Task {
            let success = await viewModel.saveCurrentSet()
            if success {
                print("Set saved successfully: \(viewModel.currentReps) reps at \(viewModel.currentWeight) \(viewModel.weightUnit)")
                // Start timer after successful save
                viewModel.startTimer()
            } else {
                print("Failed to save set")
                // Handle error (could show an alert or other UI feedback)
            }
        }
    }

    /// Save the set with the selected RIR value and start the timer
    private func saveSetWithRIR(_ rir: Int) {
        Task {
            let success = await viewModel.saveCurrentSet(rir: rir)
            if success {
                print("Set saved successfully with RIR: \(rir)")
                // Start timer after successful save
                viewModel.startTimer()
            } else {
                print("Failed to save set with RIR")
                // Handle error (could show an alert or other UI feedback)
            }
        }
    }

    // MARK: - UI Components

    private var exerciseHeaderView: some View {
        VStack(spacing: 4) {
            ExerciseHeader(exerciseName: viewModel.exerciseName)

            HStack(spacing: 8) {
                // Set type indicator (Warmup or Work Set)
                Text(viewModel.isWarmupSet ? "WARM-UP" : "WORK SET")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(viewModel.isWarmupSet ? .orange : .green)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 4)
                            .fill(viewModel.isWarmupSet ? Color.orange.opacity(0.2) : Color.green.opacity(0.2))
                    )

                // Set counter
                Text("SET \(viewModel.currentSetNumber) OF \(viewModel.totalSets)")
                    .font(.caption)
                    .foregroundColor(AppTheme.secondaryTextColor)
            }
        }
    }

    private var weightAndRepsSection: some View {
        HStack {
            // Reps display
            ValueDisplay(
                value: "\(viewModel.currentReps)",
                label: "REPS"
            ) {
                isRepsPickerPresented.toggle()
            }

            Spacer()

            // Weight display
            ValueDisplay(
                value: "\(Int(viewModel.currentWeight))",
                label: viewModel.weightUnit.uppercased()
            ) {
                isWeightPickerPresented.toggle()
            }
        }
        .padding(36)
    }

    private var saveButton: some View {
        TimerButton(
            title: "Save",
            percentage: viewModel.getPerformancePercentage(),
            showPercentage: true,
            isTimerActive: $viewModel.isTimerActive,
            secondsRemaining: $viewModel.secondsRemaining
        ) {
            if viewModel.isTimerActive {
                // If timer is active, tapping the button skips the timer
                viewModel.skipTimer()
            } else {
                // If timer is not active, tapping the button saves the set
                saveSet()
            }
        }
        .padding(.horizontal)
    }

    private var nextExerciseButton: some View {
        VStack {
            TimerButton(
                title: "Next Exercise",
                percentage: 0.0,
                showPercentage: false,
                isTimerActive: $viewModel.isTimerActive,
                secondsRemaining: $viewModel.secondsRemaining
            ) {
                if viewModel.isTimerActive {
                    // If timer is active, tapping the button skips the timer
                    viewModel.skipTimer()
                } else {
                    // If timer is not active, tapping the button moves to the next exercise
                    _ = viewModel.nextExercise()
                    // The checkmark animation will be shown, and when it completes,
                    // the onCheckmarkAnimationComplete method will be called,
                    // which will set shouldNavigateToNextExercise to true
                }
            }
            .padding(.horizontal)

            // Add navigation links for next exercise and workout complete
            Group {
                // Navigation link for next exercise
                NavigationLink(
                    destination:
                        // In a real implementation, this would navigate to the next exercise's SetView
                        // with the appropriate exerciseId and workoutId
                        Text("Next Exercise View")
                            .navigationTitle("Next Exercise"),
                    isActive: Binding(
                        get: { viewModel.shouldNavigateToNextExercise },
                        set: { newValue in
                            // If navigation is cancelled, reset the flag
                            if !newValue {
                                viewModel.shouldNavigateToNextExercise = false
                            }
                        }
                    ),
                    label: { EmptyView() }
                )
                .hidden() // Hide the navigation link, we're just using it for navigation

                // Navigation link for workout complete
                NavigationLink(
                    destination:
                        WorkoutCompleteView(viewModel: WorkoutCompleteViewModel(
                            workoutId: viewModel.workoutId,
                            workoutName: viewModel.workoutName
                        )),
                    isActive: Binding(
                        get: { viewModel.shouldNavigateToWorkoutComplete },
                        set: { newValue in
                            // If navigation is cancelled, reset the flag
                            if !newValue {
                                viewModel.shouldNavigateToWorkoutComplete = false
                            }
                        }
                    ),
                    label: { EmptyView() }
                )
                .hidden() // Hide the navigation link, we're just using it for navigation
            }
        }
    }

    private var choiceScreenContent: some View {
        VStack(spacing: 20) {
            // Add Set button
            Button(action: {
                viewModel.addSet()
            }) {
                Text("Add Set")
                    .font(.headline)
                    .foregroundColor(AppTheme.primaryTextColor)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: AppTheme.cornerRadius)
                            .fill(Color.gray.opacity(0.2))
                    )
            }
            .buttonStyle(PlainButtonStyle())

            Text("Tap 'Add Set' to add another set of this exercise, or wait for the timer to proceed to the next exercise.")
                .font(.caption)
                .foregroundColor(AppTheme.secondaryTextColor)
                .multilineTextAlignment(.center)
        }
    }
}
