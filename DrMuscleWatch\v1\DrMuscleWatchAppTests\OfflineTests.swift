import XCTest
@testable import DrMuscleWatchApp

class OfflineTests: XCTestCase {

    // Mock classes for testing
    class MockAPIClient: DrMuscleAPIClient {
        var shouldFailWithNetworkError = false
        var mockWorkoutTemplate: WorkoutTemplateModel?
        var mockRecommendation: RecommendationModel?

        override func getUserWorkout(workoutId: Int) async throws -> WorkoutTemplateModel {
            if shouldFailWithNetworkError {
                throw NSError(domain: "NetworkError", code: -1009, userInfo: [NSLocalizedDescriptionKey: "The Internet connection appears to be offline."])
            }

            guard let mockWorkout = mockWorkoutTemplate else {
                throw NSError(domain: "TestError", code: 404, userInfo: [NSLocalizedDescriptionKey: "Mock workout not found"])
            }

            return mockWorkout
        }

        override func getRecommendationForExercise(model: RecommendationRequestModel) async throws -> RecommendationModel {
            if shouldFailWithNetworkError {
                throw NSError(domain: "NetworkError", code: -1009, userInfo: [NSLocalizedDescriptionKey: "The Internet connection appears to be offline."])
            }

            guard let mockRec = mockRecommendation else {
                throw NSError(domain: "TestError", code: 404, userInfo: [NSLocalizedDescriptionKey: "Mock recommendation not found"])
            }

            return mockRec
        }
    }

    class MockStorageService: StorageService {
        var savedWorkout: Workout?
        var savedExercises: [Exercise] = []
        var savedSetLogs: [SetLog] = []
        var shouldFailSaving = false

        override func saveWorkout(id: Int64, name: String, timestamp: Date, isCompleted: Bool = false, needsSync: Bool = true) throws -> Workout {
            if shouldFailSaving {
                throw NSError(domain: "StorageError", code: 500, userInfo: [NSLocalizedDescriptionKey: "Failed to save workout"])
            }

            // Create a mock workout
            let workout = Workout(context: viewContext)
            workout.id = id
            workout.name = name
            workout.timestamp = timestamp
            workout.isCompleted = isCompleted
            workout.needsSync = needsSync

            savedWorkout = workout
            return workout
        }

        override func saveExercise(id: Int64, name: String, workoutID: Int64, isBodyweight: Bool = false) throws -> Exercise {
            if shouldFailSaving {
                throw NSError(domain: "StorageError", code: 500, userInfo: [NSLocalizedDescriptionKey: "Failed to save exercise"])
            }

            // Create a mock exercise
            let exercise = Exercise(context: viewContext)
            exercise.id = id
            exercise.name = name
            exercise.workoutID = workoutID
            exercise.isBodyweight = isBodyweight

            savedExercises.append(exercise)
            return exercise
        }

        override func saveSetLog(exerciseID: Int64, workoutID: Int64, reps: Int16, weight: Double, weightUnit: String, rir: Int16? = nil, timestamp: Date, isWarmup: Bool = false, needsSync: Bool = true) throws -> SetLog {
            if shouldFailSaving {
                throw NSError(domain: "StorageError", code: 500, userInfo: [NSLocalizedDescriptionKey: "Failed to save set log"])
            }

            // Create a mock set log
            let setLog = SetLog(context: viewContext)
            setLog.exerciseID = exerciseID
            setLog.workoutID = workoutID
            setLog.reps = reps
            setLog.weight = weight
            setLog.weightUnit = weightUnit
            if let rir = rir {
                setLog.rir = rir
            }
            setLog.timestamp = timestamp
            setLog.isWarmup = isWarmup
            setLog.needsSync = needsSync

            savedSetLogs.append(setLog)
            return setLog
        }

        override func getWorkout(id: Int64) throws -> Workout? {
            return savedWorkout
        }

        override func getExercise(id: Int64) throws -> Exercise? {
            return savedExercises.first { $0.id == id }
        }

        override func getSetLogsForExercise(exerciseID: Int64) throws -> [SetLog] {
            return savedSetLogs.filter { $0.exerciseID == exerciseID }
        }
    }

    // Test that starting a workout requires network connectivity
    func testStartWorkoutRequiresConnection() async {
        // Given
        let mockAPIClient = MockAPIClient()
        mockAPIClient.shouldFailWithNetworkError = true

        let viewModel = WorkoutDetailViewModel(workoutId: 1, apiClient: mockAPIClient)

        // When
        await viewModel.fetchWorkoutDetails()
        let result = await viewModel.startWorkout()

        // Then
        XCTAssertFalse(result, "Starting a workout should fail when there is no network connectivity")
        XCTAssertTrue(viewModel.hasError, "View model should have an error")
        XCTAssertNotNil(viewModel.errorMessage, "Error message should not be nil")
    }

    // Test that a workout in progress can continue if connection is lost
    func testWorkoutInProgressContinuesOffline() async {
        // Given
        let mockAPIClient = MockAPIClient()
        let mockStorageService = MockStorageService()

        // Create mock workout and exercise data
        let mockExercise = ExerciseModel(id: 1, name: "Bench Press", sets: [
            SetRecommendationModel(reps: 8, weight: 100.0, weightUnit: "kg", isWarmup: false, isFirstWorkSet: true, isLastPlannedSet: false, restDurationSeconds: 90)
        ])
        let mockWorkout = WorkoutTemplateModel(id: 1, name: "Push Day", exercises: [mockExercise])
        mockAPIClient.mockWorkoutTemplate = mockWorkout

        // Create mock recommendation
        let mockRecommendation = RecommendationModel(
            exerciseName: "Bench Press",
            sets: [
                SetRecommendationModel(reps: 8, weight: 100.0, weightUnit: "kg", isWarmup: false, isFirstWorkSet: true, isLastPlannedSet: false, restDurationSeconds: 90)
            ]
        )
        mockAPIClient.mockRecommendation = mockRecommendation

        // Start the workout with network connectivity
        let workoutViewModel = WorkoutDetailViewModel(workoutId: 1, apiClient: mockAPIClient, storageService: mockStorageService)
        await workoutViewModel.fetchWorkoutDetails()
        let startResult = await workoutViewModel.startWorkout()

        // Verify workout started successfully
        XCTAssertTrue(startResult, "Workout should start successfully with network connectivity")
        XCTAssertNotNil(mockStorageService.savedWorkout, "Workout should be saved to local storage")

        // Now simulate network loss
        mockAPIClient.shouldFailWithNetworkError = true

        // Create SetViewModel with the saved workout data
        let setViewModel = SetViewModel(exerciseId: 1, workoutId: 1, apiClient: mockAPIClient, storageService: mockStorageService)

        // When - try to save a set while offline
        let saveResult = await setViewModel.saveCurrentSet()

        // Then
        XCTAssertTrue(saveResult, "Saving a set should succeed even when offline")
        XCTAssertEqual(mockStorageService.savedSetLogs.count, 1, "Set log should be saved to local storage")
        XCTAssertTrue(mockStorageService.savedSetLogs[0].needsSync, "Set log should be marked for syncing")
    }

    // Test that the app can handle losing connection during rest timer
    func testLoseConnectionDuringRestTimer() async {
        // Given
        let mockAPIClient = MockAPIClient()
        let mockStorageService = MockStorageService()

        // Create mock recommendation
        let mockRecommendation = RecommendationModel(
            exerciseName: "Bench Press",
            sets: [
                SetRecommendationModel(reps: 8, weight: 100.0, weightUnit: "kg", isWarmup: false, isFirstWorkSet: true, isLastPlannedSet: false, restDurationSeconds: 90),
                SetRecommendationModel(reps: 8, weight: 105.0, weightUnit: "kg", isWarmup: false, isFirstWorkSet: false, isLastPlannedSet: false, restDurationSeconds: 90)
            ]
        )
        mockAPIClient.mockRecommendation = mockRecommendation

        // Create SetViewModel and load the exercise
        let setViewModel = SetViewModel(exerciseId: 1, workoutId: 1, apiClient: mockAPIClient, storageService: mockStorageService)
        await setViewModel.fetchExerciseDetails()

        // Save the first set
        let saveResult = await setViewModel.saveCurrentSet()
        XCTAssertTrue(saveResult, "Saving the first set should succeed")

        // Start the rest timer
        setViewModel.startTimer()
        XCTAssertTrue(setViewModel.isTimerActive, "Timer should be active")

        // Simulate network loss
        mockAPIClient.shouldFailWithNetworkError = true

        // When - timer completes and we move to the next set
        setViewModel.timerComplete()
        let moveResult = setViewModel.moveToNextSet()

        // Then
        XCTAssertTrue(moveResult, "Moving to the next set should succeed even when offline")
        XCTAssertEqual(setViewModel.currentReps, 8, "Current reps should be updated from local data")
        XCTAssertEqual(setViewModel.currentWeight, 105.0, "Current weight should be updated from local data")
    }

    // Test that the app can handle losing connection during the "Add Set" / "Next Exercise" choice screen
    func testLoseConnectionDuringChoiceScreen() async {
        // Given
        let mockAPIClient = MockAPIClient()
        let mockStorageService = MockStorageService()

        // Create mock recommendation with a last planned set
        let mockRecommendation = RecommendationModel(
            exerciseName: "Bench Press",
            sets: [
                SetRecommendationModel(reps: 8, weight: 100.0, weightUnit: "kg", isWarmup: false, isFirstWorkSet: true, isLastPlannedSet: true, restDurationSeconds: 90)
            ]
        )
        mockAPIClient.mockRecommendation = mockRecommendation

        // Create SetViewModel and load the exercise
        let setViewModel = SetViewModel(exerciseId: 1, workoutId: 1, apiClient: mockAPIClient, storageService: mockStorageService)
        await setViewModel.fetchExerciseDetails()

        // Save the set (which should be the last planned set)
        let saveResult = await setViewModel.saveCurrentSet()
        XCTAssertTrue(saveResult, "Saving the set should succeed")
        XCTAssertTrue(setViewModel.showingChoiceScreen, "Choice screen should be showing")

        // Simulate network loss
        mockAPIClient.shouldFailWithNetworkError = true

        // When - user taps "Add Set"
        setViewModel.addSet()

        // Then
        XCTAssertFalse(setViewModel.showingChoiceScreen, "Choice screen should be hidden")
        XCTAssertEqual(setViewModel.currentReps, 8, "Current reps should be preserved")
        XCTAssertEqual(setViewModel.currentWeight, 100.0, "Current weight should be preserved")
    }

    // Test that a workout can be completed offline
    func testCompleteWorkoutOffline() async {
        // Given
        let mockAPIClient = MockAPIClient()
        let mockStorageService = MockStorageService()
        let mockHealthKitService = MockHealthKitService()

        // Create a workout in local storage
        try? mockStorageService.saveWorkout(id: 1, name: "Push Day", timestamp: Date())

        // Create WorkoutCompleteViewModel
        let viewModel = WorkoutCompleteViewModel(
            workoutId: 1,
            apiClient: mockAPIClient,
            storageService: mockStorageService,
            healthKitService: mockHealthKitService
        )

        // Simulate network loss
        mockAPIClient.shouldFailWithNetworkError = true

        // When - finish the workout while offline
        let result = await viewModel.finishWorkout()

        // Then
        XCTAssertTrue(result, "Finishing a workout should succeed even when offline")
        XCTAssertTrue(mockStorageService.savedWorkout?.isCompleted ?? false, "Workout should be marked as completed in local storage")
        XCTAssertTrue(mockStorageService.savedWorkout?.needsSync ?? false, "Workout should be marked for syncing")
        XCTAssertTrue(mockHealthKitService.workoutEnded, "HealthKit workout session should be ended")
    }
}

// Mock HealthKit service for testing
class MockHealthKitService: HealthKitServiceProtocol {
    var workoutStarted = false
    var workoutEnded = false
    var workoutDataSaved = false

    func requestAuthorization() async -> Bool {
        return true
    }

    func startWorkout() async -> Bool {
        workoutStarted = true
        return true
    }

    func endWorkout() async -> Bool {
        workoutEnded = true
        return true
    }

    func saveWorkoutData(startDate: Date, endDate: Date, workoutName: String?) async -> Bool {
        workoutDataSaved = true
        return true
    }
}

// Protocol for HealthKit service
protocol HealthKitServiceProtocol {
    func requestAuthorization() async -> Bool
    func startWorkout() async -> Bool
    func endWorkout() async -> Bool
    func saveWorkoutData(startDate: Date, endDate: Date, workoutName: String?) async -> Bool
}
