import Foundation
import Network
import CoreData

/// Service for syncing locally stored data with the server
class SyncService {
    /// Shared instance for the entire application
    static let shared = SyncService()

    /// Network monitoring
    private let monitor = NWPathMonitor()

    /// Queue for network monitoring
    private let monitorQueue = DispatchQueue(label: "com.drmuscle.networkMonitor")

    /// Whether the device is currently online
    var isOnline = false

    /// The API client for syncing data with the server
    private let apiClient: DrMuscleAPIClient

    /// The storage service for local data operations
    private let storageService: StorageService

    /// Initializer that allows dependency injection for testing
    init(apiClient: DrMuscleAPIClient = .shared, storageService: StorageService = .shared) {
        self.apiClient = apiClient
        self.storageService = storageService
    }

    /// Start monitoring network status
    func startMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            let isOnline = path.status == .satisfied
            self?.isOnline = isOnline

            Task {
                await self?.networkStatusChanged(isOnline: isOnline)
            }
        }

        monitor.start(queue: monitorQueue)
    }

    /// Stop monitoring network status
    func stopMonitoring() {
        monitor.cancel()
    }

    /// Handle network status changes
    @MainActor
    func networkStatusChanged(isOnline: Bool) async {
        if isOnline {
            await attemptSync()
        }
    }

    /// Attempt to sync offline data
    @MainActor
    func attemptSync() async {
        guard isOnline else {
            print("Cannot sync: Device is offline")
            return
        }

        do {
            // Get all set logs that need to be synced
            let setLogs = try storageService.getSetLogsNeedingSync()

            // Sort set logs by timestamp (oldest first)
            let sortedSetLogs = setLogs.sorted { $0.timestamp ?? Date() < $1.timestamp ?? Date() }

            // Sync each set log
            for setLog in sortedSetLogs {
                await syncSetLog(setLog)
            }

            // TODO: Sync completed workouts if needed

        } catch {
            print("Error fetching set logs for sync: \(error.localizedDescription)")
        }
    }

    /// Sync a single set log with the server
    @MainActor
    func syncSetLog(_ setLog: SetLog) async {
        do {
            // Create the model for the API request
            let model = WorkoutLogSerieModel(
                exerciseId: setLog.exerciseID,
                reps: Int(setLog.reps),
                weight: WeightModel(
                    value: setLog.weight,
                    unit: setLog.weightUnit == "kg" ? .kg : .lb
                ),
                rir: setLog.rir >= 0 ? Int(setLog.rir) : nil,
                workoutId: setLog.workoutID,
                isWarmup: setLog.isWarmup,
                timestamp: setLog.timestamp ?? Date()
            )

            // Send the set log to the server
            let result = try await apiClient.addWorkoutLogSerie(model: model)

            if result.result {
                // Mark the set log as synced
                try storageService.markSetLogAsSynced(setLog)
                print("Successfully synced set log: \(setLog.exerciseID) - \(setLog.reps) reps @ \(setLog.weight) \(setLog.weightUnit ?? "kg")")
            } else {
                print("API returned false for set log sync: \(setLog.exerciseID) - \(setLog.reps) reps @ \(setLog.weight) \(setLog.weightUnit ?? "kg")")
            }
        } catch {
            print("Error syncing set log: \(error.localizedDescription)")
            // The set log will remain marked as needsSync = true and will be retried on the next sync attempt
        }
    }
}
