import SwiftUI

struct ContentView: View {
    @State private var isLoggedIn: Bool = false
    @State private var isLoading: Bool = false
    @ObservedObject private var errorService = ErrorHandlingService.shared

    var body: some View {
        NavigationView {
            if isLoggedIn {
                WorkoutListView(viewModel: WorkoutListViewModel())
            } else {
                VStack {
                    Text("Dr. Muscle")
                        .font(.title)
                        .foregroundColor(AppTheme.primaryTextColor)

                    if isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: AppTheme.primaryTextColor))
                    } else {
                        But<PERSON>("Sign In") {
                            isLoading = true
                            // Simulate loading
                            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                                isLoading = false
                                isLoggedIn = true
                            }
                        }
                        .buttonStyle(AppTheme.primaryButtonStyle())

                        // Test error button (for demonstration purposes)
                        But<PERSON>("Test Error") {
                            errorService.showError(message: "This is a test error message", autoDismiss: true)
                        }
                        .font(.footnote)
                        .padding(.top, 20)
                        .foregroundColor(AppTheme.secondaryTextColor)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(AppTheme.backgroundColor)
                .edgesIgnoringSafeArea(.all)
            }
        }
    }
}
