# Dr. Muscle Apple Watch App - Architecture (V1)

## Purpose

This document describes the architecture for the V1 standalone Dr. Muscle Apple Watch application.

## Architecture Overview

The application will be a **standalone watchOS app**, built natively using **Swift** and **SwiftUI**. It will communicate directly with the Dr. Muscle backend API for data synchronization and workout plan retrieval, minimizing reliance on the paired iPhone.

## Key Components & Patterns

1.  **UI Layer (SwiftUI):**
    *   All user interface elements will be built using SwiftUI, following Apple's Human Interface Guidelines where appropriate, while incorporating Dr. Muscle branding elements (e.g., button colors).
    *   Views will be designed for clarity and ease of interaction on the small watch screen, prioritizing large tap targets.
    *   State management within views will primarily use SwiftUI's built-in mechanisms (`@State`, `@StateObject`, `@EnvironmentObject`, etc.).

2.  **Data Flow & State Management:**
    *   A unidirectional data flow pattern (or similar, like MVVM) is recommended to manage application state. View models or state objects will fetch data, process user input, and update the UI.
    *   Dependency injection will be used to provide services (API client, storage manager, HealthKit manager) to view models/state objects, facilitating testability.

3.  **API Client:**
    *   A dedicated service layer will handle all communication with the Dr. Muscle backend API.
    *   It will be responsible for:
        *   Authentication (handling "Sign in with Apple" tokens).
        *   Fetching workout plans, including exercise details, set structure (reps, weight, rest), and flags (`isWarmup`, `isFirstWorkSet`, `isLastPlannedSet`).
        *   Fetching historical performance data needed for the dynamic save button calculation.
        *   Posting completed set data (reps, weight, RIR).
        *   Posting workout completion status.
        *   Handling API errors and translating them for the UI layer.
    *   Network requests will be asynchronous (`async/await`).

4.  **Local Storage:**
    *   Robust local storage is critical for offline functionality. **Core Data** is the recommended Apple framework for this.
    *   It will store:
        *   The currently active workout plan details (downloaded when the workout starts).
        *   The current state of the workout (e.g., current exercise/set index).
        *   Completed set data (reps, weight, RIR, timestamp) logged while online or offline.
        *   A queue or flag system to track data that needs syncing to the backend API.

5.  **Sync Service:**
    *   A background service responsible for:
        *   Detecting network connectivity changes.
        *   Attempting to sync locally stored, unsynced workout data to the backend API when connectivity is available.
        *   Handling sync conflicts or errors gracefully.

6.  **HealthKit Manager:**
    *   A dedicated service to interact with HealthKit.
    *   Responsibilities:
        *   Requesting user authorization for Heart Rate and Active Energy Burned data types.
        *   Starting and managing a `HKWorkoutSession` for the duration of the tracked workout.
        *   Querying live Heart Rate data (if needed for future display, but not required for V1 display).
        *   Saving the completed workout session (with associated energy burned and heart rate samples) to HealthKit.

7.  **Authentication Manager:**
    *   Handles the "Sign in with Apple" flow.
    *   Securely stores and manages authentication tokens (e.g., using the Keychain).
    *   Provides authentication status to the rest of the app.

8.  **Testing Strategy:**
    *   Unit Tests (XCTest): Focus on view models, services (API client, storage, HealthKit manager logic - potentially using fakes/stubs for external boundaries), and data models. Use BDD Given/When/Then structure.
    *   Integration Tests: Test the interaction between components (e.g., ViewModel -> Service -> Local Storage). Test API client against a mock server or potentially a dedicated test backend if available.
    *   UI Tests (XCTest): Limited use for critical user flows, acknowledging their potential flakiness.

## Dependencies

*   SwiftUI
*   WatchKit
*   Combine (for reactive programming, if desired)
*   HealthKit
*   Core Data
*   AuthenticationServices (for Sign in with Apple)
*   XCTest

## Open Questions / API Requirements

*   **API Set Identification:** The API MUST provide clear flags for each set within a workout:
    *   `isWarmup` (Boolean): Identifies warm-up sets.
    *   `isFirstWorkSet` (Boolean): Identifies the specific first work set (where RIR is prompted).
    *   `isLastPlannedSet` (Boolean): Identifies the final set planned for an exercise (triggers "Add Set" option).
    *   `restDurationSeconds` (Int): Specifies the rest period *after* this set.
*   **API Historical Data:** The API endpoint providing workout details must include the necessary historical performance data (e.g., reps/weight from the last time this exercise was performed at similar parameters) required to calculate the percentage change displayed on the "Save Set" button.