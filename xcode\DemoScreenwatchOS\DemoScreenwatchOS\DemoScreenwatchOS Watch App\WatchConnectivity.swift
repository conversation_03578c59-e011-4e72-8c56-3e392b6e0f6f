import WatchConnectivity
import WatchKit

class InterfaceController: <PERSON><PERSON><PERSON><PERSON>faceController, WCSessionDelegate {
    
    // The WCSession object is used for communication with the paired iPhone
    override func awake(withContext context: Any?) {
        super.awake(withContext: context)
        
        // Check if WCSession is supported
        if WCSession.isSupported() {
            WCSession.default.delegate = self
            WCSession.default.activate()
        }
    }

    // Required delegate method: This method is called when a message is received from iPhone
    func session(_ session: WCSession, didReceiveMessage message: [String : Any]) {
        if let receivedMessage = message["message"] as? String {
            print("Received message from iPhone: \(receivedMessage)")
        }
    }

    // Required delegate method: Called when session becomes inactive
    func sessionDidInactive(_ session: WCSession) {
        print("Session became inactive.")
    }

    // Required delegate method: Called when session is deactivated
    func sessionDidDeactive(_ session: WCSession) {
        print("Session deactivated.")
    }

    // Required delegate method: Called when session activation completes
    func session(_ session: WCSession, activationDidCompleteWith activationState: WCSessionActivationState, error: Error?) {
        if let error = error {
            print("Activation failed with error: \(error)")
        } else {
            print("Session activation completed with state: \(activationState.rawValue)")
        }
    }

    // Required delegate method: Called when session activation fails
    func session(_ session: WCSession, activationFailedWithError error: Error) {
        print("Session activation failed with error: \(error)")
    }

    // Function to send a message to the paired iPhone
    func sendMessageToPhone(message: String) {
        if WCSession.default.isReachable {
            WCSession.default.sendMessage(["message": message], replyHandler: { response in
                print("Message sent to iPhone: \(response)")
            }, errorHandler: { error in
                print("Error sending message to iPhone: \(error)")
            })
        }
    }
}
