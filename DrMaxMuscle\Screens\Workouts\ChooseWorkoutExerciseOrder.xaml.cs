﻿namespace DrMaxMuscle.Screens.Workouts;
using Acr.UserDialogs;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Layout;
using DrMuscleWebApiSharedModel;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Effects;
using DrMaxMuscle.Constants;
using System.Threading;
using DrMaxMuscle.Dependencies;
using RGPopup.Maui.Services;
using DrMaxMuscle.Utility;
using Newtonsoft.Json;

public partial class ChooseWorkoutExerciseOrder : ContentPage
{
    public ObservableListCollection<ExerciceModel> exerciseItems = new ObservableListCollection<ExerciceModel>();
    private View _toolTip;
    public ChooseWorkoutExerciseOrder()
    {
        InitializeComponent();

        ExerciseListView.ItemsSource = exerciseItems;
        ExerciseListView.ItemTapped += ExerciseListView_ItemTapped;

        SaveWorkoutButton.Clicked += NextButton_Clicked;

        RefreshLocalized();
        MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
        {
            RefreshLocalized();
        });

        // exerciseItems.OrderChanged += (sender, e) => {
        //         int jersey = 1;
        //foreach (var item in _allContacts)
        //{
        //    item.Jersey = jersey++;
        //}
        //};
        exerciseItems.CollectionChanged += (sender, e) =>
        {

        };
        OnBeforeShow();
    }

    private void RefreshLocalized()
    {
        Title = AppResources.ChooseOrder;
        SaveWorkoutButton.Text = AppResources.SaveWorkout;
    }
    public  async void OnBeforeShow()
    {
        
        DependencyService.Get<IFirebase>().SetScreenName("choose_workout_exercise_order");

        await UpdateExerciseList();
    }
    protected async override void OnAppearing()
    {
        base.OnAppearing();
        if (_toolTip != null && !Config.ShowDragnDrop)
        {
            Config.ShowDragnDrop = true;
            //

            TooltipEffect.SetPosition(_toolTip, Device.RuntimePlatform == Device.Android ? TooltipPosition.Left : TooltipPosition.Bottom);
            TooltipEffect.SetBackgroundColor(_toolTip, AppThemeConstants.BlueColor);
            TooltipEffect.SetTextColor(_toolTip, Colors.White);
            TooltipEffect.SetText(_toolTip, $"Drag and drop\nto reorder");
            TooltipEffect.SetHasTooltip(_toolTip, true);
            await Task.Delay(500);
            TooltipEffect.SetHasShowTooltip(_toolTip, true);
        }
    }

    private async void NextButton_Clicked(object sender, EventArgs e)
    {
        try
        {


            CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Clear();
            LocalDBManager.Instance.SetDBSetting($"Workout{DateTime.Now.Date}{CurrentLog.Instance.CurrentWorkoutTemplate.Id}", ""); ;
            int lastIndex = exerciseItems.Count - 1;
            var CurrentWorkoutId = LocalDBManager.Instance.GetDBSetting("CurrentWorkoutId")?.Value;
            var IsCardio = LocalDBManager.Instance.GetDBSetting("Cardio")?.Value;
            if (exerciseItems.Any(a => a.Id == 16508) && IsCardio == "false")
            {
                if (!string.IsNullOrEmpty(CurrentWorkoutId))
                {
                    var data = new
                    {
                        Id = CurrentWorkoutId,
                        IsCardio = "true"
                    };
                    LocalDBManager.Instance.SetDBSetting("CurrentWorkoutCardioDetails", JsonConvert.SerializeObject(data));
                }
            }
            foreach (ExerciceModel m in exerciseItems)
            {
                if (m.Id == 16508 && exerciseItems.IndexOf(m) == lastIndex && IsCardio != "false")
                {
                    continue; // Skip adding this item
                }
                CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Add(m);
            }
            
            if (CurrentLog.Instance.WorkoutsByExercise == null)
                CurrentLog.Instance.WorkoutsByExercise = new Dictionary<long, List<ExerciceModel>>();
            if (CurrentLog.Instance.IsAddingExerciseLocally)
            {

                CurrentLog.Instance.IsAddedExercises = true;
                if (CurrentLog.Instance.CurrentWorkoutTemplate.UserId == "89c52f09-240c-40a8-96df-9e8e152b7d63" || CurrentLog.Instance.CurrentWorkoutTemplateGroup != null && CurrentLog.Instance.CurrentWorkoutTemplateGroup.IsFeaturedProgram && CurrentLog.Instance.CurrentWorkoutTemplateGroup.WorkoutTemplates.Any(x => x.Id == CurrentLog.Instance.CurrentWorkoutTemplate.Id))
                {
                    BooleanModel result = await DrMuscleRestClient.Instance.CreateNewUserWorkoutTemplate(CurrentLog.Instance.CurrentWorkoutTemplate);
                }
                else
                {
                    if (CurrentLog.Instance.CurrentWorkoutTemplate.IsSystemExercise == false)
                    {
                        BooleanModel result = await DrMuscleRestClient.Instance.CreateNewWorkoutTemplate(CurrentLog.Instance.CurrentWorkoutTemplate);
                    }
                    else
                    {
                        BooleanModel result = await DrMuscleRestClient.Instance.CreateNewUserWorkoutTemplate(CurrentLog.Instance.CurrentWorkoutTemplate);
                    }
                }
                if (!Config.IsFirstWorkoutEditedPopup)
                {
                    Config.IsFirstWorkoutEditedPopup = true;
                    var waitHandle = new EventWaitHandle(false, EventResetMode.AutoReset);
                    var modalPage = new Views.GeneralPopup("lists.png", "You got it!", "First workout edited", "View workout");
                    if (modalPage != null)
                    {
                        modalPage.Disappearing += (sender2, e2) =>
                        {
                            waitHandle.Set();
                        };
                        await PopupNavigation.Instance.PushAsync(modalPage);

                        await Task.Run(() => waitHandle.WaitOne());
                    }
                }
                LocalDBManager.Instance.SetDBSetting($"WorkoutUpdated" + CurrentLog.Instance.CurrentWorkoutTemplate.Id, "true");
                CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Clear();
               await Utility.HelperClass.PopToPage<KenkoChooseYourWorkoutExercisePage>(this.Navigation); 


            }
            else
            {
                if (CurrentLog.Instance.WorkoutsByExercise.ContainsKey(CurrentLog.Instance.CurrentWorkoutTemplate.Id))
                    CurrentLog.Instance.WorkoutsByExercise.Remove(CurrentLog.Instance.CurrentWorkoutTemplate.Id);
                BooleanModel result = await DrMuscleRestClient.Instance.CreateNewWorkoutTemplate(CurrentLog.Instance.CurrentWorkoutTemplate);
                if (result != null && result.Result)
                {
                    //TODO: MAUI
                    //PagesFactory.GetPage<ChooseYourCustomWorkoutPage>().workouts = new List<WorkoutTemplateModel>();
                    //PagesFactory.GetPage<ChooseYourCustomWorkoutPage>().workoutGroups = new List<WorkoutTemplateGroupModel>();
                    //if (Device.RuntimePlatform == Device.Android)
                    //    await PagesFactory.PushAsyncWithoutBefore<ChooseYourCustomWorkoutPage>();
                    //else
                    //    PagesFactory.PushAsync<ChooseYourCustomWorkoutPage>();


                    Utility.HelperClass.PopToPage<ChooseYourCustomWorkoutPage>(this.Navigation);
                    
                }
                else
                {
                    ConnectionErrorPopup();
                }
            }
        }
        catch (Exception exception)
        {

        }
        finally
        {
            if (((App)Application.Current).UserWorkoutContexts != null)
            {
                ((App)Application.Current).UserWorkoutContexts.workouts = null;
                ((App)Application.Current).UserWorkoutContexts.SaveContexts();
                TimeZoneInfo local = TimeZoneInfo.Local;

                var workoutLogAverage = await DrMuscleRestClient.Instance.GetUserWorkoutProgramTimeZoneInfo(local);
                if (workoutLogAverage != null)
                {
                    ((App)Application.Current).UserWorkoutContexts.workouts = workoutLogAverage;
                    ((App)Application.Current).UserWorkoutContexts.SaveContexts();
                }
            }
        }
    }
    private void ExerciseListView_ItemTapped(object sender, ItemTappedEventArgs e)
    {

    }
    private void OnBindingContextChanged(object sender, EventArgs e)
    {
        try
        {
            base.OnBindingContextChanged();

            // CHANGE: Safely cast sender to BindableObject and check if it's valid
            if (!(sender is BindableObject bindableObject))
            {
                Console.WriteLine("Sender is not a BindableObject");
                return;
            }

            // CHANGE: Check if BindingContext is null to avoid null reference exceptions
            if (bindableObject.BindingContext == null)
                return;

            // CHANGE: Safely cast BindingContext to ExerciceModel using 'as' and null check
            var exerciceModel = bindableObject.BindingContext as ExerciceModel;
            if (exerciceModel == null)
            {
                Console.WriteLine("BindingContext is not an ExerciceModel");
                return;
            }

            // CHANGE: Safely cast UI elements with null checks and type validation
            if (sender is ViewCell viewCell &&
                viewCell.View is StackLayout outerStack &&
                outerStack.Children.Count > 1 &&
                outerStack.Children[1] is StackLayout innerStack &&
                innerStack.Children.Count > 0 &&
                innerStack.Children[0] is View toolTipView)
            {
                _toolTip = toolTipView; // Assign only if the hierarchy is valid
            }
            else
            {
                Console.WriteLine("UI hierarchy is not as expected");
            }

            // CHANGE: Commented-out code remains unchanged, but ensure it follows the same safety checks if uncommented
            //Button up = (Button)((StackLayout)((StackLayout)((ViewCell)sender).View).Children[1]).Children[0];
            //Button down = (Button)((StackLayout)((StackLayout)((ViewCell)sender).View).Children[1]).Children[1];

            //int itemIndex = exerciseItems.IndexOf(m);

            //up.Clicked += (object s, EventArgs ea) =>
            //{
            //    MoveExercise(m, "up", (Button) s);
            //};

            //down.Clicked += (object s, EventArgs ea) =>
            //{
            //    MoveExercise(m, "down", (Button)s);
            //};
        }
        catch (InvalidCastException ex)
        {
            Console.WriteLine($"Invalid cast occurred: {ex.Message}");
        }
        catch (Exception ex)
        {
            // CHANGE: Added a try-catch block to handle unexpected exceptions
            Console.WriteLine($"An error occurred: {ex.Message}");
        }
    }

    private void MoveExercise(ExerciceModel m, string UpDown, Button button)
    {
        int itemIndex = exerciseItems.IndexOf(m);
        if (UpDown == "up")
        {
            if (itemIndex - 1 >= 0)
                exerciseItems.Move(itemIndex, itemIndex - 1);
        }
        else
        {
            if (itemIndex + 1 < CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Count)
                exerciseItems.Move(itemIndex, itemIndex + 1);
        }
    }

    private async Task UpdateExerciseList()
    {
        exerciseItems.Clear();
        try
        {
            foreach (ExerciceModel e in CurrentLog.Instance.CurrentWorkoutTemplate.Exercises)
            {
                exerciseItems.Add(e);
            }
        }
        catch (Exception e)
        {
            ConnectionErrorPopup();
        }
    }

    async Task ConnectionErrorPopup()
    {
        // await UserDialogs.Instance.AlertAsync(new AlertConfig()
        // {
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     Message = AppResources.PleaseCheckInternetConnection,
        //     Title = AppResources.ConnectionError,
        //     OkText = "Try again"
        // });

        await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                        AppResources.PleaseCheckInternetConnection,"Try again","");
    }
}

