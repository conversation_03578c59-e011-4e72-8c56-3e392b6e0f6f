<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="21754" systemVersion="22G91" minimumToolsVersion="Automatic" sourceLanguage="Swift" userDefinedModelVersionIdentifier="">
    <entity name="Exercise" representedClassName="Exercise" syncable="YES" codeGenerationType="class">
        <attribute name="id" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="isBodyweight" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="name" attributeType="String"/>
        <attribute name="workoutID" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <relationship name="setLogs" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="SetLog" inverseName="exercise" inverseEntity="SetLog"/>
        <relationship name="workout" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Workout" inverseName="exercises" inverseEntity="Workout"/>
    </entity>
    <entity name="SetLog" representedClassName="SetLog" syncable="YES" codeGenerationType="class">
        <attribute name="exerciseID" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="isWarmup" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="needsSync" attributeType="Boolean" defaultValueString="YES" usesScalarValueType="YES"/>
        <attribute name="reps" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="rir" optional="YES" attributeType="Integer 16" usesScalarValueType="YES"/>
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="weight" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="weightUnit" attributeType="String" defaultValueString="kg"/>
        <attribute name="workoutID" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <relationship name="exercise" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Exercise" inverseName="setLogs" inverseEntity="Exercise"/>
        <relationship name="workout" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Workout" inverseName="setLogs" inverseEntity="Workout"/>
    </entity>
    <entity name="Workout" representedClassName="Workout" syncable="YES" codeGenerationType="class">
        <attribute name="id" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="isCompleted" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="name" attributeType="String"/>
        <attribute name="needsSync" attributeType="Boolean" defaultValueString="YES" usesScalarValueType="YES"/>
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="exercises" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Exercise" inverseName="workout" inverseEntity="Exercise"/>
        <relationship name="setLogs" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="SetLog" inverseName="workout" inverseEntity="SetLog"/>
    </entity>
</model>
