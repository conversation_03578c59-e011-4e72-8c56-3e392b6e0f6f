﻿<?xml version="1.0" encoding="utf-8" ?>
<toolkit:Popup xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
               xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
               x:Class="DrMaxMuscle.Views.CompletePopup"
               xmlns:constants="clr-namespace:DrMaxMuscle.Constants"
               xmlns:controls="clr-namespace:DrMaxMuscle.Layout"
               xmlns:ctrl="clr-namespace:DrMaxMuscle.Controls"
               xmlns:skia="clr-namespace:SkiaSharp.Extended.UI.Controls;assembly=SkiaSharp.Extended.UI"
               xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
               xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
               Color="Transparent">

    <Grid IgnoreSafeArea="True"
          BackgroundColor="Transparent"
          RowDefinitions="*,Auto"
          ColumnDefinitions="*">

        <!-- Background Image -->
        <ffimageloading:CachedImage Grid.RowSpan="2"
                                    Grid.ColumnSpan="1"
                                    Source="nav.png"
                                    Aspect="Fill"
                                    x:Name="BackImage"
                                    ErrorPlaceholder="backgroundblack.png"
                                    HorizontalOptions="FillAndExpand"
                                    VerticalOptions="FillAndExpand"/>

        <!-- Foreground Content -->
        <Grid Grid.Row="0"
              HorizontalOptions="Center"
              VerticalOptions="Center"
              Padding="0">

            <Frame Padding="0"
                   CornerRadius="4"
                   HasShadow="False"
                   BorderColor="Transparent"
                   x:Name="mainFrame"
                   IsClippedToBounds="True"
                   VerticalOptions="FillAndExpand"
                   BackgroundColor="Transparent"
                   Margin="0">

                <Grid HorizontalOptions="FillAndExpand"
                      VerticalOptions="Center"
                      BackgroundColor="Transparent"
                      Padding="0">

                    <VerticalStackLayout Spacing="40"
                                         HorizontalOptions="Center"
                                         VerticalOptions="FillAndExpand"
                                         BackgroundColor="Transparent">

                        <skia:SKLottieView Source="complete_lottie_blue.json"
                                           RepeatCount="0"
                                           RepeatMode="Restart"
                                           HeightRequest="200"
                                           WidthRequest="200"
                                           HorizontalOptions="Center" />

                        <Label x:Name="MessageLabel"
                               Text="Message"
                               HorizontalOptions="Center"
                               TextColor="White"
                               FontAttributes="Bold"
                               FontSize="40" />

                    </VerticalStackLayout>

                </Grid>

                <Frame.GestureRecognizers>
                    <TapGestureRecognizer Tapped="DrMuscleButtonShareTrial_Clicked" />
                </Frame.GestureRecognizers>
            </Frame>
        </Grid>

    </Grid>
</toolkit:Popup>
