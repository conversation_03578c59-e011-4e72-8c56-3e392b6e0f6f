// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 55;
	objects = {

/* Begin PBXBuildFile section */
		6A095F282D4D47230031A1E7 /* SQLite in Frameworks */ = {isa = PBXBuildFile; productRef = 6A095F272D4D47230031A1E7 /* SQLite */; };
		6AAE6FE02D499D92001C55A1 /* WatchConnectivity.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AAE6FDF2D499D92001C55A1 /* WatchConnectivity.swift */; };
		6AAE6FE22D4AF019001C55A1 /* WatchConnectivity.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6AAE6FDD2D499529001C55A1 /* WatchConnectivity.framework */; };
		6EA5C20D2D38D9F600DDA12E /* RMCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6EA5C20C2D38D9F600DDA12E /* RMCalculation.swift */; };
		6EAD2CFF2D2FC700005C2800 /* DemoScreenwatchOS App.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6EAD2CFE2D2FC700005C2800 /* DemoScreenwatchOS App.swift */; };
		6EAD2D012D2FC700005C2800 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6EAD2D002D2FC700005C2800 /* ContentView.swift */; };
		6EAD2D032D2FC702005C2800 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 6EAD2D022D2FC702005C2800 /* Assets.xcassets */; };
		6EAD2D062D2FC702005C2800 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 6EAD2D052D2FC702005C2800 /* Preview Assets.xcassets */; };
		6EAD2D102D2FC703005C2800 /* DemoScreenwatchOS_Watch_AppTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6EAD2D0F2D2FC703005C2800 /* DemoScreenwatchOS_Watch_AppTests.swift */; };
		6EAD2D1A2D2FC703005C2800 /* DemoScreenwatchOS_Watch_AppUITests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6EAD2D192D2FC703005C2800 /* DemoScreenwatchOS_Watch_AppUITests.swift */; };
		6EAD2D1C2D2FC703005C2800 /* DemoScreenwatchOS_Watch_AppUITestsLaunchTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6EAD2D1B2D2FC703005C2800 /* DemoScreenwatchOS_Watch_AppUITestsLaunchTests.swift */; };
		6EB38AE32D350A9100510D05 /* DatabaseManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6EB38AE22D350A9100510D05 /* DatabaseManager.swift */; };
		6EB38AE62D365F4600510D05 /* ExerciseSets.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6EB38AE52D365F4600510D05 /* ExerciseSets.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		6EAD2D0C2D2FC703005C2800 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 6EAD2CED2D2FC6FF005C2800 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 6EAD2CF82D2FC700005C2800;
			remoteInfo = "DemoScreenwatchOS Watch App";
		};
		6EAD2D162D2FC703005C2800 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 6EAD2CED2D2FC6FF005C2800 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 6EAD2CF82D2FC700005C2800;
			remoteInfo = "DemoScreenwatchOS Watch App";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		6A2BF1482D4B02C1003E4279 /* WatchKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WatchKit.framework; path = Platforms/WatchOS.platform/Developer/SDKs/WatchOS10.0.sdk/System/Library/Frameworks/WatchKit.framework; sourceTree = DEVELOPER_DIR; };
		6AAE6FDD2D499529001C55A1 /* WatchConnectivity.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WatchConnectivity.framework; path = Platforms/WatchOS.platform/Developer/SDKs/WatchOS10.0.sdk/System/Library/Frameworks/WatchConnectivity.framework; sourceTree = DEVELOPER_DIR; };
		6AAE6FDF2D499D92001C55A1 /* WatchConnectivity.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WatchConnectivity.swift; sourceTree = "<group>"; };
		6AAE6FE12D49AA04001C55A1 /* DemoScreenwatchOS-Watch-App-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = "DemoScreenwatchOS-Watch-App-Info.plist"; sourceTree = SOURCE_ROOT; };
		6EA5C20C2D38D9F600DDA12E /* RMCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RMCalculation.swift; sourceTree = "<group>"; };
		6EAD2CF92D2FC700005C2800 /* DemoScreenwatchOS Watch App.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "DemoScreenwatchOS Watch App.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		6EAD2CFE2D2FC700005C2800 /* DemoScreenwatchOS App.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "DemoScreenwatchOS App.swift"; sourceTree = "<group>"; };
		6EAD2D002D2FC700005C2800 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		6EAD2D022D2FC702005C2800 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		6EAD2D052D2FC702005C2800 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		6EAD2D0B2D2FC703005C2800 /* DemoScreenwatchOS Watch AppTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "DemoScreenwatchOS Watch AppTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		6EAD2D0F2D2FC703005C2800 /* DemoScreenwatchOS_Watch_AppTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DemoScreenwatchOS_Watch_AppTests.swift; sourceTree = "<group>"; };
		6EAD2D152D2FC703005C2800 /* DemoScreenwatchOS Watch AppUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "DemoScreenwatchOS Watch AppUITests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		6EAD2D192D2FC703005C2800 /* DemoScreenwatchOS_Watch_AppUITests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DemoScreenwatchOS_Watch_AppUITests.swift; sourceTree = "<group>"; };
		6EAD2D1B2D2FC703005C2800 /* DemoScreenwatchOS_Watch_AppUITestsLaunchTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DemoScreenwatchOS_Watch_AppUITestsLaunchTests.swift; sourceTree = "<group>"; };
		6EB38ADC2D34F07900510D05 /* DemoScreenwatchOS-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "DemoScreenwatchOS-Bridging-Header.h"; sourceTree = "<group>"; };
		6EB38AE22D350A9100510D05 /* DatabaseManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DatabaseManager.swift; sourceTree = "<group>"; };
		6EB38AE52D365F4600510D05 /* ExerciseSets.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExerciseSets.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		6EAD2CF62D2FC700005C2800 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6A095F282D4D47230031A1E7 /* SQLite in Frameworks */,
				6AAE6FE22D4AF019001C55A1 /* WatchConnectivity.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6EAD2D082D2FC703005C2800 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6EAD2D122D2FC703005C2800 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		6A2908182D4D838F0043E482 /* Context */ = {
			isa = PBXGroup;
			children = (
				6EB38AE22D350A9100510D05 /* DatabaseManager.swift */,
			);
			path = Context;
			sourceTree = "<group>";
		};
		6A2908202D4D85FB0043E482 /* WatchHelper */ = {
			isa = PBXGroup;
			children = (
				6EA5C20C2D38D9F600DDA12E /* RMCalculation.swift */,
			);
			path = WatchHelper;
			sourceTree = "<group>";
		};
		6AAE6FDC2D499528001C55A1 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				6A2BF1482D4B02C1003E4279 /* WatchKit.framework */,
				6AAE6FDD2D499529001C55A1 /* WatchConnectivity.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		6EAD2CEC2D2FC6FF005C2800 = {
			isa = PBXGroup;
			children = (
				6EAD2CFD2D2FC700005C2800 /* DemoScreenwatchOS Watch App */,
				6EAD2D0E2D2FC703005C2800 /* DemoScreenwatchOS Watch AppTests */,
				6EAD2D182D2FC703005C2800 /* DemoScreenwatchOS Watch AppUITests */,
				6EAD2CF42D2FC700005C2800 /* Products */,
				6AAE6FDC2D499528001C55A1 /* Frameworks */,
			);
			sourceTree = "<group>";
			usesTabs = 0;
		};
		6EAD2CF42D2FC700005C2800 /* Products */ = {
			isa = PBXGroup;
			children = (
				6EAD2CF92D2FC700005C2800 /* DemoScreenwatchOS Watch App.app */,
				6EAD2D0B2D2FC703005C2800 /* DemoScreenwatchOS Watch AppTests.xctest */,
				6EAD2D152D2FC703005C2800 /* DemoScreenwatchOS Watch AppUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		6EAD2CFD2D2FC700005C2800 /* DemoScreenwatchOS Watch App */ = {
			isa = PBXGroup;
			children = (
				6A2908202D4D85FB0043E482 /* WatchHelper */,
				6A2908182D4D838F0043E482 /* Context */,
				6AAE6FE12D49AA04001C55A1 /* DemoScreenwatchOS-Watch-App-Info.plist */,
				6AAE6FDF2D499D92001C55A1 /* WatchConnectivity.swift */,
				6EB38AE42D365F1000510D05 /* Model */,
				6EAD2CFE2D2FC700005C2800 /* DemoScreenwatchOS App.swift */,
				6EAD2D002D2FC700005C2800 /* ContentView.swift */,
				6EAD2D022D2FC702005C2800 /* Assets.xcassets */,
				6EAD2D042D2FC702005C2800 /* Preview Content */,
				6EB38ADC2D34F07900510D05 /* DemoScreenwatchOS-Bridging-Header.h */,
			);
			path = "DemoScreenwatchOS Watch App";
			sourceTree = "<group>";
		};
		6EAD2D042D2FC702005C2800 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				6EAD2D052D2FC702005C2800 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		6EAD2D0E2D2FC703005C2800 /* DemoScreenwatchOS Watch AppTests */ = {
			isa = PBXGroup;
			children = (
				6EAD2D0F2D2FC703005C2800 /* DemoScreenwatchOS_Watch_AppTests.swift */,
			);
			path = "DemoScreenwatchOS Watch AppTests";
			sourceTree = "<group>";
		};
		6EAD2D182D2FC703005C2800 /* DemoScreenwatchOS Watch AppUITests */ = {
			isa = PBXGroup;
			children = (
				6EAD2D192D2FC703005C2800 /* DemoScreenwatchOS_Watch_AppUITests.swift */,
				6EAD2D1B2D2FC703005C2800 /* DemoScreenwatchOS_Watch_AppUITestsLaunchTests.swift */,
			);
			path = "DemoScreenwatchOS Watch AppUITests";
			sourceTree = "<group>";
		};
		6EB38AE42D365F1000510D05 /* Model */ = {
			isa = PBXGroup;
			children = (
				6EB38AE52D365F4600510D05 /* ExerciseSets.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		6EAD2CF82D2FC700005C2800 /* DemoScreenwatchOS Watch App */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6EAD2D1F2D2FC703005C2800 /* Build configuration list for PBXNativeTarget "DemoScreenwatchOS Watch App" */;
			buildPhases = (
				6EAD2CF52D2FC700005C2800 /* Sources */,
				6EAD2CF62D2FC700005C2800 /* Frameworks */,
				6EAD2CF72D2FC700005C2800 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "DemoScreenwatchOS Watch App";
			packageProductDependencies = (
				6A095F272D4D47230031A1E7 /* SQLite */,
			);
			productName = "DemoScreenwatchOS Watch App";
			productReference = 6EAD2CF92D2FC700005C2800 /* DemoScreenwatchOS Watch App.app */;
			productType = "com.apple.product-type.application";
		};
		6EAD2D0A2D2FC703005C2800 /* DemoScreenwatchOS Watch AppTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6EAD2D262D2FC703005C2800 /* Build configuration list for PBXNativeTarget "DemoScreenwatchOS Watch AppTests" */;
			buildPhases = (
				6EAD2D072D2FC703005C2800 /* Sources */,
				6EAD2D082D2FC703005C2800 /* Frameworks */,
				6EAD2D092D2FC703005C2800 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				6EAD2D0D2D2FC703005C2800 /* PBXTargetDependency */,
			);
			name = "DemoScreenwatchOS Watch AppTests";
			productName = "DemoScreenwatchOS Watch AppTests";
			productReference = 6EAD2D0B2D2FC703005C2800 /* DemoScreenwatchOS Watch AppTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		6EAD2D142D2FC703005C2800 /* DemoScreenwatchOS Watch AppUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6EAD2D292D2FC703005C2800 /* Build configuration list for PBXNativeTarget "DemoScreenwatchOS Watch AppUITests" */;
			buildPhases = (
				6EAD2D112D2FC703005C2800 /* Sources */,
				6EAD2D122D2FC703005C2800 /* Frameworks */,
				6EAD2D132D2FC703005C2800 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				6EAD2D172D2FC703005C2800 /* PBXTargetDependency */,
			);
			name = "DemoScreenwatchOS Watch AppUITests";
			productName = "DemoScreenwatchOS Watch AppUITests";
			productReference = 6EAD2D152D2FC703005C2800 /* DemoScreenwatchOS Watch AppUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		6EAD2CED2D2FC6FF005C2800 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1400;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					6EAD2CF82D2FC700005C2800 = {
						CreatedOnToolsVersion = 14.0;
					};
					6EAD2D0A2D2FC703005C2800 = {
						CreatedOnToolsVersion = 14.0;
						TestTargetID = 6EAD2CF82D2FC700005C2800;
					};
					6EAD2D142D2FC703005C2800 = {
						CreatedOnToolsVersion = 14.0;
						TestTargetID = 6EAD2CF82D2FC700005C2800;
					};
				};
			};
			buildConfigurationList = 6EAD2CF02D2FC6FF005C2800 /* Build configuration list for PBXProject "DemoScreenwatchOS" */;
			compatibilityVersion = "Xcode 13.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 6EAD2CEC2D2FC6FF005C2800;
			packageReferences = (
				6EB38ADF2D3504D500510D05 /* XCRemoteSwiftPackageReference "SQLite" */,
			);
			productRefGroup = 6EAD2CF42D2FC700005C2800 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				6EAD2CF82D2FC700005C2800 /* DemoScreenwatchOS Watch App */,
				6EAD2D0A2D2FC703005C2800 /* DemoScreenwatchOS Watch AppTests */,
				6EAD2D142D2FC703005C2800 /* DemoScreenwatchOS Watch AppUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		6EAD2CF72D2FC700005C2800 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6EAD2D062D2FC702005C2800 /* Preview Assets.xcassets in Resources */,
				6EAD2D032D2FC702005C2800 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6EAD2D092D2FC703005C2800 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6EAD2D132D2FC703005C2800 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		6EAD2CF52D2FC700005C2800 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6AAE6FE02D499D92001C55A1 /* WatchConnectivity.swift in Sources */,
				6EB38AE62D365F4600510D05 /* ExerciseSets.swift in Sources */,
				6EAD2D012D2FC700005C2800 /* ContentView.swift in Sources */,
				6EA5C20D2D38D9F600DDA12E /* RMCalculation.swift in Sources */,
				6EAD2CFF2D2FC700005C2800 /* DemoScreenwatchOS App.swift in Sources */,
				6EB38AE32D350A9100510D05 /* DatabaseManager.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6EAD2D072D2FC703005C2800 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6EAD2D102D2FC703005C2800 /* DemoScreenwatchOS_Watch_AppTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6EAD2D112D2FC703005C2800 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6EAD2D1A2D2FC703005C2800 /* DemoScreenwatchOS_Watch_AppUITests.swift in Sources */,
				6EAD2D1C2D2FC703005C2800 /* DemoScreenwatchOS_Watch_AppUITestsLaunchTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		6EAD2D0D2D2FC703005C2800 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 6EAD2CF82D2FC700005C2800 /* DemoScreenwatchOS Watch App */;
			targetProxy = 6EAD2D0C2D2FC703005C2800 /* PBXContainerItemProxy */;
		};
		6EAD2D172D2FC703005C2800 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 6EAD2CF82D2FC700005C2800 /* DemoScreenwatchOS Watch App */;
			targetProxy = 6EAD2D162D2FC703005C2800 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		6EAD2D1D2D2FC703005C2800 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKWatchOnly = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				MACH_O_TYPE = mh_execute;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Debug;
		};
		6EAD2D1E2D2FC703005C2800 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_WKWatchOnly = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				MACH_O_TYPE = mh_execute;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Release;
		};
		6EAD2D202D2FC703005C2800 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"DemoScreenwatchOS Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 7AAXZ47995;
				"DEVELOPMENT_TEAM[sdk=watchos*]" = 7AAXZ47995;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "DemoScreenwatchOS-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = DemoScreenwatchOS;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKRunsIndependentlyOfCompanionApp = NO;
				INFOPLIST_KEY_WKWatchOnly = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = "$(RECOMMENDED_MACOSX_DEPLOYMENT_TARGET)";
				MARKETING_VERSION = 1.0;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.drmaxmuscle.max;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "Tejinder Dr Muscle";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Tejinder Dr Muscle";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=watchos*]" = "Tejinder Dr Muscle";
				SDKROOT = watchos;
				SKIP_INSTALL = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				TVOS_DEPLOYMENT_TARGET = 12.0;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Debug;
		};
		6EAD2D212D2FC703005C2800 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"DemoScreenwatchOS Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 7AAXZ47995;
				"DEVELOPMENT_TEAM[sdk=watchos*]" = 7AAXZ47995;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "DemoScreenwatchOS-Watch-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = DemoScreenwatchOS;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKRunsIndependentlyOfCompanionApp = NO;
				INFOPLIST_KEY_WKWatchOnly = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = "$(RECOMMENDED_MACOSX_DEPLOYMENT_TARGET)";
				MARKETING_VERSION = 1.0;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.drmaxmuscle.max;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "Tejinder Dr Muscle";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Tejinder Dr Muscle";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=watchos*]" = "Tejinder Dr Muscle";
				SDKROOT = watchos;
				SKIP_INSTALL = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				TVOS_DEPLOYMENT_TARGET = 12.0;
				VALIDATE_PRODUCT = YES;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Release;
		};
		6EAD2D272D2FC703005C2800 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 7AAXZ47995;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "DrMuscle.DemoScreenwatchOS-Watch-AppTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/DemoScreenwatchOS Watch App.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/DemoScreenwatchOS Watch App";
				WATCHOS_DEPLOYMENT_TARGET = 9.0;
			};
			name = Debug;
		};
		6EAD2D282D2FC703005C2800 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 7AAXZ47995;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "DrMuscle.DemoScreenwatchOS-Watch-AppTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/DemoScreenwatchOS Watch App.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/DemoScreenwatchOS Watch App";
				VALIDATE_PRODUCT = YES;
				WATCHOS_DEPLOYMENT_TARGET = 9.0;
			};
			name = Release;
		};
		6EAD2D2A2D2FC703005C2800 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 7AAXZ47995;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "DrMuscle.DemoScreenwatchOS-Watch-AppUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				TEST_TARGET_NAME = "DemoScreenwatchOS Watch App";
				WATCHOS_DEPLOYMENT_TARGET = 9.0;
			};
			name = Debug;
		};
		6EAD2D2B2D2FC703005C2800 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 7AAXZ47995;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "DrMuscle.DemoScreenwatchOS-Watch-AppUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				TEST_TARGET_NAME = "DemoScreenwatchOS Watch App";
				VALIDATE_PRODUCT = YES;
				WATCHOS_DEPLOYMENT_TARGET = 9.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		6EAD2CF02D2FC6FF005C2800 /* Build configuration list for PBXProject "DemoScreenwatchOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6EAD2D1D2D2FC703005C2800 /* Debug */,
				6EAD2D1E2D2FC703005C2800 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6EAD2D1F2D2FC703005C2800 /* Build configuration list for PBXNativeTarget "DemoScreenwatchOS Watch App" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6EAD2D202D2FC703005C2800 /* Debug */,
				6EAD2D212D2FC703005C2800 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6EAD2D262D2FC703005C2800 /* Build configuration list for PBXNativeTarget "DemoScreenwatchOS Watch AppTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6EAD2D272D2FC703005C2800 /* Debug */,
				6EAD2D282D2FC703005C2800 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6EAD2D292D2FC703005C2800 /* Build configuration list for PBXNativeTarget "DemoScreenwatchOS Watch AppUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6EAD2D2A2D2FC703005C2800 /* Debug */,
				6EAD2D2B2D2FC703005C2800 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		6EB38ADF2D3504D500510D05 /* XCRemoteSwiftPackageReference "SQLite" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/stephencelis/SQLite.swift";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.9.2;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		6A095F272D4D47230031A1E7 /* SQLite */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6EB38ADF2D3504D500510D05 /* XCRemoteSwiftPackageReference "SQLite" */;
			productName = SQLite;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 6EAD2CED2D2FC6FF005C2800 /* Project object */;
}
