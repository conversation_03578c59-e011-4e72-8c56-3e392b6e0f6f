﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">iPhoneSimulator</Platform>
    <ProjectTypeGuids>{1E2E965C-F6D2-49ED-B86E-418A60C69EEF};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <ProjectGuid>{57544D52-4285-4F2B-AEEF-ADCB4D044951}</ProjectGuid>
    <TemplateGuid>{22c3201b-89f4-43a0-a2fc-a31418d6fe44}</TemplateGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>DrMuscleWatch.WatchOSExtension</RootNamespace>
    <AssemblyName>DrMuscleWatchWatchOSExtension</AssemblyName>
    <IPhoneResourcePrefix>Resources</IPhoneResourcePrefix>
    <MtouchEnableSGenConc>true</MtouchEnableSGenConc>
    <MtouchHttpClientHandler>NSUrlSessionHandler</MtouchHttpClientHandler>
    <RestoreProjectStyle>PackageReference</RestoreProjectStyle>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|iPhoneSimulator' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\iPhoneSimulator\Debug</OutputPath>
    <DefineConstants>DEBUG;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodesignKey>iPhone Developer</CodesignKey>
    <MtouchDebug>true</MtouchDebug>
    <MtouchFastDev>true</MtouchFastDev>
    <MtouchLink>SdkOnly</MtouchLink>
    <MtouchArch>i386, x86_64</MtouchArch>
    <DeviceSpecificBuild>false</DeviceSpecificBuild>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|iPhone' ">
    <Optimize>true</Optimize>
    <OutputPath>bin\iPhone\Release</OutputPath>
    <DefineConstants>
    </DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodesignKey>iPhone Distribution: Dr. Muscle (7AAXZ47995)</CodesignKey>
    <MtouchUseLlvm>true</MtouchUseLlvm>
    <MtouchFloat32>true</MtouchFloat32>
    <MtouchEnableBitcode>true</MtouchEnableBitcode>
    <CodesignEntitlements>Entitlements.plist</CodesignEntitlements>
    <MtouchLink>SdkOnly</MtouchLink>
    <MtouchArch>ARMv7k, ARM64_32</MtouchArch>
    <CodesignProvision>Jignesh iOS Distribution Dr. Muscle WatchExtension</CodesignProvision>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|iPhoneSimulator' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\iPhoneSimulator\Release</OutputPath>
    <DefineConstants>
    </DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodesignKey>iPhone Developer</CodesignKey>
    <MtouchLink>None</MtouchLink>
    <MtouchArch>i386</MtouchArch>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|iPhone' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\iPhone\Debug</OutputPath>
    <DefineConstants>DEBUG;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodesignKey>iPhone Distribution: Dr. Muscle (7AAXZ47995)</CodesignKey>
    <DeviceSpecificBuild>true</DeviceSpecificBuild>
    <MtouchDebug>true</MtouchDebug>
    <MtouchFloat32>true</MtouchFloat32>
    <CodesignEntitlements>Entitlements.plist</CodesignEntitlements>
    <MtouchLink>SdkOnly</MtouchLink>
    <MtouchArch>ARMv7k</MtouchArch>
    <IOSDebugOverWiFi>true</IOSDebugOverWiFi>
    <IOSDebuggerPort>10001</IOSDebuggerPort>
    <CodesignProvision>Jignesh iOS Distribution Dr. Muscle WatchExtension</CodesignProvision>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="Xamarin.WatchOS" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Resources\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Info.plist" />
    <None Include="Entitlements.plist" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ExtensionDelegate.cs" />
    <Compile Include="InterfaceController.cs" />
    <Compile Include="InterfaceController.designer.cs">
      <DependentUpon>InterfaceController.cs</DependentUpon>
    </Compile>
    <Compile Include="NotificationController.cs" />
    <Compile Include="NotificationController.designer.cs">
      <DependentUpon>NotificationController.cs</DependentUpon>
    </Compile>
    <Compile Include="SessionManager\WCSessionManager.cs" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="PushNotificationPayload.json" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json">
      <Version>12.0.3</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\DrMuscleWebApiSharedModel\DrMuscleWebApiSharedModel\DrMuscleWebApiSharedModel.csproj">
      <Project>{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}</Project>
      <Name>DrMuscleWebApiSharedModel</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\WatchOS\Xamarin.WatchOS.AppExtension.CSharp.targets" />
</Project>