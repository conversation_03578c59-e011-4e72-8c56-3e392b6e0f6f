import XCTest
import Swift<PERSON>
@testable import DrMuscleWatchApp

class RIRPickerTests: XCTestCase {
    
    func testRIRPickerInitialState() {
        // Given
        let selectedRIR = Binding.constant(0)
        var confirmedRIR: Int?
        
        // When
        let picker = RIRPicker(selectedRIR: selectedRIR) { rir in
            confirmedRIR = rir
        }
        
        // Then
        XCTAssertEqual(selectedRIR.wrappedValue, 0, "Initial RIR value should be 0")
        XCTAssertNil(confirmedRIR, "Confirmed RIR should be nil initially")
    }
    
    func testRIRPickerOptions() {
        // Given
        let selectedRIR = Binding.constant(0)
        
        // When
        let picker = RIRPicker(selectedRIR: selectedRIR) { _ in }
        
        // Then
        // Access the private property using Mirror
        let mirror = Mirror(reflecting: picker)
        if let rirOptions = mirror.children.first(where: { $0.label == "rirOptions" })?.value as? [(text: String, value: Int)] {
            XCTAssertEqual(rirOptions.count, 5, "Should have 5 RIR options")
            XCTAssertEqual(rirOptions[0].text, "Very hard (0 left)", "First option should be 'Very hard (0 left)'")
            XCTAssertEqual(rirOptions[0].value, 0, "First option value should be 0")
            XCTAssertEqual(rirOptions[1].text, "Could do 1-2 more", "Second option should be 'Could do 1-2 more'")
            XCTAssertEqual(rirOptions[1].value, 1, "Second option value should be 1")
            XCTAssertEqual(rirOptions[2].text, "Could do 3-4 more", "Third option should be 'Could do 3-4 more'")
            XCTAssertEqual(rirOptions[2].value, 3, "Third option value should be 3")
            XCTAssertEqual(rirOptions[3].text, "Could do 5-6 more", "Fourth option should be 'Could do 5-6 more'")
            XCTAssertEqual(rirOptions[3].value, 5, "Fourth option value should be 5")
            XCTAssertEqual(rirOptions[4].text, "Could do 7+ more", "Fifth option should be 'Could do 7+ more'")
            XCTAssertEqual(rirOptions[4].value, 7, "Fifth option value should be 7")
        } else {
            XCTFail("Could not access rirOptions property")
        }
    }
    
    func testRIRPickerSelection() {
        // Given
        let selectedRIR = Binding.constant(0)
        var confirmedRIR: Int?
        
        // When
        let picker = RIRPicker(selectedRIR: selectedRIR) { rir in
            confirmedRIR = rir
        }
        
        // Then
        // Simulate tapping on the "Could do 3-4 more" option
        // In a real test environment, we would use ViewInspector or similar to tap the button
        // For now, we'll just verify the structure of the picker
        let mirror = Mirror(reflecting: picker)
        if let rirOptions = mirror.children.first(where: { $0.label == "rirOptions" })?.value as? [(text: String, value: Int)] {
            let option = rirOptions[2] // "Could do 3-4 more"
            XCTAssertEqual(option.text, "Could do 3-4 more", "Third option should be 'Could do 3-4 more'")
            XCTAssertEqual(option.value, 3, "Third option value should be 3")
        } else {
            XCTFail("Could not access rirOptions property")
        }
    }
}
