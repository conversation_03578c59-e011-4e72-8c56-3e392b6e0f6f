using Acr.UserDialogs;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Screens.Workouts;
using DrMuscleWebApiSharedModel;
using Microsoft.Maui.Controls;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using ConfirmConfig = Acr.UserDialogs.ConfirmConfig;
using UserDialogs = Acr.UserDialogs.UserDialogs;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Views;
using RGPopup.Maui.Services;
using DrMaxMuscle.Utility;
namespace DrMaxMuscle.Screens.Workouts;

public partial class ChooseYourHomeWorkoutPage : ContentPage
{
    private List<WorkoutTemplateModel> workouts;
    private List<WorkoutTemplateGroupModel> workoutGroups;
    public ObservableCollection<WorkoutTemplateModel> workoutItems = new ObservableCollection<WorkoutTemplateModel>();
    public ObservableCollection<WorkoutTemplateModel> workoutItems2 = new ObservableCollection<WorkoutTemplateModel>();

    public ObservableRangeCollection<ProgramGroupSection> ExeList { get; set; }
      = new ObservableRangeCollection<ProgramGroupSection>();

    GetUserWorkoutTemplateGroupResponseModel programWorkout;

    public ChooseYourHomeWorkoutPage()
    {
        InitializeComponent();

        //WorkoutListView.ItemsSource = workoutItems;
        //ProgramListView.ItemsSource = workoutItems2;

        //WorkoutListView.ItemTapped += WorkoutListView_ItemTapped;
        //ProgramListView.ItemTapped += WorkoutListView_ItemTapped;

        ExpandableList.ItemsSource = ExeList;
        ExpandableList.ItemTapped += WorkoutListView_ItemTapped;

        if (LocalDBManager.Instance.GetDBSetting("WorkoutTypeList") == null)
            LocalDBManager.Instance.SetDBSetting("WorkoutTypeList", "0");

        if (LocalDBManager.Instance.GetDBSetting("WorkoutTypeListDayPerWeek") == null)
            LocalDBManager.Instance.SetDBSetting("WorkoutTypeListDayPerWeek", "0");

        if (LocalDBManager.Instance.GetDBSetting("WorkoutOrderList") == null)
            LocalDBManager.Instance.SetDBSetting("WorkoutOrderList", "0");

        RefreshLocalized();
        MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
        {
            RefreshLocalized();
        });
    }

    private void RefreshLocalized()
    {
        Title = AppResources.ChooseWorkouts;
    }

    public async void OnBeforeShow()
    {
        // Uncomment code please
        //DependencyService.Get<IFirebase>().SetScreenName("choose_home_workout");
        try
        {
            if (LocalDBManager.Instance.GetDBSetting("Equipment") == null)
                LocalDBManager.Instance.SetDBSetting("Equipment", "false");

            if (LocalDBManager.Instance.GetDBSetting("ChinUp") == null)
                LocalDBManager.Instance.SetDBSetting("ChinUp", "true");

            if (LocalDBManager.Instance.GetDBSetting("Dumbbell") == null)
                LocalDBManager.Instance.SetDBSetting("Dumbbell", "true");

            if (LocalDBManager.Instance.GetDBSetting("Plate") == null)
                LocalDBManager.Instance.SetDBSetting("Plate", "true");

            if (LocalDBManager.Instance.GetDBSetting("Pully") == null)
                LocalDBManager.Instance.SetDBSetting("Pully", "true");
            if (programWorkout == null)
            {
                if (LocalDBManager.Instance.GetDBSetting("Equipment").Value == "true")
                {
                    programWorkout = await DrMuscleRestClient.Instance.GetOnlyCustomizedSystemWorkoutGroup(
                        new EquipmentModel()
                        {
                            IsEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("Equipment").Value == "true",
                            IsChinUpBarEnabled = LocalDBManager.Instance.GetDBSetting("ChinUp").Value == "true",
                            IsPullyEnabled = LocalDBManager.Instance.GetDBSetting("Pully").Value == "true",
                            IsPlateEnabled = LocalDBManager.Instance.GetDBSetting("Plate").Value == "true",
                            IsDumbbellEnabled = LocalDBManager.Instance.GetDBSetting("Dumbbell").Value == "true"

                        });
                }
                else
                    programWorkout = await DrMuscleRestClient.Instance.GetOnlySystemWorkoutGroup();
            }

            await UpdateWorkoutList();
        }
        catch (Exception e)
        {
            // Uncomment code please
            //ConnectionErrorPopup();
        }
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();

        // Ensure that the platform view is not accessed too early
        Device.BeginInvokeOnMainThread(async () =>
        {
            if (Config.ShowHomeGymPopup == false)
            {
            if (App.IsHomeGymPopup)
                return;
            App.IsHomeGymPopup = true;

            var ShowPopUp = await HelperClass.DisplayCustomPopup("Home gym workouts",
            "Choose a level. Exercises vary more often on higher levels (A/B/C workouts). Save a workout to change program. Visit the Learn tab to get custom tips for your level.",
            AppResources.GotIt,AppResources.RemindMe);
            ShowPopUp.ActionSelected += (sender,action) => {
                if(action == PopupAction.OK){
                    Config.ShowHomeGymPopup = true;
                }
                else
                {
                    Config.ShowHomeGymPopup = false;
                }
            };

            // ConfirmConfig ShowPopUp = new ConfirmConfig()
            // {
            //     Title = "Home gym workouts",
            //     Message = "Choose a level. Exercises vary more often on higher levels (A/B/C workouts). Save a workout to change program. Visit the Learn tab to get custom tips for your level.",
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     OkText = AppResources.GotIt,
            //     CancelText = AppResources.RemindMe,
            //     OnAction = async (bool ok) =>
            //     {
            //         if (ok)
            //         {
            //             Config.ShowHomeGymPopup = true;
            //         }
            //         else
            //         {
            //             Config.ShowHomeGymPopup = false;
            //         }
            //     }
            // };
            // await Task.Delay(100);
            // UserDialogs.Instance.Confirm(ShowPopUp);
        }
        });
    }


    private async Task UpdateWorkoutList()
    {
        

        if (programWorkout == null || programWorkout.WorkoutOrders == null)
            return;
        ExeList.Clear();

        var bodyweightGroups = programWorkout.WorkoutOrders.Where(x => x.Label.Contains("[Home]")).ToList();

        foreach (var groupItem in bodyweightGroups)
        {
            var bodyPartShoulders = new WorkoutTemplateGroupModel() { Label = groupItem.Label };
            bodyPartShoulders.WorkoutTemplates = new List<WorkoutTemplateModel>();
            bodyPartShoulders.WorkoutTemplates.AddRange(groupItem.WorkoutTemplates);

            ProgramGroupSection section = new ProgramGroupSection(bodyPartShoulders, false);
            ExeList.Add(section);
        }

        // Fin du dup

    }

    void Section_Tapped(object sender, TappedEventArgs e)
    {
        var obj = (ProgramGroupSection)((StackLayout)sender).BindingContext;
        obj.Expanded = !obj.Expanded;
        try
        {
            var itemsSource = ExpandableList.ItemsSource;
            ExpandableList.ItemsSource = null;
            ExpandableList.ItemsSource = itemsSource;

            // Force the layout to update
            foreach (var item in ExpandableList.TemplatedItems)
            {
                if (item is ViewCell viewCell)
                {
                    viewCell.ForceUpdateSize();
                }
            }
            
        }
        catch (Exception ex)
        {

        }
    }
    private void StateImage_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        if (e.PropertyName.Equals("Source"))
        {
            var image = sender as Image;
            image.Opacity = 0;
            image.FadeTo(1, 1000);
        }
    }

    public void OnReset(object sender, EventArgs e)
    {
        try
        {
            var mi = ((Button)sender);
            if (mi.CommandParameter is WorkoutTemplateModel)
            {
                WorkoutTemplateModel m = (WorkoutTemplateModel)mi.CommandParameter;
                CurrentLog.Instance.WorkoutTemplateSettings = m;
                OnCancelClicked(sender, e);
            }
            Navigation.PushAsync(new WorkoutSettingsPage());
        }
        catch (Exception ex)
        {

        }
    }
    void OnCancelClicked(object sender, System.EventArgs e)
    {
        StackLayout s = ((StackLayout)((Button)sender).Parent);
        SetVisibility(s.Children[0], false);
        SetVisibility(s.Children[1], false);
        SetVisibility(s.Children[2], true);
    }
    void SetVisibility(IView view, bool isVisible)
    {
        if (view is View mauiView)
        {
            mauiView.IsVisible = isVisible;
        }
    }
    void OnContextMenuClicked(object sender, System.EventArgs e)
    {
        StackLayout s = ((StackLayout)((Button)sender).Parent);
        SetVisibility(s.Children[0], true);
        SetVisibility(s.Children[1], true);
        SetVisibility(s.Children[2], false);

    }

    public async void OnEdit(object sender, EventArgs e)
    {
        var mi = ((Button)sender);
        if (mi.CommandParameter is WorkoutTemplateModel)
        {
            WorkoutTemplateModel m = (WorkoutTemplateModel)mi.CommandParameter;
            CurrentLog.Instance.CurrentWorkoutTemplate = m;
            // Uncomment code please
            //await PagesFactory.PushAsync<AddExercisesToWorkoutPage>();
        }
        else
        {
            WorkoutTemplateGroupModel m = (WorkoutTemplateGroupModel)mi.CommandParameter;
            CurrentLog.Instance.CurrentWorkoutTemplateGroup = m;
            // Uncomment code please
            //await PagesFactory.PushAsync<AddWorkoutToWorkoutOrderPage>();
        }

    }

    private void OnBindingContextChanged(object sender, EventArgs e)
    {
        base.OnBindingContextChanged();

        if (((BindableObject)sender).BindingContext == null)
            return;
        WorkoutTemplateModel m = (WorkoutTemplateModel)((BindableObject)sender).BindingContext;
        if (m.IsSystemExercise)
            ((Cell)sender).ContextActions.Clear();

    }

    private async void WorkoutListView_ItemTapped(object sender, ItemTappedEventArgs e)
    {
        try
        {
            //if (await CanGoFurther())
            //{
            if (e.Item is WorkoutTemplateModel workoutTemplate)
            {
                if (workoutTemplate.Id != -1)
                {
                    LocalDBManager.Instance.SetDBSetting("CurrentWorkoutId", workoutTemplate.Id.ToString());
                    CurrentLog.Instance.CurrentWorkoutTemplate = workoutTemplate;
                    //if (((WorkoutTemplateModel)e.Item).Exercises.Count == 0)
                    //{
                    //    UserDialogs.Instance.AlertAsync($"No exercise exist in {((WorkoutTemplateModel)e.Item).Label} workout.", "Error!", "OK");
                    //    return;
                    //}
                    if (workoutTemplate.Exercises.Count > 0)
                        CurrentLog.Instance.WorkoutTemplateCurrentExercise = workoutTemplate.Exercises.First();
                    CurrentLog.Instance.WorkoutStarted = true;
                    try
                    {

                        if (programWorkout != null && programWorkout.WorkoutOrders != null)
                        {
                            CurrentLog.Instance.CurrentWorkoutTemplateGroup = programWorkout?.WorkoutOrders?.FirstOrDefault(x => x.WorkoutTemplates.Contains(workoutTemplate));
                        }

                    }
                    catch (Exception ex)
                    {

                    }
                    try
                    {
                        var workoutModel = LocalDBManager.Instance.GetDBSetting($"Workout{DateTime.Now.Date}{CurrentLog.Instance.CurrentWorkoutTemplate.Id}")?.Value;
                        if (!string.IsNullOrEmpty(workoutModel))
                        {
                            var model = Newtonsoft.Json.JsonConvert.DeserializeObject<WorkoutTemplateModel>(workoutModel);
                            CurrentLog.Instance.CurrentWorkoutTemplate = model;
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                    Device.BeginInvokeOnMainThread(async () =>
                    {
                        KenkoChooseYourWorkoutExercisePage page = new KenkoChooseYourWorkoutExercisePage();
                        App.ShowTopButtonWorkout = true;

                        // Ensure OnBeforeShow doesn't access uninitialized PlatformView
                        page.OnBeforeShow();

                        // Add a small delay to ensure UI thread readiness
                        await Task.Delay(100);

                        await Navigation.PushAsync(page);
                    });

                }
                else
                {
                    AddMyOwnWorkout();
                }
            }
            if (e.Item is WorkoutTemplateGroupModel workoutTemplate1)
            {
                if (workoutTemplate1.Id != -1)
                {
                    CurrentLog.Instance.CurrentWorkoutTemplateGroup = workoutTemplate1;
                    // Uncomment code please
                    //await PagesFactory.PushAsync<ChooseYourWorkoutTemplateInGroup>();
                    var page = new KenkoChooseYourWorkoutExercisePage();
                    App.ShowTopButtonWorkout = true;
                    page.OnBeforeShow();
                    await Task.Delay(100); // Allow UI thread to stabilize
                    await Navigation.PushAsync(page);
                }
                else
                {
                    AddMyOwnWorkoutTemplateOrder();
                }
            }
            //}
            //else
            //{
            //    await PagesFactory.PushAsync<SubscriptionPage>();
            //}
        }
        catch (Exception ex)
        {

        }
    }

    private async void AddMyOwnWorkoutTemplateOrder()
    {
         CustomPromptConfig p = new CustomPromptConfig(AppResources.CreateNewProgram,AppResources.CreateNewProgram,
            AppResources.CreateNew,AppResources.Cancel,
            "");

            p.ActionSelected += (sender,action) => {
                if(action == PopupAction.OK){
                    AddWorkoutOrderAction(action,p.text);
                }
            };

            await PopupNavigation.Instance.PushAsync(p);

        // PromptConfig p = new PromptConfig()
        // {
        //     InputType = InputType.Default,
        //     IsCancellable = true,
        //     Title = AppResources.CreateNewProgram,
        //     Placeholder = AppResources.CreateNewProgram,
        //     OkText = AppResources.CreateNew,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     OnAction = new Action<PromptResult>(AddWorkoutOrderAction)
        // };

        // UserDialogs.Instance.Prompt(p);
    }

    private async void AddMyOwnWorkout()
    {
        CustomPromptConfig p = new CustomPromptConfig(AppResources.CreateNewWorkout,AppResources.NameYourWorkout,
            AppResources.CreateNew,AppResources.Cancel,
            "");

            p.ActionSelected += (sender,action) => {
                if(action == PopupAction.OK){
                    AddWorkoutAction(action,p.text);
                }
            };

            await PopupNavigation.Instance.PushAsync(p);

        // PromptConfig p = new PromptConfig()
        // {
        //     InputType = InputType.Default,
        //     IsCancellable = true,
        //     Title = AppResources.CreateNewWorkout,
        //     Placeholder = AppResources.NameYourWorkout,
        //     OkText = AppResources.CreateNew,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     OnAction = new Action<PromptResult>(AddWorkoutAction)
        // };

        // UserDialogs.Instance.Prompt(p);
    }

    private async void AddWorkoutOrderAction(PopupAction response,string Text)
    {
        if (response == PopupAction.OK)
        {
            try
            {
                WorkoutTemplateGroupModel newWorkoutOrder = new WorkoutTemplateGroupModel()
                {
                    Label = Text,
                    Id = -1
                };
                /*await DrMuscleRestClient.Instance.CreateNewWorkoutTemplate(new AddUserWorkoutTemplateModel()
            {
                WorkoutLabel = response.Text
            });
            */
                CurrentLog.Instance.CurrentWorkoutTemplateGroup = newWorkoutOrder;
                // Uncomment code please
                //await PagesFactory.PushAsync<AddWorkoutToWorkoutOrderPage>();
            }
            catch (Exception e)
            {
                // Uncomment code please
                //ConnectionErrorPopup();
            }
        }
    }

    private async void AddWorkoutAction(PopupAction response,string Text)
    {
        if (response == PopupAction.OK)
        {
            try
            {
                WorkoutTemplateModel newWorkout = new WorkoutTemplateModel()
                {
                    Label = Text,
                    Id = -1
                };
                /*await DrMuscleRestClient.Instance.CreateNewWorkoutTemplate(new AddUserWorkoutTemplateModel()
            {
                WorkoutLabel = response.Text
            });
            */
                CurrentLog.Instance.CurrentWorkoutTemplate = newWorkout;
                CurrentLog.Instance.WorkoutTemplateCurrentExercise = null;
                CurrentLog.Instance.WorkoutStarted = false;
                // Uncomment code please
                //await PagesFactory.PushAsync<AddExercisesToWorkoutPage>();
            }
            catch (Exception e)
            {
                // Uncomment code please
                //ConnectionErrorPopup();
            }
        }
    }

}
