import SwiftUI

/// A custom picker component for selecting values (reps, weight, etc.)
struct CustomPicker: View {
    let title: String
    let range: ClosedRange<Int>
    @Binding var selectedValue: Int
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack {
            Text(title)
                .font(.system(size: 22, weight: .semibold))
                .foregroundColor(AppTheme.primaryTextColor)
                .padding()
            
            ScrollViewReader { scrollProxy in
                ScrollView(.vertical, showsIndicators: false) {
                    VStack {
                        ForEach(range, id: \.self) { value in
                            Text("\(value)")
                                .font(.system(size: 28, weight: .bold))
                                .padding()
                                .frame(maxWidth: .infinity)
                                .background(selectedValue == value ? 
                                    AppTheme.yellowGradientStart.opacity(0.2) : 
                                    Color.clear)
                                .cornerRadius(8)
                                .onTapGesture {
                                    selectedValue = value
                                    dismiss()
                                }
                                .id(value)
                        }
                    }
                    .onAppear {
                        // Scroll to the selected value when the view appears
                        DispatchQueue.main.async {
                            scrollProxy.scrollTo(selectedValue, anchor: .center)
                        }
                    }
                }
            }
            .padding(.horizontal, 16)
        }
        .padding()
        .background(AppTheme.backgroundColor)
    }
}
