import XCTest
import SwiftUI
@testable import DrMuscleWatchApp

class SetViewTests: XCTestCase {

    func testSetViewDisplaysExerciseDetails() {
        // Given
        let mockViewModel = MockSetViewModel()
        mockViewModel.exerciseName = "Bench Press"
        mockViewModel.currentReps = 8
        mockViewModel.currentWeight = 100
        mockViewModel.weightUnit = "lbs"

        // When
        let view = SetView(viewModel: mockViewModel)

        // Then
        // This is a basic test to ensure the view can be created with the view model
        // In a real environment, we would use ViewInspector or similar to verify the UI elements
        XCTAssertEqual(mockViewModel.exerciseName, "Bench Press", "View model should have exercise name Bench Press")
        XCTAssertEqual(mockViewModel.currentReps, 8, "View model should have 8 reps")
        XCTAssertEqual(mockViewModel.currentWeight, 100, "View model should have 100 weight")
        XCTAssertEqual(mockViewModel.weightUnit, "lbs", "View model should have lbs weight unit")
        XCTAssertFalse(mockViewModel.isLoading, "View model should not be in loading state")
        XCTAssertFalse(mockViewModel.hasError, "View model should not have an error")
    }

    func testSetViewDisplaysErrorState() {
        // Given
        let mockViewModel = MockSetViewModel()
        mockViewModel.hasError = true
        mockViewModel.errorMessage = "Failed to load exercise details"

        // When
        let view = SetView(viewModel: mockViewModel)

        // Then
        XCTAssertTrue(mockViewModel.hasError, "View model should have an error")
        XCTAssertEqual(mockViewModel.errorMessage, "Failed to load exercise details", "Error message should match")
    }

    func testSetViewDisplaysLoadingState() {
        // Given
        let mockViewModel = MockSetViewModel()
        mockViewModel.isLoading = true

        // When
        let view = SetView(viewModel: mockViewModel)

        // Then
        XCTAssertTrue(mockViewModel.isLoading, "View model should be in loading state")
        XCTAssertFalse(mockViewModel.hasError, "View model should not have an error")
    }

    func testWeightPickerUpdatesViewModel() {
        // Given
        let mockViewModel = MockSetViewModel()
        mockViewModel.exerciseName = "Bench Press"
        mockViewModel.currentReps = 8
        mockViewModel.currentWeight = 100
        mockViewModel.weightUnit = "lbs"

        // When
        let view = SetView(viewModel: mockViewModel)

        // Then
        // Initial state
        XCTAssertEqual(mockViewModel.currentWeight, 100, "Initial weight should be 100")

        // Simulate weight update through the binding
        // This simulates what happens when a user selects a new weight in the picker
        let weightBinding = Binding(
            get: { Int(mockViewModel.currentWeight) },
            set: { mockViewModel.updateWeight(Double($0)) }
        )
        weightBinding.wrappedValue = 105

        // Then
        XCTAssertEqual(mockViewModel.currentWeight, 105, "Weight should be updated to 105")

        // Verify the currentSetData is also updated
        let currentSetData = mockViewModel.getCurrentSetData()
        XCTAssertEqual(currentSetData.weight, 105, "Current set data weight should be updated to 105")
    }
}

// Mock ViewModel for testing
class MockSetViewModel: SetViewModel {
    override init(exerciseId: Int64 = 1, workoutId: Int64 = 1, apiClient: DrMuscleAPIClient = MockDrMuscleAPIClient()) {
        super.init(exerciseId: exerciseId, workoutId: workoutId, apiClient: apiClient)
    }

    override func fetchExerciseDetails() async {
        // Do nothing in the mock
    }

    override func moveToNextSet() -> Bool {
        return true
    }

    override func updateReps(_ reps: Int) {
        self.currentReps = reps
    }

    override func updateWeight(_ weight: Double) {
        self.currentWeight = weight
        // Update currentSetData to match the behavior of the real implementation
        self.currentSetData.weight = weight
    }

    override func getCurrentSetData() -> CurrentSetData {
        return currentSetData
    }
}
