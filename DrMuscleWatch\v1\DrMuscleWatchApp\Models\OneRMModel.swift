import Foundation

/// Model for one-rep max data
struct OneRMModel: Codable {
    let exerciseId: Int64
    let userId: String?
    let oneRMDate: Date
    let oneRM: WeightModel
    let reps: Int
    let weight: WeightModel
    
    enum CodingKeys: String, CodingKey {
        case exerciseId = "ExerciseId"
        case userId = "UserId"
        case oneRMDate = "OneRMDate"
        case oneRM = "OneRM"
        case reps = "Reps"
        case weight = "Weight"
    }
}

/// Model for requesting one-rep max data for an exercise
struct GetOneRMforExerciseModel: Codable {
    let exerciseId: Int64
    
    enum CodingKeys: String, CodingKey {
        case exerciseId = "ExerciseId"
    }
}
