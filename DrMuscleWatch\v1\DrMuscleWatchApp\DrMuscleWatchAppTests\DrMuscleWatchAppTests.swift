//
//  DrMuscleWatchAppTests.swift
//  DrMuscleWatchAppTests
//
//  Created on May 1, 2024
//

import XCTest
import CoreData
@testable import DrMuscleWatchApp

final class DrMuscleWatchAppTests: XCTestCase {

    // Test that the app initializes correctly
    func testAppInitialization() {
        // Given a new watchOS app is created using Swift and SwiftUI
        let app = DrMuscleWatchApp()

        // When we access the app's body
        let _ = app.body

        // Then the app should not be nil (this is a basic test to ensure the app compiles)
        XCTAssertNotNil(app)
    }

    // Test that the ContentView initializes correctly
    func testContentViewInitialization() {
        // Given the project structure is initialized and a managed object context is available
        let persistenceController = PersistenceController.preview
        let contentView = ContentView()
            .environment(\.managedObjectContext, persistenceController.container.viewContext)

        // When we access the view's body
        let _ = contentView.body

        // Then the ContentView should not be nil
        XCTAssertNotNil(contentView)
    }

    // Test that the PersistenceController initializes correctly
    func testPersistenceControllerInitialization() {
        // Given the application needs local persistence
        // When the Core Data stack is initialized
        let persistenceController = PersistenceController.shared

        // Then the persistent container loads successfully without errors
        XCTAssertNotNil(persistenceController.container, "Persistent container should not be nil")
        XCTAssertEqual(persistenceController.container.name, "DrMuscleData", "Container name should be 'DrMuscleData'")

        // And the view context is configured correctly
        XCTAssertTrue(persistenceController.container.viewContext.automaticallyMergesChangesFromParent, "View context should automatically merge changes from parent")
        XCTAssertTrue(persistenceController.container.viewContext.mergePolicy is NSMergeByPropertyObjectTrumpMergePolicy, "View context should use NSMergeByPropertyObjectTrumpMergePolicy")
    }
}
