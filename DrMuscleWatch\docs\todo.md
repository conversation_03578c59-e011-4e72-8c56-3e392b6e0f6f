# Dr. Muscle Apple Watch App - Task List (V1)

## Purpose

This file serves as the main entry point for the development tasks for the V1 Dr. Muscle Apple Watch app. It outlines the structure of the detailed task lists.

## Project Organization

The project is organized into two main directories:

- **v0/**: Contains the old implementation (Xamarin-based) with folders:
  - DrMuscleWatch.WatchOSApp
  - DrMuscleWatch.WatchOSExtension

- **v1/**: Contains the new implementation (Swift/SwiftUI-based) with folder:
  - DrMuscleWatchApp

This organization preserves the old implementation for reference while clearly separating it from the new implementation.

## Instructions

*   The detailed development tasks, including specific BDD Acceptance Criteria (User Stories), are broken down into logical groups in separate files within the `docs/todos/` directory.
*   Refer to the files below for the specific tasks relevant to different functional areas of the application.
*   Follow the development workflow outlined in `docs/rules.md` when tackling tasks from these files.
*   Use `docs/status.md` to track the *currently active* task, regardless of which file it originates from.
*   For API-related tasks, refer to `docs/api-reference.md` for detailed endpoint specifications, data models, and implementation patterns.
*   For UI-related tasks, refer to `docs/ui-components.md` for detailed component documentation, styling guidelines, and implementation patterns.

## Detailed Task & Implementation Files

1.  **`docs/todos/core.md`:** Covers foundational setup, authentication, basic API client structure, and local storage setup. Start here for core infrastructure.
2.  **`docs/todos/workout.md`:** Contains all tasks directly related to the user's active workout flow – from selecting a workout to completing the last set. This covers the main user-facing workout experience.
3.  **`docs/todos/background.md`:** Focuses on supporting background processes and integrations like offline handling, sync, HealthKit, and general error display. These tasks support the core workout flow.
4.  **`docs/todos/ui-implementation.md`:** Documents the UI components that have been implemented, particularly for the Set screen, including reusable components, style guide, and documentation. This file provides specific locations for all UI components in the codebase. See the [Set screen screenshot](../dr-muscle-watch-app.png) for reference.

