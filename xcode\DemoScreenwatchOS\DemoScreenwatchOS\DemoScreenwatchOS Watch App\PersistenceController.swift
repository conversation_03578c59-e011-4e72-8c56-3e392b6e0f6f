import CoreData

struct PersistenceController {
    static let shared = PersistenceController()

    let container: NSPersistentContainer

    init() {
        container = NSPersistentContainer(name: "Exercise") // Replace "ModelName" with your actual Core Data model name.
        container.loadPersistentStores { description, error in
            if let error = error {
                fatalError("Unresolved error \(error), \(error.localizedDescription)")
            }
        }
    }

    // Save function to add new exercises dynamically
    func addExercise(exerciseName: String, reps: Int32, lbs: Double) {
        let context = container.viewContext
        let newExercise = Exercise(context: context) // Replace `Exercise` with your actual entity class
        newExercise.exerciseName = exerciseName
        newExercise.reps = reps
        newExercise.lbs = lbs

        do {
            try context.save()
        } catch {
            let nsError = error as NSError
            fatalError("Unresolved error \(nsError), \(nsError.userInfo)")
        }
    }
}
