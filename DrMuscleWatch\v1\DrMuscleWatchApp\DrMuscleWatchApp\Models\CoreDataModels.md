# Core Data Models

This document describes the Core Data models used in the Dr. Muscle Watch app.

## Entities

### Workout

Represents a workout template or a completed workout.

| Attribute | Type | Description |
|-----------|------|-------------|
| id | Int64 | The unique identifier for the workout, matching the ID from the API |
| name | String | The name of the workout |
| timestamp | Date | The date and time when the workout was performed |
| isCompleted | Boolean | Whether the workout has been completed |
| needsSync | Boolean | Whether the workout needs to be synced with the server |

**Relationships:**
- `exercises`: One-to-many relationship to Exercise entities
- `setLogs`: One-to-many relationship to SetLog entities

### Exercise

Represents an exercise within a workout.

| Attribute | Type | Description |
|-----------|------|-------------|
| id | Int64 | The unique identifier for the exercise, matching the ID from the API |
| name | String | The name of the exercise |
| workoutID | Int64 | The ID of the workout this exercise belongs to |
| isBodyweight | Boolean | Whether this is a bodyweight exercise |

**Relationships:**
- `workout`: Many-to-one relationship to the Workout entity
- `setLogs`: One-to-many relationship to SetLog entities

### SetLog

Represents a completed set of an exercise.

| Attribute | Type | Description |
|-----------|------|-------------|
| exerciseID | Int64 | The ID of the exercise this set belongs to |
| workoutID | Int64 | The ID of the workout this set belongs to |
| reps | Int16 | The number of repetitions performed |
| weight | Double | The weight used |
| weightUnit | String | The unit of the weight (e.g., "kg", "lb") |
| rir | Int16 | Reps in reserve (optional) |
| timestamp | Date | The date and time when the set was performed |
| isWarmup | Boolean | Whether this is a warmup set |
| needsSync | Boolean | Whether the set needs to be synced with the server |

**Relationships:**
- `exercise`: Many-to-one relationship to the Exercise entity
- `workout`: Many-to-one relationship to the Workout entity

## Usage

The Core Data models are managed by the `PersistenceController` class, which provides the Core Data stack. The `StorageService` class provides a higher-level API for working with the Core Data models.

### Example: Saving a Workout

```swift
do {
    let workout = try storageService.saveWorkout(
        id: 123,
        name: "Push Day",
        timestamp: Date()
    )
    // Use the workout...
} catch {
    // Handle error...
}
```

### Example: Saving an Exercise

```swift
do {
    let exercise = try storageService.saveExercise(
        id: 456,
        name: "Bench Press",
        workoutID: 123
    )
    // Use the exercise...
} catch {
    // Handle error...
}
```

### Example: Saving a Set Log

```swift
do {
    let setLog = try storageService.saveSetLog(
        exerciseID: 456,
        workoutID: 123,
        reps: 8,
        weight: 100.0,
        weightUnit: "kg",
        rir: 2,
        timestamp: Date()
    )
    // Use the set log...
} catch {
    // Handle error...
}
```

### Example: Fetching Set Logs for an Exercise

```swift
do {
    let setLogs = try storageService.getSetLogsForExercise(exerciseID: 456)
    // Use the set logs...
} catch {
    // Handle error...
}
```

### Example: Fetching Set Logs That Need Sync

```swift
do {
    let setLogsNeedingSync = try storageService.getSetLogsNeedingSync()
    // Sync the set logs...
} catch {
    // Handle error...
}
```

### Example: Marking a Set Log as Synced

```swift
do {
    try storageService.markSetLogAsSynced(setLog)
    // Set log is now marked as synced
} catch {
    // Handle error...
}
```

### Example: Deleting a Workout

```swift
do {
    try storageService.deleteWorkout(workoutID: 123)
    // Workout and associated exercises and set logs are deleted
} catch {
    // Handle error...
}
```
