<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>SchemeUserState</key>
	<dict>
		<key>DemoScreenwatchOS Watch App.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>0</integer>
		</dict>
		<key>SQLite (Playground) 1.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>2</integer>
		</dict>
		<key>SQLite (Playground) 2.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>3</integer>
		</dict>
		<key>SQLite (Playground).xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>1</integer>
		</dict>
		<key>WatchApp.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>4</integer>
		</dict>
	</dict>
	<key>SuppressBuildableAutocreation</key>
	<dict>
		<key>6EAD2CF22D2FC700005C2800</key>
		<dict>
			<key>primary</key>
			<true/>
		</dict>
		<key>6EAD2CF82D2FC700005C2800</key>
		<dict>
			<key>primary</key>
			<true/>
		</dict>
		<key>6EAD2D0A2D2FC703005C2800</key>
		<dict>
			<key>primary</key>
			<true/>
		</dict>
		<key>6EAD2D142D2FC703005C2800</key>
		<dict>
			<key>primary</key>
			<true/>
		</dict>
	</dict>
</dict>
</plist>
