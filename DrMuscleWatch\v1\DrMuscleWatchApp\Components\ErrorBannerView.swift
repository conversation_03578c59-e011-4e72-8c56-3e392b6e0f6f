import SwiftUI

/// A banner view for displaying error messages
struct ErrorBannerView: View {
    /// The error message to display
    let message: String
    
    /// The action to perform when the retry button is tapped
    let action: (() -> Void)?
    
    /// Whether the banner should auto-dismiss
    let autoDismiss: Bool
    
    /// The delay in seconds after which to dismiss the banner
    let dismissAfter: TimeInterval
    
    /// Whether the banner is visible
    @State var isVisible: Bool = true
    
    /// Initialize the error banner view
    /// - Parameters:
    ///   - message: The error message to display
    ///   - action: The action to perform when the retry button is tapped (optional)
    ///   - autoDismiss: Whether the banner should auto-dismiss
    ///   - dismissAfter: The delay in seconds after which to dismiss the banner
    init(
        message: String,
        action: (() -> Void)? = nil,
        autoDismiss: Bool = false,
        dismissAfter: TimeInterval = 3.0
    ) {
        self.message = message
        self.action = action
        self.autoDismiss = autoDismiss
        self.dismissAfter = dismissAfter
    }
    
    var body: some View {
        if isVisible {
            VStack(spacing: 8) {
                HStack(alignment: .top, spacing: 8) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.red)
                    
                    Text(message)
                        .font(.footnote)
                        .foregroundColor(AppTheme.primaryTextColor)
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                    
                    Button(action: {
                        withAnimation {
                            isVisible = false
                        }
                    }) {
                        Image(systemName: "xmark")
                            .font(.footnote)
                            .foregroundColor(AppTheme.secondaryTextColor)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                
                if let action = action {
                    Button("Retry") {
                        withAnimation {
                            isVisible = false
                        }
                        action()
                    }
                    .font(.footnote.bold())
                    .foregroundColor(AppTheme.accentBlueColor)
                    .padding(.vertical, 4)
                    .padding(.horizontal, 12)
                    .background(
                        Capsule()
                            .strokeBorder(AppTheme.accentBlueColor, lineWidth: 1)
                    )
                }
            }
            .padding(10)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.black.opacity(0.8))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .strokeBorder(Color.red.opacity(0.6), lineWidth: 1)
            )
            .padding(.horizontal, 8)
            .transition(.move(edge: .top).combined(with: .opacity))
            .onAppear {
                if autoDismiss {
                    DispatchQueue.main.asyncAfter(deadline: .now() + dismissAfter) {
                        withAnimation {
                            isVisible = false
                        }
                    }
                }
            }
        }
    }
}

/// A modifier to add an error banner to a view
struct ErrorBannerModifier: ViewModifier {
    /// The error handling service
    @ObservedObject var errorService: ErrorHandlingService
    
    func body(content: Content) -> some View {
        ZStack(alignment: .top) {
            content
            
            if errorService.isShowingError, let errorMessage = errorService.currentError {
                ErrorBannerView(
                    message: errorMessage,
                    action: nil,
                    autoDismiss: true
                )
                .zIndex(100) // Ensure the banner appears above other content
                .transition(.move(edge: .top).combined(with: .opacity))
            }
        }
        .animation(.easeInOut, value: errorService.isShowingError)
    }
}

/// Extension to add the error banner modifier to any view
extension View {
    /// Add an error banner to the view
    /// - Parameter errorService: The error handling service
    /// - Returns: The view with the error banner
    func withErrorBanner(errorService: ErrorHandlingService = .shared) -> some View {
        self.modifier(ErrorBannerModifier(errorService: errorService))
    }
}

#Preview {
    VStack {
        ErrorBannerView(
            message: "Failed to load workouts. Please check your connection.",
            action: {
                print("Retry tapped")
            }
        )
        
        ErrorBannerView(
            message: "Network connection lost. Your progress has been saved locally.",
            autoDismiss: true
        )
    }
    .padding()
    .background(AppTheme.backgroundColor)
}
