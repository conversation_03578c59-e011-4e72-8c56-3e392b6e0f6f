﻿<?xml version="1.0" encoding="utf-8" ?>
<toolkit:PopupPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
               xmlns:toolkit="clr-namespace:RGPopup.Maui.Pages;assembly=RGPopup.Maui"
                xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
                xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             x:Class="DrMaxMuscle.Views.GeneralPopup">

    <StackLayout
        x:Name="mainStack"
        Padding="0"
        BackgroundColor="Transparent"
        Margin="0"
        VerticalOptions="CenterAndExpand"
        HorizontalOptions="FillAndExpand">
        <Frame  
            
            Padding="0"
            CornerRadius="4"
            HasShadow="False"
            Margin="20,0,20,0"
            BorderColor="Transparent"
        HorizontalOptions="FillAndExpand"
        VerticalOptions="FillAndExpand"
        BackgroundColor="White"
        >
        <Grid
            BackgroundColor="White"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="CenterAndExpand"
            Padding="0,20,0,15">

            <Frame
                Padding="2,10,2,10"
                BorderColor="Transparent"
                Margin="0,10,0,0"
                CornerRadius="6"
                HasShadow="False"
                IsClippedToBounds="True"
                HorizontalOptions="FillAndExpand">
                <StackLayout
                    x:Name="stackOne"
                    Padding="0,0,0,24">

                    <BoxView Margin="0,0,0,0" BackgroundColor="Transparent" />
                        <ffimageloading:CachedImage Margin="0,0,0,0" x:Name="ImgName" WidthRequest="100" HeightRequest="100" ErrorPlaceholder="backgroundblack.png" HorizontalOptions="Center" VerticalOptions="Start" Source="truestate.png" />

                    <Label  Margin="0,15,0,0" Text="Success!" x:Name="LblHeading" HorizontalOptions="Center" FontSize="26" FontAttributes="Bold" TextColor="Black" HorizontalTextAlignment="Center" />
                    <Label  Text="Account created" x:Name="LblSubHead" Margin="15,0,15,0" HorizontalOptions="Center"  HorizontalTextAlignment="Center" FontSize="17" TextColor="#505050" FontAttributes="Bold"/>
                    <Button Text="Learn more"  x:Name="BtnLearnMore"   Clicked="DrMuscleButton_Clicked" BackgroundColor="Transparent" TextColor="#007aff" HeightRequest="66" />
                    <StackLayout
                        Padding="0"
                        x:Name="OkAction"
                        IsClippedToBounds="true"
                        VerticalOptions="EndAndExpand"
                        HorizontalOptions="FillAndExpand"
                        Margin="25,10,25,0"
                        HeightRequest="66">
                            <StackLayout.Background>
                                <LinearGradientBrush EndPoint="1,0">
                                    <GradientStop Color="#0C2432" Offset="0.0" />
                                    <GradientStop Color="#195276" Offset="1.0" />
                                </LinearGradientBrush>
                            </StackLayout.Background>
                            <Label
                        x:Name="OkButton"
                        Text="OK cool"
                        FontAttributes="Bold"
                        BackgroundColor="Transparent"
                        VerticalTextAlignment="Center"
                        TextColor="White"
                            VerticalOptions="CenterAndExpand"
                        HorizontalTextAlignment="Center"
                        HorizontalOptions="FillAndExpand"
                        Margin="0"
                         />

                    </StackLayout>
                    <Button Text="Cancel" x:Name="BtnCancel" Clicked="DrMuscleButtonCancel_Clicked" BackgroundColor="Transparent" TextColor="#007aff" HeightRequest="66" IsVisible="false" />

                    <Label Text="" x:Name="LblTipText" Margin="15,0,15,0"
HeightRequest="66"
HorizontalOptions="Center"
                          VerticalOptions="Center" VerticalTextAlignment="Center" HorizontalTextAlignment="Center" FontSize="17" TextColor="#505050" FontAttributes="Bold"/>
                    <Label Text="" IsVisible="False" HeightRequest="30" x:Name="LblCountDown" Margin="15,0,15,0"
HorizontalOptions="Center"
                          VerticalOptions="Center" VerticalTextAlignment="Center" HorizontalTextAlignment="Center" FontSize="15" TextColor="#505050" FontAttributes="Bold"/>

                    <StackLayout
                        IsVisible="False"
                        x:Name="ContinueSkipBtnLayout"
                        Orientation="Vertical"
                        VerticalOptions="End"
                        HorizontalOptions="FillAndExpand"
                        Margin="{OnPlatform Android='14,12,14,0',iOS='14,16,14,0'}"
                        Spacing="5"
                        Padding="0,0,0,0">

                        <!-- ProgressBar -->
                        <ProgressBar x:Name="myProgressBar"
                            Progress="0"
                            HeightRequest="6"
                            BackgroundColor="LightGray"
                            ProgressColor="#195276" 
                            Margin="0,0,0,3"/>

                        <!--New Skip Button-->
                        <controls:DrMuscleImageButton
                            Text="Skip"
                            HeightRequest="60"
                            Clicked="SkipReviewBtn_Clicked"/>

                        <!--New Continue-->
                        <Border
                            Padding="0"
                            Margin="0"
                            Stroke="Transparent"
                            HorizontalOptions="FillAndExpand" 
                            Style="{StaticResource GradientBorderStyleBlue}"
                            HeightRequest="66">
                            <Button
                                x:Name="ContinueReviewBtn"
                                Text="Continue"
                                VerticalOptions="FillAndExpand"
                                HorizontalOptions="FillAndExpand"
                                IsVisible="true"
                                Style="{StaticResource highEmphasisButtonStyle}"
                                BackgroundColor="Transparent"
                                BorderColor="Transparent"
                                TextColor="White"
                                Clicked="ContinueReviewBtn_Clicked"/>
                        </Border>
                    </StackLayout>

                </StackLayout>

            </Frame>
            <!--<Image Grid.Row="0" Margin="0,25" Source="SharpCurve.png" HorizontalOptions="FillAndExpand" HeightRequest="120"  VerticalOptions="Start" Aspect="Fill" />-->
            <!--<Image Grid.Row="0" Margin="0,50,0,20" Source="" WidthRequest="50" HeightRequest="50" HorizontalOptions="Center" VerticalOptions="Start" />-->
            <!--<forms:ParticleView x:Name="MyParticleCanvas"
                  FallingParticlesPerSecond="25.0"
                  IsActive="False"
                  IsRunning="False"
                  HasFallingParticles="True"
                  VerticalOptions="FillAndExpand"
                  HorizontalOptions="FillAndExpand"
                  InputTransparent="True"/>-->
        </Grid>
    </Frame>
        
    </StackLayout>
        

</toolkit:PopupPage>
