import XCTest
@testable import DrMuscleWatchApp

class WorkoutListViewModelTests: XCTestCase {

    func testFetchWorkoutsSuccess() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockWorkouts = [
            WorkoutTemplateModel(id: 1, name: "Push Day", exercises: []),
            WorkoutTemplateModel(id: 2, name: "Pull Day", exercises: [])
        ]
        let mockWorkoutGroup = WorkoutTemplateGroupModel(id: 1, name: "My Workouts", workouts: mockWorkouts)
        mockAPIClient.mockWorkoutTemplateGroup = mockWorkoutGroup

        let viewModel = WorkoutListViewModel(apiClient: mockAPIClient)

        // When
        await viewModel.fetchWorkouts()

        // Then
        XCTAssertFalse(viewModel.isLoading, "Loading state should be false after fetch completes")
        XCTAssertFalse(viewModel.hasError, "Error state should be false after successful fetch")
        XCTAssertEqual(viewModel.workouts.count, 2, "Should have 2 workouts")
        XCTAssertEqual(viewModel.workouts[0].name, "Push Day", "First workout should be Push Day")
        XCTAssertEqual(viewModel.workouts[1].name, "Pull Day", "Second workout should be Pull Day")

        if case .loaded(let workouts) = viewModel.state {
            XCTAssertEqual(workouts.count, 2, "State should contain 2 workouts")
        } else {
            XCTFail("State should be .loaded")
        }
    }

    func testFetchWorkoutsEmpty() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockWorkoutGroup = WorkoutTemplateGroupModel(id: 1, name: "My Workouts", workouts: [])
        mockAPIClient.mockWorkoutTemplateGroup = mockWorkoutGroup

        let viewModel = WorkoutListViewModel(apiClient: mockAPIClient)

        // When
        await viewModel.fetchWorkouts()

        // Then
        XCTAssertFalse(viewModel.isLoading, "Loading state should be false after fetch completes")
        XCTAssertFalse(viewModel.hasError, "Error state should be false after successful fetch")
        XCTAssertTrue(viewModel.workouts.isEmpty, "Workouts array should be empty")
        XCTAssertTrue(viewModel.isEmptyState, "Empty state should be true when no workouts are returned")

        if case .empty = viewModel.state {
            // This is the expected state
        } else {
            XCTFail("State should be .empty")
        }
    }

    func testFetchWorkoutsError() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        mockAPIClient.shouldThrowError = true

        let viewModel = WorkoutListViewModel(apiClient: mockAPIClient)

        // When
        await viewModel.fetchWorkouts()

        // Then
        XCTAssertFalse(viewModel.isLoading, "Loading state should be false after fetch completes")
        XCTAssertTrue(viewModel.hasError, "Error state should be true after failed fetch")
        XCTAssertTrue(viewModel.workouts.isEmpty, "Workouts array should be empty on error")
        XCTAssertFalse(viewModel.isEmptyState, "Empty state should be false when there's an error")
        XCTAssertNotNil(viewModel.errorMessage, "Error message should not be nil")

        if case .error(let message) = viewModel.state {
            XCTAssertFalse(message.isEmpty, "Error message should not be empty")
        } else {
            XCTFail("State should be .error")
        }
    }

    func testInitialState() {
        // Given
        let viewModel = WorkoutListViewModel(apiClient: MockDrMuscleAPIClient())

        // Then
        XCTAssertTrue(viewModel.isLoading, "Initial state should be loading")
        XCTAssertFalse(viewModel.hasError, "Initial state should not have error")
        XCTAssertFalse(viewModel.isEmptyState, "Initial state should not be empty")
        XCTAssertTrue(viewModel.workouts.isEmpty, "Initial workouts array should be empty")

        if case .loading = viewModel.state {
            // This is the expected state
        } else {
            XCTFail("Initial state should be .loading")
        }
    }
}

// Mock API Client for testing
class MockDrMuscleAPIClient: DrMuscleAPIClient {
    var mockWorkoutTemplateGroup: WorkoutTemplateGroupModel?
    var shouldThrowError = false

    override func getUserWorkoutGroup() async throws -> WorkoutTemplateGroupModel {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 0, userInfo: [NSLocalizedDescriptionKey: "Mock API error"])
        }

        return mockWorkoutTemplateGroup ?? WorkoutTemplateGroupModel(id: 0, name: "", workouts: [])
    }
}
