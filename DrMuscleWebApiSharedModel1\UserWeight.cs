﻿using System;
namespace DrMuscleWebApiSharedModel
{
    public class UserWeight
    {
        public Decimal Weight { get; set; }
        public DateTime CreatedDate { get; set; }
        public Decimal? TargetIntake { get; set; }
        public Decimal? LastTargetIntake { get; set; }
        public Decimal? Fat { get; set; }
        public Decimal? FFM { get; set; }
        public Decimal? TrendWeight { get; set; }
        public Decimal? TrendFat { get; set; }
        public long Id {get; set; }
        public string Label { get; set; }
        public string FFMLabel { get; set; }
        public string TrendWeightLabel { get; set; }
        public string TrendFatLabel { get; set; }
        public string TrendFFMLabel { get; set; }
    }
}
