import XCTest
import Swift<PERSON>
@testable import DrMuscleWatchApp

class WorkoutDetailViewTests: XCTestCase {
    
    func testWorkoutDetailViewDisplaysWorkoutDetails() {
        // Given
        let mockExercises = [
            ExerciseModel(id: 1, name: "Bench Press", sets: []),
            ExerciseModel(id: 2, name: "Overhead Press", sets: [])
        ]
        let mockWorkout = WorkoutTemplateModel(id: 1, name: "Push Day", exercises: mockExercises)
        
        let mockViewModel = MockWorkoutDetailViewModel()
        mockViewModel.workout = mockWorkout
        
        // When
        let view = WorkoutDetailView(viewModel: mockViewModel)
        
        // Then
        // This is a basic test to ensure the view can be created with the view model
        // In a real environment, we would use ViewInspector or similar to verify the UI elements
        XCTAssertEqual(mockViewModel.workout?.name, "Push Day", "View model should have workout name Push Day")
        XCTAssertEqual(mockViewModel.workout?.exercises.count, 2, "View model should have 2 exercises")
        XCTAssertFalse(mockViewModel.isLoading, "View model should not be in loading state")
        XCTAssertFalse(mockViewModel.hasError, "View model should not have an error")
    }
    
    func testWorkoutDetailViewDisplaysErrorState() {
        // Given
        let mockViewModel = MockWorkoutDetailViewModel()
        mockViewModel.hasError = true
        mockViewModel.errorMessage = "Failed to load workout details"
        
        // When
        let view = WorkoutDetailView(viewModel: mockViewModel)
        
        // Then
        XCTAssertNil(mockViewModel.workout, "View model should have no workout")
        XCTAssertTrue(mockViewModel.hasError, "View model should have an error")
        XCTAssertEqual(mockViewModel.errorMessage, "Failed to load workout details", "Error message should match")
    }
    
    func testWorkoutDetailViewDisplaysLoadingState() {
        // Given
        let mockViewModel = MockWorkoutDetailViewModel()
        mockViewModel.isLoading = true
        
        // When
        let view = WorkoutDetailView(viewModel: mockViewModel)
        
        // Then
        XCTAssertTrue(mockViewModel.isLoading, "View model should be in loading state")
        XCTAssertFalse(mockViewModel.hasError, "View model should not have an error")
        XCTAssertNil(mockViewModel.workout, "View model should have no workout while loading")
    }
}

// Mock ViewModel for testing
class MockWorkoutDetailViewModel: WorkoutDetailViewModel {
    override init(workoutId: Int = 1, apiClient: DrMuscleAPIClient = MockDrMuscleAPIClient()) {
        super.init(workoutId: workoutId, apiClient: apiClient)
    }
    
    override func fetchWorkoutDetails() async {
        // Do nothing in the mock
    }
    
    override func startWorkout() async -> Bool {
        return true
    }
}
