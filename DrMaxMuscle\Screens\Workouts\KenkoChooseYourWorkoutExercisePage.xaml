﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="DrMaxMuscle.Screens.Workouts.KenkoChooseYourWorkoutExercisePage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:cells="clr-namespace:DrMaxMuscle.Cells"
    xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
    xmlns:converter="clr-namespace:DrMaxMuscle.Convertors"
    xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:heaer="clr-namespace:DrMaxMuscle.Screens.Workouts"
    xmlns:ios="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;assembly=Microsoft.Maui.Controls"
    xmlns:t="clr-namespace:DrMaxMuscle.Layout"
    Title="KenkoChooseYourWorkoutExercisePage"
    ios:Application.HandleControlUpdatesOnMainThread="True"
    ios:Page.UseSafeArea="False"
    NavigationPage.HasNavigationBar="False">
    <ContentPage.Resources>
        <ResourceDictionary>
            <converter:IdToBodyPartConverter x:Key="IdToBodyConverter" />
            <converter:IdToTransparentBodyPartConverter x:Key="IdToTransBodyConverter" />
        </ResourceDictionary>
    </ContentPage.Resources>
    <ContentPage.Content>

        <Grid x:Name="NavGrid"
              Padding="0,0,0,8"
              BackgroundColor="#D8D8D8"
              IgnoreSafeArea="True"
              RowSpacing="0">
            <Grid.RowDefinitions>
                <RowDefinition x:Name="StatusBarHeight" Height="20" />
                <RowDefinition Height="auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Image x:Name="navView"
                   Grid.RowSpan="2"
                   Grid.ColumnSpan="2"
                   Margin="0"
                   Aspect="AspectFill"
                   Source="nav" />

            <!--Top Navbar-->
            <Grid x:Name="StackTopHeader"
                  Grid.Row="1"
                  Grid.Column="0"
                  Grid.ColumnSpan="2"
                  Padding="8,-5,8,0"
                  VerticalOptions="StartAndExpand">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!--Row 1-->
                <StackLayout Grid.ColumnSpan="2"
                             HorizontalOptions="FillAndExpand"
                             Orientation="Horizontal" 
                             Padding="0" Margin="0"
                             VerticalOptions="Start">
                    <ImageButton Clicked="Back_Clicked"
                                 Padding="-8,0,0,0"
                                 Margin="-8,0,0,0"
                                 Aspect="AspectFit"
                                 BackgroundColor="Transparent"                                    
                                 HeightRequest="{OnPlatform Android='32', iOS='35'}"
                                 HorizontalOptions="Start"
                                 Source="back_arrow.png"
                                 VerticalOptions="Center"/>
                    <Label x:Name="LblWorkoutName"
                            FontSize="24"
                            HorizontalOptions="FillAndExpand"
                            LineBreakMode="TailTruncation"
                            Style="{StaticResource BoldLabelStyle}"
                            TextColor="White"
                            VerticalOptions="Center"
                            VerticalTextAlignment="Center" />
                </StackLayout>

                <!--Row 2-->
                <StackLayout Grid.Row="1" Grid.Column="0"
                             Padding="5,0,0,0"
                             HorizontalOptions="StartAndExpand"
                             Orientation="Horizontal">
                    <Label FontSize="16"
                           HorizontalOptions="Start"
                           HorizontalTextAlignment="Start"
                           LineBreakMode="NoWrap"
                           Text="In progress"
                           TextColor="#AAFFFFFF"
                           VerticalOptions="Center"
                           VerticalTextAlignment="Center">
                        <Label.GestureRecognizers>
                            <TapGestureRecognizer Tapped="ShowAlert_Tapped" />
                        </Label.GestureRecognizers>
                    </Label>
                    <ffimageloading:CachedImage x:Name="IconOrange"
                                                Margin="{OnPlatform iOS='0,1,0,1', Android='0,1,0,-3'}"
                                                Aspect="AspectFit"
                                                ErrorPlaceholder="backgroundblack"
                                                HeightRequest="22"
                                                HorizontalOptions="Start"
                                                Source="orange"
                                                VerticalOptions="Center"
                                                WidthRequest="40">
                        <ffimageloading:CachedImage.GestureRecognizers>
                            <TapGestureRecognizer Tapped="ShowAlert_Tapped" />
                        </ffimageloading:CachedImage.GestureRecognizers>
                    </ffimageloading:CachedImage>
                </StackLayout>

                <HorizontalStackLayout Grid.Row="1" Grid.Column="1"
                                       Spacing="15">
                    <t:DrMuscleButton x:Name="BtnEditWorkout"
                                      BackgroundColor="Transparent"
                                      Clicked="NewTapped"
                                      Padding="0,5,0,5"
                                      FontSize="20"
                                      HeightRequest="44"
                                      ImageSource="ic_edit.png"
                                      TextColor="Transparent"
                                      VerticalOptions="Center"/>
                    <t:DrMuscleButton x:Name="ImgPlate"
                                      BackgroundColor="Transparent"
                                      Clicked="PlateTapped"
                                      Padding="0,5,0,5"
                                      HeightRequest="44"
                                      ImageSource="plate.png"
                                      VerticalOptions="Center"/>
                    <t:DrMuscleButton x:Name="BtnTimer"
                                      BackgroundColor="Transparent"
                                      Clicked="TimerTapped"
                                      Padding="0,5,0,5"
                                      FontSize="19"
                                      FontAttributes="Bold"
                                      HeightRequest="44"
                                      ImageSource="stopwatch.png"
                                      TextColor="White"
                                      VerticalOptions="Center"/>
                </HorizontalStackLayout>                
            </Grid>

            <Border
                x:Name="StackHeader"
                Grid.Row="2"
                Grid.Column="0"
                Grid.ColumnSpan="2"
                BackgroundColor="#D8D8D8"
                HeightRequest="4" />
            <!--<t:DrMuscleListViewCache x:Name="ExerciseListView"

                                              Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2"  Margin="0,7,0,0" HasUnevenRows="True" BackgroundColor="Transparent" SeparatorColor="Transparent" VerticalOptions="Fill" HorizontalOptions="Fill" SeparatorVisibility="None" IsGroupingEnabled="True" Header="{Binding}" ItemTemplate="{StaticResource SetDataTemplateSelector}" GroupHeaderTemplate="{StaticResource kenkoHeaderDataTemplateSelector}" RowHeight="-1" ios:ListView.GroupHeaderStyle="Grouped"
                                             >


    </t:DrMuscleListViewCache>-->
            <ScrollView
                x:Name="ExerciseListScroll"
                Grid.Row="3"
                Grid.Column="0"
                Grid.ColumnSpan="2">
                <StackLayout>
                    <StackLayout
                        x:Name="StackResume"
                        Margin="8,10,8,0"
                        Padding="0"
                        HeightRequest="80"
                        IsVisible="false"
                        Spacing="0"
                        VerticalOptions="Start">
                        <StackLayout.Background>
                            <LinearGradientBrush>
                                <GradientStop Offset="0.0" Color="#DFFF69" />
                                <GradientStop Offset="1.0" Color="#E9FF97" />
                            </LinearGradientBrush>
                        </StackLayout.Background>
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Tapped="ResumeButton_Clicked" />
                        </StackLayout.GestureRecognizers>
                        <StackLayout
                            Margin="-26,-6,0,0"
                            BackgroundColor="Transparent"
                            HorizontalOptions="Center"
                            Orientation="Horizontal"
                            Spacing="8"
                            VerticalOptions="FillAndExpand">
                            <Image
                                x:Name="resumeImg"
                                Margin="0,0,-12,0"
                                HorizontalOptions="Center"
                                Source="resume_icon"
                                VerticalOptions="CenterAndExpand"
                                WidthRequest="50" />

                            <Label
                                x:Name="BtnResume"
                                FontAttributes="Bold"
                                FontSize="20"
                                HeightRequest="50"
                                HorizontalOptions="Center"
                                HorizontalTextAlignment="Center"
                                Text="Resume"
                                TextColor="#195377"
                                VerticalOptions="CenterAndExpand"
                                VerticalTextAlignment="Center" />

                        </StackLayout>


                    </StackLayout>
                    <StackLayout
                        x:Name="StackRestart"
                        Margin="8,10,8,0"
                        Padding="0"
                        BackgroundColor="White"
                        HeightRequest="80"
                        IsVisible="false"
                        Spacing="0"
                        VerticalOptions="Start">
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Tapped="RestartButton_Clicked" />
                        </StackLayout.GestureRecognizers>
                        <Grid Margin="0,0,0,0" VerticalOptions="CenterAndExpand">
                            <StackLayout
                                Grid.Row="0"
                                Grid.Column="0"
                                Margin="-20,-6,0,0"
                                BackgroundColor="Transparent"
                                HorizontalOptions="Center"
                                Orientation="Horizontal"
                                Spacing="8"
                                VerticalOptions="FillAndExpand">
                                <Image
                                    x:Name="RestartImg"
                                    Margin="1,15"
                                    HorizontalOptions="Center"
                                    Source="restart_icon"
                                    VerticalOptions="CenterAndExpand"
                                    WidthRequest="22" />

                                <Label
                                    x:Name="BtnRestart"
                                    FontAttributes="Bold"
                                    FontSize="20"
                                    HeightRequest="50"
                                    HorizontalOptions="Center"
                                    HorizontalTextAlignment="Center"
                                    Text="Restart"
                                    TextColor="#195377"
                                    VerticalOptions="CenterAndExpand"
                                    VerticalTextAlignment="Center" />

                            </StackLayout>


                        </Grid>
                    </StackLayout>
                    <StackLayout
                        x:Name="StackSave"
                        Margin="8,10,8,0"
                        Padding="0"
                        BackgroundColor="White"
                        HeightRequest="80"
                        IsVisible="false"
                        Spacing="0"
                        VerticalOptions="Start">
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Tapped="SaveButton_Clicked" />
                        </StackLayout.GestureRecognizers>
                        <Grid Margin="0,0,0,0" VerticalOptions="CenterAndExpand">
                            <StackLayout
                                Grid.Row="0"
                                Grid.Column="0"
                                Margin="-26,-6,0,0"
                                BackgroundColor="Transparent"
                                HorizontalOptions="Center"
                                Orientation="Horizontal"
                                Spacing="8"
                                VerticalOptions="FillAndExpand">
                                <Image
                                    x:Name="SaveImg"
                                    Margin="2,15"
                                    HorizontalOptions="Center"
                                    Source="save_icon"
                                    VerticalOptions="CenterAndExpand"
                                    WidthRequest="25" />
                                <Label
                                    x:Name="BtnSave"
                                    FontAttributes="Bold"
                                    FontSize="20"
                                    HeightRequest="50"
                                    HorizontalOptions="Center"
                                    HorizontalTextAlignment="Center"
                                    Text="Save"
                                    TextColor="#195377"
                                    VerticalOptions="CenterAndExpand"
                                    VerticalTextAlignment="Center" />

                            </StackLayout>


                        </Grid>
                    </StackLayout>

                    <StackLayout
                        x:Name="ExerciseListView"
                        Margin="0,0,0,0"
                        BindableLayout.ItemsSource="{Binding exerciseItems}"
                        Spacing="0">
                        <BindableLayout.ItemTemplate>
                            <DataTemplate>
                                <Border
                                    Margin="4,10,4,0"
                                    Padding="0"
                                    Stroke="Transparent"
                                    StrokeShape="RoundRectangle 4,4,4,4"
                                    Style="{StaticResource GradientBorderStyleBlue}">
                                    <VerticalStackLayout BackgroundColor="Transparent" Spacing="0">
                                        <StackLayout>
                                            <Grid
                                                BackgroundColor="Transparent"
                                                BindingContextChanged="OnBindingContextChanged"
                                                HeightRequest="105"
                                                IsClippedToBounds="True">
                                                <Grid.GestureRecognizers>
                                                    <TapGestureRecognizer Tapped="CellHeaderTapped" />
                                                </Grid.GestureRecognizers>


                                                <ffimageloading:CachedImage
                                                    Grid.Row="0"
                                                    Margin="4,10,4,0"
                                                    Aspect="Fill"
                                                    ErrorPlaceholder="backgroundblack"
                                                    Source="{Binding BodyPartId, Converter={StaticResource IdToTransBodyConverter}}" />


                                                <StackLayout
                                                    Grid.Row="0"
                                                    Margin="0,0,0,0"
                                                    Padding="15,0,15,0">
                                                    <StackLayout
                                                        HorizontalOptions="FillAndExpand"
                                                        Orientation="Horizontal"
                                                        Spacing="4"
                                                        VerticalOptions="CenterAndExpand">

                                                        <Image
                                                            Margin="7,0,0,0"
                                                            Aspect="AspectFit"
                                                            HorizontalOptions="Start"
                                                            IsVisible="{Binding IsFinished}"
                                                            Source="done2"
                                                            VerticalOptions="FillAndExpand"
                                                            WidthRequest="18" />
                                                        <ffimageloading:CachedImage
                                                            Aspect="AspectFit"
                                                            ErrorPlaceholder="backgroundblack"
                                                            HeightRequest="90"
                                                            Source="{Binding BodyPartId, Converter={StaticResource IdToBodyConverter}}"
                                                            WidthRequest="65" />
                                                        <StackLayout
                                                            Margin="0,0,0,0"
                                                            HorizontalOptions="FillAndExpand"
                                                            Spacing="0"
                                                            VerticalOptions="CenterAndExpand">
                                                            <controls:AutoSizeLabel
                                                                FontAttributes="Bold"
                                                                FontSize="19"
                                                                HorizontalOptions="FillAndExpand"
                                                                MaxLines="3"
                                                                TextColor="#FFFFFF"
                                                                VerticalTextAlignment="Center">
                                                                <controls:AutoSizeLabel.FormattedText>
                                                                    <FormattedString>
                                                                        <Span FontAttributes="Bold" Text="{Binding CountNo}" />
                                                                        <Span Text="&#8211;" />
                                                                        <Span FontAttributes="Bold" Text="{Binding Label}" />
                                                                    </FormattedString>
                                                                </controls:AutoSizeLabel.FormattedText>
                                                                <controls:AutoSizeLabel.Triggers>
                                                                    <DataTrigger
                                                                        Binding="{Binding IsNextExercise}"
                                                                        TargetType="controls:AutoSizeLabel"
                                                                        Value="false">
                                                                        <Setter Property="TextColor" Value="White" />
                                                                    </DataTrigger>

                                                                    <DataTrigger
                                                                        Binding="{Binding IsNextExercise}"
                                                                        TargetType="controls:AutoSizeLabel"
                                                                        Value="true">
                                                                        <Setter Property="TextColor" Value="#97D2F3" />
                                                                    </DataTrigger>
                                                                </controls:AutoSizeLabel.Triggers>
                                                            </controls:AutoSizeLabel>

                                                        </StackLayout>

                                                        <Image
                                                            Margin="3,6"
                                                            Aspect="AspectFit"
                                                            HorizontalOptions="Start"
                                                            IsVisible="{Binding IsSwapTarget}"
                                                            Source="swap.png"
                                                            VerticalOptions="Start"
                                                            WidthRequest="10" />
                                                        <StackLayout
                                                            HorizontalOptions="End"
                                                            Orientation="Horizontal"
                                                            Spacing="0"
                                                            VerticalOptions="Center">


                                                            <t:DrMuscleButton
                                                                Margin="0,0,-3,0"
                                                                BackgroundColor="Transparent"
                                                                Clicked="OnContextMenuClicked"
                                                                CommandParameter="{Binding .}"
                                                                HorizontalOptions="End"
                                                                ImageSource="menu_blue"
                                                                VerticalOptions="Center"
                                                                WidthRequest="{OnPlatform Android=60,
                                                                                          iOS=50}" />



                                                            <controls:ContextMenuButton
                                                                x:Name="MenuButton"
                                                                Margin="0,0,0,0"
                                                                HeightRequest="1"
                                                                ItemsContainerHeight="240"
                                                                ItemsContainerWidth="240"
                                                                WidthRequest="1">

                                                                <!--  // uncomment code please  -->
                                                                <controls:ContextMenuButton.Items>
                                                                    <x:Array Type="{x:Type MenuItem}" />
                                                                </controls:ContextMenuButton.Items>
                                                            </controls:ContextMenuButton>




                                                        </StackLayout>
                                                        <StackLayout.GestureRecognizers>

                                                            <TapGestureRecognizer CommandParameter="{Binding .}" Tapped="CellHeaderTapped" />
                                                        </StackLayout.GestureRecognizers>
                                                    </StackLayout>

                                                </StackLayout>

                                            </Grid>
                                        </StackLayout>
                                        <Border
                                            Margin="4,0,4,0"
                                            HorizontalOptions="FillAndExpand"
                                            Stroke="Transparent"
                                            Style="{StaticResource GradientBorderStyleBlue}"
                                            VerticalOptions="Start">
                                            <StackLayout
                                                x:Name="bind"
                                                BackgroundColor="Transparent"
                                                BindableLayout.ItemsSource="{Binding .}"
                                                Spacing="0">

                                                <BindableLayout.ItemTemplateSelector>
                                                    <cells:SetBindingTemplateSelector />
                                                </BindableLayout.ItemTemplateSelector>

                                            </StackLayout>
                                        </Border>
                                    </VerticalStackLayout>
                                </Border>
                            </DataTemplate>
                        </BindableLayout.ItemTemplate>
                    </StackLayout>
                    <StackLayout
                        x:Name="StackAddExercise"
                        Margin="8,10,8,0"
                        BackgroundColor="White"
                        HeightRequest="80"
                        IsVisible="false"
                        Spacing="0"
                        VerticalOptions="Start">

                        <t:DrMuscleButton
                            x:Name="BtnAddExercise"
                            Grid.Row="0"
                            Grid.Column="0"
                            BackgroundColor="Transparent"
                            Clicked="AddExerciseButton_Clicked"
                            CommandParameter="{Binding .}"
                            FontAttributes="Bold"
                            FontSize="20"
                            HeightRequest="70"
                            HorizontalOptions="FillAndExpand"
                            Text="Add exercise"
                            TextColor="#195377"
                            VerticalOptions="CenterAndExpand">
                            <t:DrMuscleButton.Triggers>
                                <DataTrigger
                                    Binding="{Binding IsFinished}"
                                    TargetType="t:DrMuscleButton"
                                    Value="false">
                                    <Setter Property="TextColor" Value="#195377" />
                                </DataTrigger>
                            </t:DrMuscleButton.Triggers>
                        </t:DrMuscleButton>

                    </StackLayout>
                    <StackLayout
                        x:Name="StackShareExercise1"
                        Margin="8,10,8,0"
                        Padding="0"
                        BackgroundColor="White"
                        HeightRequest="80"
                        IsVisible="false"
                        Spacing="0"
                        VerticalOptions="Start">
                        <Grid Margin="0,0,0,0" VerticalOptions="CenterAndExpand">
                            <StackLayout
                                Grid.Row="0"
                                Grid.Column="0"
                                Margin="-26,-6,0,0"
                                BackgroundColor="Transparent"
                                HorizontalOptions="Center"
                                Orientation="Horizontal"
                                Spacing="8"
                                VerticalOptions="FillAndExpand">
                                <StackLayout.GestureRecognizers>
                                    <TapGestureRecognizer NumberOfTapsRequired="1" Tapped="ShareExerciseButton_Clicked" />
                                </StackLayout.GestureRecognizers>

                                <Image
                                    x:Name="Img"
                                    Margin="0,15"
                                    HorizontalOptions="Center"
                                    Source="ic_share_exercise"
                                    VerticalOptions="CenterAndExpand"
                                    WidthRequest="25" />

                                <Label
                                    x:Name="LblText"
                                    FontAttributes="Bold"
                                    FontSize="20"
                                    HeightRequest="50"
                                    HorizontalOptions="Center"
                                    HorizontalTextAlignment="Center"
                                    Text="Share"
                                    TextColor="#195377"
                                    VerticalOptions="CenterAndExpand"
                                    VerticalTextAlignment="Center">
                                    <Label.FormattedText>
                                        <FormattedString>
                                            <Span FontAttributes="Bold" Text="Share" />
                                        </FormattedString>
                                    </Label.FormattedText>
                                </Label>
                            </StackLayout>


                        </Grid>
                    </StackLayout>

                    <StackLayout
                        x:Name="StackSets"
                        Margin="8,10,8,15"
                        BackgroundColor="White"
                        HeightRequest="80"
                        IsVisible="false"
                        Spacing="0"
                        VerticalOptions="Start">

                        <ffimageloading:CachedImage
                            Grid.Row="0"
                            Grid.Column="0"
                            Margin="-2,0"
                            Aspect="AspectFill"
                            ErrorPlaceholder="backgroundblack"
                            HorizontalOptions="FillAndExpand"
                            IsVisible="false"
                            Source="finishset_orange"
                            VerticalOptions="CenterAndExpand" />
                        <t:DrMuscleButton
                            x:Name="BtnFinishWorkout"
                            Grid.Row="0"
                            Grid.Column="0"
                            BackgroundColor="Transparent"
                            Clicked="SaveWorkoutButton_Clicked"
                            FontAttributes="Bold"
                            FontSize="20"
                            HeightRequest="50"
                            HorizontalOptions="FillAndExpand"
                            Text="Finish workout"
                            TextColor="#195377"
                            VerticalOptions="CenterAndExpand">
                            <!--<t:DrMuscleButton.Triggers>
                                                            <DataTrigger TargetType="t:DrMuscleButton" Binding="{Binding IsFinished}" Value="false">
                                                                <Setter Property="TextColor" Value="#195377" />
                                                            </DataTrigger>
                                                            <DataTrigger TargetType="t:DrMuscleButton" Binding="{Binding IsFinished}" Value="true">
                                                                <Setter Property="TextColor" Value="White" />
                                                            </DataTrigger>
                                                        </t:DrMuscleButton.Triggers>-->
                        </t:DrMuscleButton>

                    </StackLayout>

                </StackLayout>
            </ScrollView>

            <t:RightSideMasterPage x:Name="SlideMenu"
                Grid.Row="0"
                Grid.RowSpan="4"
                Grid.ColumnSpan="2"
                Margin="0"
                Padding="0"
                HorizontalOptions="EndAndExpand"
                IsVisible="False"
                VerticalOptions="FillAndExpand" />
        </Grid>
    </ContentPage.Content>
</ContentPage>