//
//  ExerciseSet.swift
//  DemoScreenwatchOS Watch App
//
//  Created by Dotnet on 14/01/25.
//

// Model for ExerciseSet
struct ExerciseSet {
    var id: Int64         // Unique identifier for the exercise set
    var reps: Int         // Number of repetitions in the set
    var weight: Int       // Weight used in the set
    var unit: String      // Unit of the weight (e.g., kg, lbs)
    var name: String      // Name of the exercise
    var setNumber: Int    // Set number (e.g., Set 1, Set 2, etc.)
    
    // Initializer
    init(id: Int64, reps: Int, weight: Int, unit: String, name: String, setNumber: Int) {
        self.id = id
        self.reps = reps
        self.weight = weight
        self.unit = unit
        self.name = name
        self.setNumber = setNumber
    }
}
