import XCTest
import CoreData
@testable import DrMuscleWatchApp

final class StorageServiceTests: XCTestCase {
    
    var storageService: StorageService!
    var persistenceController: PersistenceController!
    
    override func setUp() {
        super.setUp()
        // Use an in-memory store for testing
        persistenceController = PersistenceController(inMemory: true)
        storageService = StorageService(persistenceController: persistenceController)
    }
    
    override func tearDown() {
        storageService = nil
        persistenceController = nil
        super.tearDown()
    }
    
    // Test saving and retrieving a workout
    func testSaveAndRetrieveWorkout() {
        // Given a workout to save
        let workoutID: Int64 = 123
        let workoutName = "Test Workout"
        let timestamp = Date()
        
        // When saving the workout
        do {
            let savedWorkout = try storageService.saveWorkout(
                id: workoutID,
                name: workoutName,
                timestamp: timestamp,
                isCompleted: false,
                needsSync: true
            )
            
            // Then the workout should be saved with the correct properties
            XCTAssertEqual(savedWorkout.id, workoutID)
            XCTAssertEqual(savedWorkout.name, workoutName)
            XCTAssertEqual(savedWorkout.timestamp, timestamp)
            XCTAssertFalse(savedWorkout.isCompleted)
            XCTAssertTrue(savedWorkout.needsSync)
            
            // And it should be retrievable
            let retrievedWorkout = try storageService.getWorkout(id: workoutID)
            XCTAssertNotNil(retrievedWorkout)
            XCTAssertEqual(retrievedWorkout?.id, workoutID)
            XCTAssertEqual(retrievedWorkout?.name, workoutName)
        } catch {
            XCTFail("Failed to save or retrieve workout: \(error)")
        }
    }
    
    // Test saving and retrieving an exercise
    func testSaveAndRetrieveExercise() {
        // Given a workout and an exercise to save
        let workoutID: Int64 = 123
        let exerciseID: Int64 = 456
        let exerciseName = "Bench Press"
        
        // First save a workout
        do {
            _ = try storageService.saveWorkout(
                id: workoutID,
                name: "Test Workout",
                timestamp: Date()
            )
        } catch {
            XCTFail("Failed to save workout: \(error)")
            return
        }
        
        // When saving the exercise
        do {
            let savedExercise = try storageService.saveExercise(
                id: exerciseID,
                name: exerciseName,
                workoutID: workoutID,
                isBodyweight: false
            )
            
            // Then the exercise should be saved with the correct properties
            XCTAssertEqual(savedExercise.id, exerciseID)
            XCTAssertEqual(savedExercise.name, exerciseName)
            XCTAssertEqual(savedExercise.workoutID, workoutID)
            XCTAssertFalse(savedExercise.isBodyweight)
            
            // And it should be retrievable
            let retrievedExercise = try storageService.getExercise(id: exerciseID)
            XCTAssertNotNil(retrievedExercise)
            XCTAssertEqual(retrievedExercise?.id, exerciseID)
            XCTAssertEqual(retrievedExercise?.name, exerciseName)
            
            // And it should be linked to the workout
            XCTAssertNotNil(retrievedExercise?.workout)
            XCTAssertEqual(retrievedExercise?.workout?.id, workoutID)
        } catch {
            XCTFail("Failed to save or retrieve exercise: \(error)")
        }
    }
    
    // Test saving and retrieving a set log
    func testSaveAndRetrieveSetLog() {
        // Given a workout, an exercise, and a set log to save
        let workoutID: Int64 = 123
        let exerciseID: Int64 = 456
        let reps: Int16 = 8
        let weight: Double = 100.0
        let weightUnit = "kg"
        let rir: Int16 = 2
        let timestamp = Date()
        
        // First save a workout and an exercise
        do {
            _ = try storageService.saveWorkout(
                id: workoutID,
                name: "Test Workout",
                timestamp: Date()
            )
            
            _ = try storageService.saveExercise(
                id: exerciseID,
                name: "Bench Press",
                workoutID: workoutID
            )
        } catch {
            XCTFail("Failed to save workout or exercise: \(error)")
            return
        }
        
        // When saving the set log
        do {
            let savedSetLog = try storageService.saveSetLog(
                exerciseID: exerciseID,
                workoutID: workoutID,
                reps: reps,
                weight: weight,
                weightUnit: weightUnit,
                rir: rir,
                timestamp: timestamp,
                isWarmup: false,
                needsSync: true
            )
            
            // Then the set log should be saved with the correct properties
            XCTAssertEqual(savedSetLog.exerciseID, exerciseID)
            XCTAssertEqual(savedSetLog.workoutID, workoutID)
            XCTAssertEqual(savedSetLog.reps, reps)
            XCTAssertEqual(savedSetLog.weight, weight)
            XCTAssertEqual(savedSetLog.weightUnit, weightUnit)
            XCTAssertEqual(savedSetLog.rir, rir)
            XCTAssertEqual(savedSetLog.timestamp, timestamp)
            XCTAssertFalse(savedSetLog.isWarmup)
            XCTAssertTrue(savedSetLog.needsSync)
            
            // And it should be retrievable
            let setLogs = try storageService.getSetLogsForExercise(exerciseID: exerciseID)
            XCTAssertEqual(setLogs.count, 1)
            XCTAssertEqual(setLogs.first?.reps, reps)
            XCTAssertEqual(setLogs.first?.weight, weight)
            
            // And it should be linked to the exercise and workout
            XCTAssertNotNil(setLogs.first?.exercise)
            XCTAssertEqual(setLogs.first?.exercise?.id, exerciseID)
            XCTAssertNotNil(setLogs.first?.workout)
            XCTAssertEqual(setLogs.first?.workout?.id, workoutID)
        } catch {
            XCTFail("Failed to save or retrieve set log: \(error)")
        }
    }
    
    // Test fetching set logs that need sync
    func testFetchSetLogsNeedingSync() {
        // Given multiple set logs with different sync states
        let workoutID: Int64 = 123
        let exerciseID: Int64 = 456
        
        // First save a workout and an exercise
        do {
            _ = try storageService.saveWorkout(
                id: workoutID,
                name: "Test Workout",
                timestamp: Date()
            )
            
            _ = try storageService.saveExercise(
                id: exerciseID,
                name: "Bench Press",
                workoutID: workoutID
            )
        } catch {
            XCTFail("Failed to save workout or exercise: \(error)")
            return
        }
        
        // Save multiple set logs with different sync states
        do {
            // Set log 1 - needs sync
            _ = try storageService.saveSetLog(
                exerciseID: exerciseID,
                workoutID: workoutID,
                reps: 8,
                weight: 100.0,
                weightUnit: "kg",
                timestamp: Date(),
                needsSync: true
            )
            
            // Set log 2 - doesn't need sync
            _ = try storageService.saveSetLog(
                exerciseID: exerciseID,
                workoutID: workoutID,
                reps: 10,
                weight: 90.0,
                weightUnit: "kg",
                timestamp: Date(),
                needsSync: false
            )
            
            // Set log 3 - needs sync
            _ = try storageService.saveSetLog(
                exerciseID: exerciseID,
                workoutID: workoutID,
                reps: 12,
                weight: 80.0,
                weightUnit: "kg",
                timestamp: Date(),
                needsSync: true
            )
        } catch {
            XCTFail("Failed to save set logs: \(error)")
            return
        }
        
        // When fetching set logs that need sync
        do {
            let setLogsNeedingSync = try storageService.getSetLogsNeedingSync()
            
            // Then only the set logs that need sync should be returned
            XCTAssertEqual(setLogsNeedingSync.count, 2)
            
            // And they should have the needsSync flag set to true
            for setLog in setLogsNeedingSync {
                XCTAssertTrue(setLog.needsSync)
            }
        } catch {
            XCTFail("Failed to fetch set logs needing sync: \(error)")
        }
    }
    
    // Test marking a set log as synced
    func testMarkSetLogAsSynced() {
        // Given a set log that needs sync
        let workoutID: Int64 = 123
        let exerciseID: Int64 = 456
        
        // First save a workout and an exercise
        do {
            _ = try storageService.saveWorkout(
                id: workoutID,
                name: "Test Workout",
                timestamp: Date()
            )
            
            _ = try storageService.saveExercise(
                id: exerciseID,
                name: "Bench Press",
                workoutID: workoutID
            )
        } catch {
            XCTFail("Failed to save workout or exercise: \(error)")
            return
        }
        
        // Save a set log that needs sync
        var setLog: SetLog!
        do {
            setLog = try storageService.saveSetLog(
                exerciseID: exerciseID,
                workoutID: workoutID,
                reps: 8,
                weight: 100.0,
                weightUnit: "kg",
                timestamp: Date(),
                needsSync: true
            )
        } catch {
            XCTFail("Failed to save set log: \(error)")
            return
        }
        
        // When marking the set log as synced
        do {
            try storageService.markSetLogAsSynced(setLog)
            
            // Then the set log should have the needsSync flag set to false
            XCTAssertFalse(setLog.needsSync)
            
            // And it should not be returned when fetching set logs that need sync
            let setLogsNeedingSync = try storageService.getSetLogsNeedingSync()
            XCTAssertEqual(setLogsNeedingSync.count, 0)
        } catch {
            XCTFail("Failed to mark set log as synced: \(error)")
        }
    }
    
    // Test deleting a workout
    func testDeleteWorkout() {
        // Given a workout with exercises and set logs
        let workoutID: Int64 = 123
        let exerciseID: Int64 = 456
        
        // First save a workout, an exercise, and a set log
        do {
            _ = try storageService.saveWorkout(
                id: workoutID,
                name: "Test Workout",
                timestamp: Date()
            )
            
            _ = try storageService.saveExercise(
                id: exerciseID,
                name: "Bench Press",
                workoutID: workoutID
            )
            
            _ = try storageService.saveSetLog(
                exerciseID: exerciseID,
                workoutID: workoutID,
                reps: 8,
                weight: 100.0,
                weightUnit: "kg",
                timestamp: Date()
            )
        } catch {
            XCTFail("Failed to save workout, exercise, or set log: \(error)")
            return
        }
        
        // Verify that the workout, exercise, and set log were saved
        do {
            let workout = try storageService.getWorkout(id: workoutID)
            XCTAssertNotNil(workout)
            
            let exercise = try storageService.getExercise(id: exerciseID)
            XCTAssertNotNil(exercise)
            
            let setLogs = try storageService.getSetLogsForExercise(exerciseID: exerciseID)
            XCTAssertEqual(setLogs.count, 1)
        } catch {
            XCTFail("Failed to verify saved entities: \(error)")
            return
        }
        
        // When deleting the workout
        do {
            try storageService.deleteWorkout(workoutID: workoutID)
            
            // Then the workout should be deleted
            let workout = try storageService.getWorkout(id: workoutID)
            XCTAssertNil(workout)
            
            // And the associated exercise and set log should also be deleted (cascade delete)
            let exercises = try storageService.getExercisesForWorkout(workoutID: workoutID)
            XCTAssertEqual(exercises.count, 0)
            
            let setLogs = try storageService.getSetLogsForWorkout(workoutID: workoutID)
            XCTAssertEqual(setLogs.count, 0)
        } catch {
            XCTFail("Failed to delete workout: \(error)")
        }
    }
}
