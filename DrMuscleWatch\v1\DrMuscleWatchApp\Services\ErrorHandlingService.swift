import Foundation
import Combine

/// A service for handling and displaying errors throughout the app
class ErrorHandlingService: ObservableObject {
    /// The shared instance of the error handling service
    static let shared = ErrorHandlingService()
    
    /// Whether an error is currently being shown
    @Published var isShowingError = false
    
    /// The current error message being shown
    @Published var currentError: String?
    
    /// Timer for auto-dismissing errors
    private var dismissTimer: Timer?
    
    /// Private initializer to enforce singleton pattern
    private init() {}
    
    /// Show an error message
    /// - Parameters:
    ///   - message: The error message to show
    ///   - autoDismiss: Whether to automatically dismiss the error after a delay
    ///   - dismissAfter: The delay in seconds after which to dismiss the error
    func showError(message: String, autoDismiss: Bool = false, dismissAfter: TimeInterval = 3.0) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            self.currentError = message
            self.isShowingError = true
            
            if autoDismiss {
                self.dismissTimer?.invalidate()
                self.dismissTimer = Timer.scheduledTimer(withTimeInterval: dismissAfter, repeats: false) { [weak self] _ in
                    self?.dismissError()
                }
            }
        }
    }
    
    /// Dismiss the current error message
    func dismissError() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            self.isShowingError = false
            self.currentError = nil
            self.dismissTimer?.invalidate()
            self.dismissTimer = nil
        }
    }
    
    /// Handle an API error
    /// - Parameter error: The API error to handle
    func handleAPIError(_ error: APIError) {
        showError(message: error.localizedDescription)
    }
    
    /// Handle a generic error
    /// - Parameter error: The error to handle
    func handleError(_ error: Error) {
        if let apiError = error as? APIError {
            handleAPIError(apiError)
            return
        }
        
        let nsError = error as NSError
        
        // Handle network connectivity errors
        if nsError.domain == NSURLErrorDomain {
            switch nsError.code {
            case NSURLErrorNotConnectedToInternet, NSURLErrorNetworkConnectionLost, NSURLErrorCannotConnectToHost:
                showError(message: "No internet connection. Please check your network settings.")
                return
            default:
                break
            }
        }
        
        // Handle other errors
        if let localizedDescription = (error as NSError).userInfo[NSLocalizedDescriptionKey] as? String {
            showError(message: localizedDescription)
        } else {
            showError(message: error.localizedDescription)
        }
    }
}
