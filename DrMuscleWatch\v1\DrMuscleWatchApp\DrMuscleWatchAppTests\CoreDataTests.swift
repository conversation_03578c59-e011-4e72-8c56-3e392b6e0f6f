import XCTest
@testable import <PERSON><PERSON><PERSON><PERSON><PERSON>atchApp

final class CoreDataTests: XCTestCase {
    
    // Test that the Core Data model initializes correctly
    func testCoreDataModelInitialization() {
        // Given the application needs local persistence
        // When the Core Data stack is initialized
        let persistenceController = PersistenceController.preview
        
        // Then the persistent container loads successfully without errors
        XCTAssertNotNil(persistenceController.container, "Persistent container should not be nil")
        XCTAssertEqual(persistenceController.container.name, "DrMuscleData", "Container name should be '<PERSON><PERSON>uscleD<PERSON>'")
    }
    
    // Test saving a workout to Core Data
    func testSavingWorkout() {
        // Given the Core Data stack is initialized
        let persistenceController = PersistenceController.preview
        let context = persistenceController.container.viewContext
        
        // When a new Workout entity is created and saved
        let workout = Workout(context: context)
        workout.id = 1
        workout.name = "Test Workout"
        workout.timestamp = Date()
        
        // Then the save operation should succeed
        XCTAssertNoThrow(try context.save(), "Saving context should not throw an error")
        
        // And the workout should be retrievable
        let fetchRequest = Workout.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "id == %lld", 1)
        
        do {
            let results = try context.fetch(fetchRequest)
            XCTAssertEqual(results.count, 1, "Should retrieve exactly one workout")
            XCTAssertEqual(results.first?.name, "Test Workout", "Retrieved workout should have the correct name")
        } catch {
            XCTFail("Fetch request failed: \(error)")
        }
    }
    
    // Test saving an exercise to Core Data
    func testSavingExercise() {
        // Given the Core Data stack is initialized
        let persistenceController = PersistenceController.preview
        let context = persistenceController.container.viewContext
        
        // When a new Exercise entity is created and saved
        let exercise = Exercise(context: context)
        exercise.id = 1
        exercise.name = "Bench Press"
        exercise.workoutID = 1
        
        // Then the save operation should succeed
        XCTAssertNoThrow(try context.save(), "Saving context should not throw an error")
        
        // And the exercise should be retrievable
        let fetchRequest = Exercise.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "id == %lld", 1)
        
        do {
            let results = try context.fetch(fetchRequest)
            XCTAssertEqual(results.count, 1, "Should retrieve exactly one exercise")
            XCTAssertEqual(results.first?.name, "Bench Press", "Retrieved exercise should have the correct name")
        } catch {
            XCTFail("Fetch request failed: \(error)")
        }
    }
    
    // Test saving a set log to Core Data
    func testSavingSetLog() {
        // Given the Core Data stack is initialized
        let persistenceController = PersistenceController.preview
        let context = persistenceController.container.viewContext
        
        // When a new SetLog entity is created and saved
        let setLog = SetLog(context: context)
        setLog.exerciseID = 1
        setLog.workoutID = 1
        setLog.reps = 8
        setLog.weight = 100.0
        setLog.weightUnit = "kg"
        setLog.rir = 2
        setLog.timestamp = Date()
        setLog.isWarmup = false
        setLog.needsSync = true
        
        // Then the save operation should succeed
        XCTAssertNoThrow(try context.save(), "Saving context should not throw an error")
        
        // And the set log should be retrievable
        let fetchRequest = SetLog.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "exerciseID == %lld AND workoutID == %lld", 1, 1)
        
        do {
            let results = try context.fetch(fetchRequest)
            XCTAssertEqual(results.count, 1, "Should retrieve exactly one set log")
            XCTAssertEqual(results.first?.reps, 8, "Retrieved set log should have the correct reps")
            XCTAssertEqual(results.first?.weight, 100.0, "Retrieved set log should have the correct weight")
            XCTAssertEqual(results.first?.rir, 2, "Retrieved set log should have the correct RIR")
            XCTAssertEqual(results.first?.needsSync, true, "Retrieved set log should have the correct sync flag")
        } catch {
            XCTFail("Fetch request failed: \(error)")
        }
    }
    
    // Test fetching data with predicates
    func testFetchingWithPredicates() {
        // Given the Core Data stack is initialized with some data
        let persistenceController = PersistenceController.preview
        let context = persistenceController.container.viewContext
        
        // Create multiple set logs
        let setLog1 = SetLog(context: context)
        setLog1.exerciseID = 1
        setLog1.workoutID = 1
        setLog1.reps = 8
        setLog1.weight = 100.0
        setLog1.weightUnit = "kg"
        setLog1.timestamp = Date()
        setLog1.needsSync = true
        
        let setLog2 = SetLog(context: context)
        setLog2.exerciseID = 1
        setLog2.workoutID = 1
        setLog2.reps = 10
        setLog2.weight = 90.0
        setLog2.weightUnit = "kg"
        setLog2.timestamp = Date()
        setLog2.needsSync = false
        
        let setLog3 = SetLog(context: context)
        setLog3.exerciseID = 2
        setLog3.workoutID = 1
        setLog3.reps = 12
        setLog3.weight = 50.0
        setLog3.weightUnit = "kg"
        setLog3.timestamp = Date()
        setLog3.needsSync = true
        
        // Save the context
        do {
            try context.save()
        } catch {
            XCTFail("Failed to save context: \(error)")
            return
        }
        
        // When fetching set logs that need sync
        let syncFetchRequest = SetLog.fetchRequest()
        syncFetchRequest.predicate = NSPredicate(format: "needsSync == %@", NSNumber(value: true))
        
        do {
            let syncResults = try context.fetch(syncFetchRequest)
            // Then we should get the correct number of results
            XCTAssertEqual(syncResults.count, 2, "Should retrieve two set logs that need sync")
        } catch {
            XCTFail("Fetch request failed: \(error)")
        }
        
        // When fetching set logs for a specific exercise
        let exerciseFetchRequest = SetLog.fetchRequest()
        exerciseFetchRequest.predicate = NSPredicate(format: "exerciseID == %lld", 1)
        
        do {
            let exerciseResults = try context.fetch(exerciseFetchRequest)
            // Then we should get the correct number of results
            XCTAssertEqual(exerciseResults.count, 2, "Should retrieve two set logs for exercise ID 1")
        } catch {
            XCTFail("Fetch request failed: \(error)")
        }
    }
}
