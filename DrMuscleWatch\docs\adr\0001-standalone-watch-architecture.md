# ADR-0001: Standalone Watch Architecture

*   **Status:** Accepted
*   **Date:** {{CURRENT_DATE}}
*   **Deciders:** Project Lead (based on user requirements)
*   **Consulted:** AI Assistant
*   **Informed:** Development Team

## Context and Problem Statement

The Dr. Muscle Apple Watch app needs to allow users to track workouts effectively, often in gym environments where iPhone connectivity may be unreliable or inconvenient. The initial alpha version relied on WatchConnectivity, requiring constant communication with the iPhone app. This approach limits usability when the phone is not nearby or connected. We need an architecture that allows the watch app to function independently for the core workout tracking experience.

## Decision Drivers

*   **User Convenience:** Users should not need their iPhone readily accessible during a workout.
*   **Offline Functionality:** Gyms can have poor connectivity; the app must tolerate temporary or complete offline periods during a workout.
*   **Reliability:** Reduce dependency on Bluetooth/WatchConnectivity stability.
*   **Future Scalability:** A standalone architecture simplifies adding features directly to the watch app.

## Considered Options

*   **Option 1: WatchConnectivity Companion App:** Continue the alpha approach. Watch app acts primarily as a remote control/display for the iPhone app.
    *   *Pros:* Simpler data management (single source of truth on iPhone), potentially less complex initial build for basic mirroring.
    *   *Cons:* Requires constant iPhone connection, unusable offline, limits watch-specific features.
*   **Option 2: Standalone Watch App with API Sync:** Watch app operates independently, fetching data directly from the backend API and storing workout progress locally. Syncs with the backend when online.
    *   *Pros:* Works independently of iPhone, enables robust offline functionality, aligns with user expectations for modern watch apps.
    *   *Cons:* Requires local storage implementation, sync logic complexity, direct API integration on the watch.

## Decision Outcome

**Chosen Option:** Standalone Watch App with API Sync.

This architecture directly addresses the core requirements of user convenience and offline functionality. While it introduces complexity in local storage and synchronization, these are necessary trade-offs for delivering the desired user experience. The watch app will fetch workout plans from the API, store progress locally using Core Data, and sync back to the API when connectivity is available.

### Negative Consequences

*   Increased complexity due to local database management (Core Data).
*   Requires robust sync logic to handle conflicts and ensure data integrity.
*   Requires direct API client implementation on the watch.

## Links

*   `docs/architecture.md`