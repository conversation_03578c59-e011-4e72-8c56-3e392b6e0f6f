import XCTest
import Health<PERSON>it
@testable import DrMuscleWatchApp

final class HealthKitServiceTests: XCTestCase {

    // Test that the HealthKitService requests authorization on first workout start
    func testRequestAuthorizationOnFirstWorkoutStart() async {
        // Given
        let mockHealthStore = MockHKHealthStore()
        let healthKitService = HealthKitService(healthStore: mockHealthStore)

        // When
        let result = await healthKitService.requestAuthorization()

        // Then
        XCTAssertTrue(mockHealthStore.requestAuthorizationCalled, "Authorization should be requested")
        XCTAssertTrue(result, "Authorization result should be true")
        XCTAssertTrue(mockHealthStore.typesToShare.contains(HKObjectType.workoutType()), "Should request authorization to share workout type")
        XCTAssertTrue(mockHealthStore.typesToRead.contains(HKQuantityType.quantityType(forIdentifier: .activeEnergyBurned)!), "Should request authorization to read active energy burned")
        XCTAssertTrue(mockHealthStore.typesToRead.contains(HKQuantityType.quantityType(forIdentifier: .heartRate)!), "Should request authorization to read heart rate")
    }

    // Test that the HealthKitService handles authorization granted
    func testAuthorizationGranted() async {
        // Given
        let mockHealthStore = MockHKHealthStore()
        let healthKitService = HealthKitService(healthStore: mockHealthStore)

        // When
        let result = await healthKitService.requestAuthorization()

        // Then
        XCTAssertTrue(result, "Authorization result should be true when granted")
    }

    // Test that the HealthKitService handles authorization denied
    func testAuthorizationDenied() async {
        // Given
        let mockHealthStore = MockHKHealthStore()
        mockHealthStore.shouldThrowError = true
        let healthKitService = HealthKitService(healthStore: mockHealthStore)

        // When
        let result = await healthKitService.requestAuthorization()

        // Then
        XCTAssertFalse(result, "Authorization result should be false when denied")
    }

    // Test that the HealthKitService handles authorization already determined
    func testAuthorizationAlreadyDetermined() async {
        // Given
        let mockHealthStore = MockHKHealthStore()
        let healthKitService = HealthKitService(healthStore: mockHealthStore)

        // When - request authorization twice
        let firstResult = await healthKitService.requestAuthorization()
        let secondResult = await healthKitService.requestAuthorization()

        // Then
        XCTAssertTrue(firstResult, "First authorization request should succeed")
        XCTAssertTrue(secondResult, "Second authorization request should succeed")
        XCTAssertEqual(mockHealthStore.requestAuthorizationCallCount, 2, "Authorization should be requested twice")
    }

    // Test that the HealthKitService handles unavailable HealthKit
    func testHealthKitUnavailable() async {
        // Given
        let mockHealthStore = MockHKHealthStore()
        mockHealthStore.isHealthDataAvailable = false
        let healthKitService = HealthKitService(healthStore: mockHealthStore)

        // When
        let result = await healthKitService.requestAuthorization()

        // Then
        XCTAssertFalse(result, "Authorization result should be false when HealthKit is unavailable")
        XCTAssertFalse(mockHealthStore.requestAuthorizationCalled, "Authorization should not be requested when HealthKit is unavailable")
    }

    // Test that the HealthKitService starts a workout session successfully
    func testStartWorkoutSessionSuccessfully() async {
        // Given
        let mockHealthStore = MockHKHealthStore()
        let mockWorkoutSession = MockHKWorkoutSession()
        let mockWorkoutBuilder = MockHKWorkoutBuilder()

        mockHealthStore.mockWorkoutSession = mockWorkoutSession
        mockWorkoutSession.mockWorkoutBuilder = mockWorkoutBuilder

        let healthKitService = HealthKitService(healthStore: mockHealthStore)

        // When
        let result = await healthKitService.startWorkout(workoutName: "Test Workout")

        // Then
        XCTAssertTrue(result, "Starting workout should return true")
        XCTAssertTrue(mockWorkoutSession.startActivityCalled, "Workout session should be started")
        XCTAssertTrue(mockWorkoutBuilder.beginCollectionCalled, "Workout builder should begin collection")
    }

    // Test that the HealthKitService handles workout session start failure
    func testStartWorkoutSessionFailure() async {
        // Given
        let mockHealthStore = MockHKHealthStore()
        mockHealthStore.shouldThrowWorkoutSessionError = true

        let healthKitService = HealthKitService(healthStore: mockHealthStore)

        // When
        let result = await healthKitService.startWorkout(workoutName: "Test Workout")

        // Then
        XCTAssertFalse(result, "Starting workout should return false when session creation fails")
    }

    // Test that the HealthKitService ends a workout session successfully
    func testEndWorkoutSessionSuccessfully() async {
        // Given
        let mockHealthStore = MockHKHealthStore()
        let mockWorkoutSession = MockHKWorkoutSession()
        let mockWorkoutBuilder = MockHKWorkoutBuilder()

        mockHealthStore.mockWorkoutSession = mockWorkoutSession
        mockWorkoutSession.mockWorkoutBuilder = mockWorkoutBuilder

        let healthKitService = HealthKitService(healthStore: mockHealthStore)

        // First start a workout
        let startResult = await healthKitService.startWorkout(workoutName: "Test Workout")
        XCTAssertTrue(startResult, "Starting workout should return true")

        // When
        let endResult = await healthKitService.endWorkout()

        // Then
        XCTAssertTrue(endResult, "Ending workout should return true")
        XCTAssertTrue(mockWorkoutSession.endCalled, "Workout session should be ended")
        XCTAssertTrue(mockWorkoutBuilder.endCollectionCalled, "Workout builder should end collection")
        XCTAssertTrue(mockWorkoutBuilder.finishWorkoutCalled, "Workout builder should finish workout")
    }

    // Test that the HealthKitService handles workout session end failure
    func testEndWorkoutSessionFailure() async {
        // Given
        let mockHealthStore = MockHKHealthStore()
        let mockWorkoutSession = MockHKWorkoutSession()
        let mockWorkoutBuilder = MockHKWorkoutBuilder()

        mockHealthStore.mockWorkoutSession = mockWorkoutSession
        mockWorkoutSession.mockWorkoutBuilder = mockWorkoutBuilder
        mockWorkoutBuilder.shouldThrowEndCollectionError = true
        mockWorkoutBuilder.shouldThrowFinishWorkoutError = true

        let healthKitService = HealthKitService(healthStore: mockHealthStore)

        // First start a workout
        let startResult = await healthKitService.startWorkout(workoutName: "Test Workout")
        XCTAssertTrue(startResult, "Starting workout should return true")

        // When
        let endResult = await healthKitService.endWorkout()

        // Then
        XCTAssertFalse(endResult, "Ending workout should return false when collection or finishing fails")
        XCTAssertTrue(mockWorkoutSession.endCalled, "Workout session should still be ended")
    }

    // Test that the HealthKitService handles ending a non-existent workout session
    func testEndNonExistentWorkoutSession() async {
        // Given
        let mockHealthStore = MockHKHealthStore()
        let healthKitService = HealthKitService(healthStore: mockHealthStore)

        // When
        let result = await healthKitService.endWorkout()

        // Then
        XCTAssertTrue(result, "Ending a non-existent workout should return true")
    }

    // Test that the HealthKitService saves workout data to HealthKit with energy burned and heart rate
    func testSaveWorkoutDataWithEnergyAndHeartRate() async {
        // Given
        let mockHealthStore = MockHKHealthStore()
        let healthKitService = HealthKitService(healthStore: mockHealthStore)

        // Set up dates for the workout
        let startDate = Date().addingTimeInterval(-3600) // 1 hour ago
        let endDate = Date()
        let workoutName = "Test Workout"

        // When
        let result = await healthKitService.saveWorkoutData(startDate: startDate, endDate: endDate, workoutName: workoutName)

        // Then
        XCTAssertTrue(result, "Saving workout data should return true")
        XCTAssertTrue(mockHealthStore.saveObjectCalled, "HealthKit store should save the workout")
        XCTAssertNotNil(mockHealthStore.savedWorkout, "A workout should be saved")
        XCTAssertEqual(mockHealthStore.savedWorkout?.workoutActivityType, .traditionalStrengthTraining, "Workout should have correct activity type")
        XCTAssertEqual(mockHealthStore.savedWorkout?.startDate, startDate, "Workout should have correct start date")
        XCTAssertEqual(mockHealthStore.savedWorkout?.endDate, endDate, "Workout should have correct end date")
        XCTAssertNotNil(mockHealthStore.savedWorkout?.totalEnergyBurned, "Workout should have energy burned data")
        XCTAssertEqual(mockHealthStore.savedWorkout?.metadata?[HKMetadataKeyWorkoutBrandName] as? String, "Dr. Muscle", "Workout should have Dr. Muscle as brand name")
        XCTAssertEqual(mockHealthStore.savedWorkout?.metadata?[HKMetadataKeyWorkoutName] as? String, workoutName, "Workout should have correct name")
    }

    // Test that the HealthKitService saves workout data to HealthKit without energy burned or heart rate when permissions are denied
    func testSaveWorkoutDataWithoutEnergyAndHeartRate() async {
        // Given
        let mockHealthStore = MockHKHealthStore()
        mockHealthStore.shouldDenyEnergyAndHeartRatePermissions = true
        let healthKitService = HealthKitService(healthStore: mockHealthStore)

        // Set up dates for the workout
        let startDate = Date().addingTimeInterval(-3600) // 1 hour ago
        let endDate = Date()

        // When
        let result = await healthKitService.saveWorkoutData(startDate: startDate, endDate: endDate, workoutName: nil)

        // Then
        XCTAssertTrue(result, "Saving workout data should return true even without energy and heart rate")
        XCTAssertTrue(mockHealthStore.saveObjectCalled, "HealthKit store should save the workout")
        XCTAssertNotNil(mockHealthStore.savedWorkout, "A workout should be saved")
        XCTAssertEqual(mockHealthStore.savedWorkout?.workoutActivityType, .traditionalStrengthTraining, "Workout should have correct activity type")
        XCTAssertNil(mockHealthStore.savedWorkout?.totalEnergyBurned, "Workout should not have energy burned data")
        XCTAssertEqual(mockHealthStore.savedWorkout?.metadata?[HKMetadataKeyWorkoutBrandName] as? String, "Dr. Muscle", "Workout should have Dr. Muscle as brand name")
    }

    // Test that the HealthKitService handles errors when saving workout data
    func testSaveWorkoutDataHandlesErrors() async {
        // Given
        let mockHealthStore = MockHKHealthStore()
        mockHealthStore.shouldThrowSaveError = true
        let healthKitService = HealthKitService(healthStore: mockHealthStore)

        // Set up dates for the workout
        let startDate = Date().addingTimeInterval(-3600) // 1 hour ago
        let endDate = Date()

        // When
        let result = await healthKitService.saveWorkoutData(startDate: startDate, endDate: endDate, workoutName: nil)

        // Then
        XCTAssertFalse(result, "Saving workout data should return false when there's an error")
        XCTAssertTrue(mockHealthStore.saveObjectCalled, "HealthKit store should attempt to save the workout")
    }
}

// Mock HKHealthStore for testing
class MockHKHealthStore: HKHealthStoreProtocol {
    var requestAuthorizationCalled = false
    var requestAuthorizationCallCount = 0
    var typesToShare: Set<HKSampleType> = []
    var typesToRead: Set<HKObjectType> = []
    var shouldThrowError = false
    var shouldThrowWorkoutSessionError = false
    var shouldThrowSaveError = false
    var shouldDenyEnergyAndHeartRatePermissions = false
    var isHealthDataAvailable = true
    var mockWorkoutSession: MockHKWorkoutSession?

    // Properties for tracking workout saves
    var saveObjectCalled = false
    var savedWorkout: HKWorkout?
    var savedHeartRateSamples: [HKQuantitySample] = []
    var savedEnergySample: HKQuantitySample?

    func requestAuthorization(toShare typesToShare: Set<HKSampleType>, read typesToRead: Set<HKObjectType>) async throws {
        requestAuthorizationCalled = true
        requestAuthorizationCallCount += 1
        self.typesToShare = typesToShare
        self.typesToRead = typesToRead

        if shouldThrowError {
            throw NSError(domain: "HealthKitError", code: 1, userInfo: nil)
        }
    }

    // Mock method for saving objects to HealthKit
    func saveObject(_ object: HKObject, withCompletion completion: @escaping (Bool, Error?) -> Void) {
        saveObjectCalled = true

        if let workout = object as? HKWorkout {
            savedWorkout = workout
        } else if let sample = object as? HKQuantitySample {
            if sample.quantityType == HKQuantityType.quantityType(forIdentifier: .heartRate) {
                savedHeartRateSamples.append(sample)
            } else if sample.quantityType == HKQuantityType.quantityType(forIdentifier: .activeEnergyBurned) {
                savedEnergySample = sample
            }
        }

        if shouldThrowSaveError {
            completion(false, NSError(domain: "HealthKitError", code: 6, userInfo: nil))
        } else {
            completion(true, nil)
        }
    }

    // Mock method for saving objects to HealthKit
    func save(_ objects: [HKObject], withCompletion completion: @escaping (Bool, Error?) -> Void) {
        saveObjectCalled = true

        for object in objects {
            if let workout = object as? HKWorkout {
                savedWorkout = workout
            } else if let sample = object as? HKQuantitySample {
                if sample.quantityType == HKQuantityType.quantityType(forIdentifier: .heartRate) {
                    savedHeartRateSamples.append(sample)
                } else if sample.quantityType == HKQuantityType.quantityType(forIdentifier: .activeEnergyBurned) {
                    savedEnergySample = sample
                }
            }
        }

        if shouldThrowSaveError {
            completion(false, NSError(domain: "HealthKitError", code: 6, userInfo: nil))
        } else {
            completion(true, nil)
        }
    }
}

// Mock HKWorkoutSession for testing
class MockHKWorkoutSession: HKWorkoutSession {
    var startActivityCalled = false
    var endCalled = false
    var mockWorkoutBuilder: MockHKWorkoutBuilder?

    // Override the initializer to avoid calling super.init
    override init(configuration: HKWorkoutConfiguration) throws {
        // Create a valid configuration
        let validConfig = HKWorkoutConfiguration()
        validConfig.activityType = .traditionalStrengthTraining
        validConfig.locationType = .indoor

        try super.init(configuration: validConfig)
    }

    // Override startActivity to track when it's called
    override func startActivity(with date: Date) {
        startActivityCalled = true
        // Don't call super.startActivity to avoid actual HealthKit operations
    }

    // Override end to track when it's called
    override func end() {
        endCalled = true
        // Don't call super.end to avoid actual HealthKit operations
    }

    // Override associatedWorkoutBuilder to return our mock builder
    override func associatedWorkoutBuilder() -> HKWorkoutBuilder {
        return mockWorkoutBuilder ?? MockHKWorkoutBuilder()
    }
}

// Mock HKWorkoutBuilder for testing
class MockHKWorkoutBuilder: HKWorkoutBuilder {
    var beginCollectionCalled = false
    var endCollectionCalled = false
    var finishWorkoutCalled = false
    var shouldThrowBeginCollectionError = false
    var shouldThrowEndCollectionError = false
    var shouldThrowFinishWorkoutError = false
    var mockDataSource: Any?

    // Override the initializer to avoid calling super.init
    override init(healthStore: HKHealthStore, configuration: HKWorkoutConfiguration, device: HKDevice?) {
        // Create a valid configuration
        let validConfig = HKWorkoutConfiguration()
        validConfig.activityType = .traditionalStrengthTraining
        validConfig.locationType = .indoor

        super.init(healthStore: HKHealthStore(), configuration: validConfig, device: nil)
    }

    // Override beginCollection to track when it's called
    override func beginCollection(withStart date: Date, completion: @escaping (Bool, Error?) -> Void) {
        beginCollectionCalled = true

        if shouldThrowBeginCollectionError {
            completion(false, NSError(domain: "HealthKitError", code: 2, userInfo: nil))
        } else {
            completion(true, nil)
        }
    }

    // Override endCollection to track when it's called
    override func endCollection(withEnd date: Date, completion: @escaping (Bool, Error?) -> Void) {
        endCollectionCalled = true

        if shouldThrowEndCollectionError {
            completion(false, NSError(domain: "HealthKitError", code: 3, userInfo: nil))
        } else {
            completion(true, nil)
        }
    }

    // Override finishWorkout to track when it's called
    override func finishWorkout(completion: @escaping (HKWorkout?, Error?) -> Void) {
        finishWorkoutCalled = true

        if shouldThrowFinishWorkoutError {
            completion(nil, NSError(domain: "HealthKitError", code: 4, userInfo: nil))
        } else {
            // Create a mock workout
            let workout = HKWorkout(
                activityType: .traditionalStrengthTraining,
                start: Date().addingTimeInterval(-3600), // 1 hour ago
                end: Date(),
                duration: 3600,
                totalEnergyBurned: nil,
                totalDistance: nil,
                metadata: nil
            )

            completion(workout, nil)
        }
    }

    // Override dataSource setter to use our mock data source
    override var dataSource: HKWorkoutDataSource? {
        get {
            return mockDataSource as? HKWorkoutDataSource
        }
        set {
            mockDataSource = newValue
        }
    }
}

// Extension to make HKHealthStore testable
extension HKHealthStore {
    static var isHealthDataAvailableOverride: Bool?

    static func resetIsHealthDataAvailableOverride() {
        isHealthDataAvailableOverride = nil
    }

    class func isHealthDataAvailable() -> Bool {
        return isHealthDataAvailableOverride ?? true
    }
}
