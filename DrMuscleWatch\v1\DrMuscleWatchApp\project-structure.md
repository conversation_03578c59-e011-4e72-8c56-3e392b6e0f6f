# Dr. Muscle Watch App - Project Structure

## Overview

This document describes the structure of the Dr. Muscle Watch App project, which is a standalone watchOS app built with Swift and SwiftUI. The project follows the architecture outlined in `docs/architecture.md`.

## Project Structure

```
DrMuscleWatchApp/
├── DrMuscleWatchApp/
│   ├── DrMuscleWatchApp.swift       # Main app entry point with SwiftUI App lifecycle
│   ├── ContentView.swift            # Initial view that will contain login or workout view
│   ├── Info.plist                   # App configuration
│   ├── Assets.xcassets/             # App assets including icons
│   └── Preview Content/             # Preview assets for SwiftUI previews
└── DrMuscleWatchAppTests/
    └── DrMuscleWatchAppTests.swift  # Basic tests for app initialization
```

## Importing into Xcode

When importing this project into Xcode on the Mac, follow these steps:

1. Create a new watchOS App project in Xcode
2. Replace the generated files with the files from this project structure
3. Ensure the project settings match the requirements in `docs/architecture.md`
4. Build and run the project to verify it works correctly

## Dependencies

The project uses the following dependencies:
- SwiftUI
- WatchKit
- XCTest (for testing)

Additional dependencies will be added as needed for future tasks, such as:
- Core Data (for local storage)
- HealthKit (for workout tracking)
- AuthenticationServices (for Sign in with Apple)

## Next Steps

After successfully importing and building the project, proceed to the next task in `docs/todos/core.md`, which is AUTH-01: Implement "Sign in with Apple" flow.
