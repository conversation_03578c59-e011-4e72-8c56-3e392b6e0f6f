import SwiftUI

/// A view that displays a checkmark animation for exercise transitions
struct CheckmarkView: View {
    // MARK: - Properties

    @State private var animationProgress: CGFloat = 0
    let onAnimationComplete: () -> Void

    // Customizable properties
    var iconName: String = "checkmark.circle.fill"
    var iconColor: Color = .green
    var iconSize: CGFloat = 100
    var backgroundColor: Color = Color.black.opacity(0.8)
    var fadeInDuration: Double = 0.5
    var displayDuration: Double = 1.0
    var fadeOutDuration: Double = 0.3

    // MARK: - Initialization

    /// Initialize with default values
    /// - Parameter onAnimationComplete: Closure to call when animation completes
    init(onAnimationComplete: @escaping () -> Void) {
        self.onAnimationComplete = onAnimationComplete
    }

    /// Initialize with custom values
    /// - Parameters:
    ///   - iconName: SF Symbol name for the icon
    ///   - iconColor: Color of the icon
    ///   - iconSize: Size of the icon
    ///   - backgroundColor: Background color
    ///   - fadeInDuration: Duration of the fade-in animation
    ///   - displayDuration: Duration to display the icon
    ///   - fadeOutDuration: Duration of the fade-out animation
    ///   - onAnimationComplete: Closure to call when animation completes
    init(
        iconName: String = "checkmark.circle.fill",
        iconColor: Color = .green,
        iconSize: CGFloat = 100,
        backgroundColor: Color = Color.black.opacity(0.8),
        fadeInDuration: Double = 0.5,
        displayDuration: Double = 1.0,
        fadeOutDuration: Double = 0.3,
        onAnimationComplete: @escaping () -> Void
    ) {
        self.iconName = iconName
        self.iconColor = iconColor
        self.iconSize = iconSize
        self.backgroundColor = backgroundColor
        self.fadeInDuration = fadeInDuration
        self.displayDuration = displayDuration
        self.fadeOutDuration = fadeOutDuration
        self.onAnimationComplete = onAnimationComplete
    }

    // MARK: - Body

    var body: some View {
        ZStack {
            // Background overlay
            backgroundColor
                .edgesIgnoringSafeArea(.all)

            // Icon
            Image(systemName: iconName)
                .font(.system(size: iconSize))
                .foregroundColor(iconColor)
                .opacity(animationProgress)
                .scaleEffect(animationProgress)
        }
        .onAppear {
            // Start the animation sequence
            startAnimationSequence()
        }
    }

    // MARK: - Private Methods

    /// Start the animation sequence: fade in, display, fade out, complete
    private func startAnimationSequence() {
        // Fade in
        withAnimation(.easeInOut(duration: fadeInDuration)) {
            animationProgress = 1.0
        }

        // After display duration, fade out
        DispatchQueue.main.asyncAfter(deadline: .now() + fadeInDuration + displayDuration) {
            withAnimation(.easeInOut(duration: fadeOutDuration)) {
                animationProgress = 0
            }

            // Call the completion handler after the fade-out animation
            DispatchQueue.main.asyncAfter(deadline: .now() + fadeOutDuration) {
                onAnimationComplete()
            }
        }
    }
}

// MARK: - Preview

#Preview {
    CheckmarkView {
        print("Animation completed")
    }
}
