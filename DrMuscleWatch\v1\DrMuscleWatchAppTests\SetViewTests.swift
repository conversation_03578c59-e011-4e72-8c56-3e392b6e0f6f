import XCTest
import Swift<PERSON>
@testable import DrMuscleWatchApp

class SetViewTests: XCTestCase {

    func testSetViewDisplaysExerciseDetails() {
        // Given
        let mockViewModel = MockSetViewModel()
        mockViewModel.exerciseName = "Bench Press"
        mockViewModel.currentReps = 8
        mockViewModel.currentWeight = 100
        mockViewModel.weightUnit = "lbs"

        // When
        let view = SetView(viewModel: mockViewModel)

        // Then
        XCTAssertEqual(mockViewModel.exerciseName, "Bench Press", "Exercise name should be Bench Press")
        XCTAssertEqual(mockViewModel.currentReps, 8, "Current reps should be 8")
        XCTAssertEqual(mockViewModel.currentWeight, 100, "Current weight should be 100")
        XCTAssertEqual(mockViewModel.weightUnit, "lbs", "Weight unit should be lbs")
    }

    func testSetViewDisplaysErrorState() {
        // Given
        let mockViewModel = MockSetViewModel()
        mockViewModel.state = .error("Test error message")

        // When
        let view = SetView(viewModel: mockViewModel)

        // Then
        XCTAssertTrue(mockViewModel.hasError, "View model should have an error")
        XCTAssertEqual(mockViewModel.errorMessage, "Test error message", "Error message should match")
    }

    func testSetViewDisplaysLoadingState() {
        // Given
        let mockViewModel = MockSetViewModel()
        mockViewModel.state = .loading

        // When
        let view = SetView(viewModel: mockViewModel)

        // Then
        XCTAssertTrue(mockViewModel.isLoading, "View model should be in loading state")
    }

    func testWeightPickerUpdatesViewModel() {
        // Given
        let mockViewModel = MockSetViewModel()
        mockViewModel.exerciseName = "Bench Press"
        mockViewModel.currentReps = 8
        mockViewModel.currentWeight = 100
        mockViewModel.weightUnit = "lbs"

        // When
        let view = SetView(viewModel: mockViewModel)

        // Then
        // Initial state
        XCTAssertEqual(mockViewModel.currentWeight, 100, "Initial weight should be 100")

        // Simulate weight update through the binding
        // This simulates what happens when a user selects a new weight in the picker
        let weightBinding = Binding(
            get: { Int(mockViewModel.currentWeight) },
            set: { mockViewModel.updateWeight(Double($0)) }
        )
        weightBinding.wrappedValue = 105

        // Then
        XCTAssertEqual(mockViewModel.currentWeight, 105, "Weight should be updated to 105")
    }
}

// Mock ViewModel for testing
class MockSetViewModel: SetViewModel {
    override init(exerciseId: Int64 = 1, workoutId: Int64 = 1, apiClient: DrMuscleAPIClient = MockDrMuscleAPIClient()) {
        super.init(exerciseId: exerciseId, workoutId: workoutId, apiClient: apiClient)
    }

    override func fetchExerciseDetails() async {
        // Do nothing in the mock
    }

    override func moveToNextSet() -> Bool {
        return true
    }

    override func updateReps(_ reps: Int) {
        self.currentReps = reps
    }

    override func updateWeight(_ weight: Double) {
        self.currentWeight = weight
        // Update currentSetData to match the behavior of the real implementation
        self.currentSetData.weight = weight
    }

    override func getCurrentSetData() -> CurrentSetData {
        return currentSetData
    }
    
    override func nextExercise() -> Bool {
        showingChoiceScreen = false
        showingCheckmarkAnimation = true
        return true
    }
    
    override func onCheckmarkAnimationComplete() {
        showingCheckmarkAnimation = false
        shouldNavigateToNextExercise = true
    }
}
