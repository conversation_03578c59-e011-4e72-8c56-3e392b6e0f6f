﻿namespace DrMaxMuscle.Views;

using System.Diagnostics;
using CommunityToolkit.Maui.Views;
using DrMuscleWebApiSharedModel;
using RGPopup.Maui.Pages;
using RGPopup.Maui.Services;

public enum PopupAction
{
    Cancel,
    OK
}
public partial class CustomPopup : Popup
{
    
    public event EventHandler<PopupAction> ActionSelected;
    public CustomPopup(string title, string body, string okBtnText, string cancelBtnText,bool isVertical = false)
    {
        Debug.WriteLine("Custom popup calling");
        InitializeComponent();

        // Dynamically set the width of the Frame to 90% of the screen width
        var screenWidth = DeviceDisplay.MainDisplayInfo.Width / DeviceDisplay.MainDisplayInfo.Density;
        IOSFrame.WidthRequest = screenWidth * 0.9; // Assuming the Frame has x:Name="MainFrame"

        if (string.IsNullOrEmpty(title)){
            titleLabelIOS.IsVisible = false;
        }

        if(string.IsNullOrEmpty(body)){
            bodyLabelIOS.IsVisible = false;
        }


        if(string.IsNullOrEmpty(title) && string.IsNullOrEmpty(body)){
            labelView.IsVisible = false;
            
        }else{
            if(isVertical){
                verticalButtonsView.Margin = new Thickness(0,15,0,0);
            }
        }

        if(isVertical){
            horizontalButtonsView.IsVisible = false;
            verticalButtonsView.IsVisible = true;
        }else{
            horizontalButtonsView.IsVisible = true;
            verticalButtonsView.IsVisible = false;
            if(string.IsNullOrEmpty(cancelBtnText)){
                cancelBtnIOS.IsVisible = false;
            }
        }

        // if (Device.RuntimePlatform == Device.Android)
        // {
        //     IOSFrame.IsVisible = false;
        //     AndroidFrame.IsVisible = true;
        //     titleLabel.Text = title;
        //     bodyLabel.IsVisible = !string.IsNullOrEmpty(body) ? true : false;
        //     bodyLabel.Text = body;
        //     cancelBtn.Text = cancelBtnText;
        //     okBtn.Text = okBtnText;
        // }
        // else
        {
            //AndroidFrame.IsVisible = false;
            IOSFrame.IsVisible = true;
            titleLabelIOS.Text = title;
            bodyLabelIOS.IsVisible = !string.IsNullOrEmpty(body) ? true : false;
            bodyLabelIOS.Text = body;
            cancelBtnIOS.Text = cancelBtnText;
            okBtnIOS.Text = okBtnText;


            cancelBtnVerticalIOS.Text = cancelBtnText ;
            okBtnVerticalIOS.Text = okBtnText ;

        }

    }

    private async void Cancel_Btn_Clicked(object sender, EventArgs e)
    {
        try
        {
            await this.CloseAsync();
            //await MauiProgram.SafeDismissTopPopup();
            //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
            //    await PopupNavigation.Instance.PopAsync();
            ActionSelected?.Invoke(this, PopupAction.Cancel);
        }
        catch (Exception ex)
        {

        }
    }

    private async void OK_Btn_Clicked(object sender, EventArgs e)
    {
        try
        {
            await this.CloseAsync();
            //await MauiProgram.SafeDismissTopPopup();
            //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
            //    await PopupNavigation.Instance.PopAsync();
            ActionSelected?.Invoke(this, PopupAction.OK);
        }
        catch (Exception ex)
        {

        }
    }
    

    private async void OK_Btn_ClickedIOS(object sender, EventArgs e)
    {
        try
        {
            await this.CloseAsync();
            //await MauiProgram.SafeDismissTopPopup();
            //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
            //    await PopupNavigation.Instance.PopAsync();
            // Add check to ensure ActionSelected is not invoked multiple times
            if (ActionSelected != null)
            {
                ActionSelected.Invoke(this, PopupAction.OK);
                ActionSelected = null; // Prevent multiple invocations
            }
          
            //ActionSelected?.Invoke(this, PopupAction.OK);
        }
        catch (Exception ex)
        {

        }
    }

    private async void Cancel_Btn_ClickedIOS(object sender, EventArgs e)
    {
        try
        {
            await this.CloseAsync();
            //await MauiProgram.SafeDismissTopPopup();
            //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
            //    await PopupNavigation.Instance.PopAsync();
            ActionSelected?.Invoke(this, PopupAction.Cancel);
            
        }
        catch (Exception ex)
        {

        }
    }



}

