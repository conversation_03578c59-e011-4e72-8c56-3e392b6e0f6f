import SwiftUI

/// A timer view for rest periods
struct TimerView: View {
    @Binding var secondsRemaining: Int
    @Binding var isTimerRunning: Bool
    let onHide: () -> Void
    
    private let timer = Timer.publish(every: 1, on: .main, in: .common).autoconnect()
    
    var body: some View {
        VStack(spacing: 16) {
            Text("Rest")
                .font(AppTheme.titleStyle)
                .foregroundColor(AppTheme.primaryTextColor)
            
            Text(formatTime(seconds: secondsRemaining))
                .font(.system(size: 40, weight: .bold))
                .foregroundColor(AppTheme.primaryTextColor)
                .onReceive(timer) { _ in
                    if isTimerRunning && secondsRemaining > 0 {
                        secondsRemaining -= 1
                    } else if secondsRemaining == 0 {
                        isTimerRunning = false
                    }
                }
            
            Button("Hide Timer") {
                onHide()
            }
            .buttonStyle(AppTheme.tertiaryButtonStyle())
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.black.opacity(0.9))
    }
    
    private func formatTime(seconds: Int) -> String {
        let minutes = seconds / 60
        let remainingSeconds = seconds % 60
        return String(format: "%d:%02d", minutes, remainingSeconds)
    }
}
