﻿<?xml version="1.0" encoding="utf-8" ?>
<toolkit:Popup xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
                xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
                Color="#99000000"
                xmlns:microcharts="clr-namespace:Microcharts.Maui;assembly=Microcharts.Maui"
                xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
                xmlns:app="clr-namespace:DrMaxMuscle.Constants"
                    xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
                CanBeDismissedByTappingOutsideOfPopup="True"
             x:Class="DrMaxMuscle.Views.EndExercisePopup">

   <Frame
        x:Name="MainFrame"
        Padding="0"
        CornerRadius="0"
        HasShadow="False"
        IsClippedToBounds="True"
        HorizontalOptions="FillAndExpand"
        VerticalOptions="FillAndExpand"
        BackgroundColor="#f4f4f4"
        BorderColor="Transparent"
        Margin="0">
        <Grid VerticalOptions="CenterAndExpand">
            <StackLayout
                HorizontalOptions="FillAndExpand"
                VerticalOptions="FillAndExpand"
                Padding="20,0,20,10"
                BackgroundColor="#f4f4f4">

               
                    <StackLayout
                        x:Name="ScrollContentToShare"
                        VerticalOptions="FillAndExpand">
                        <StackLayout
                            x:Name="ResultStackLayout"
                            VerticalOptions="FillAndExpand"
                            Spacing="3"
                            Padding="0,27,0,0">
                            <ffimageloading:CachedImage
                                ErrorPlaceholder="backgroundblack.png"
                                IsVisible="false"
                                Margin="0,0,0,0"
                                x:Name="ImgName"
                                WidthRequest="100"
                                HeightRequest="100"
                                HorizontalOptions="Center"
                                Aspect="AspectFit"
                                VerticalOptions="Start" />
                            <Label
                                x:Name="lblResult4"
                                Text=""
                                IsVisible="true"
                                HorizontalOptions="Center"
                                FontSize="43"
                                        FontAttributes="Bold"
                                Style="{StaticResource LabelStyle}"
                                TextColor="Black" />
                            <Label
                                Margin="0,-5,0,0"
                                Text=""
                                x:Name="lblResult1"
                                HorizontalOptions="Center"
                                FontSize="24"
                                FontAttributes="Bold"
                                TextColor="Black"
                                HorizontalTextAlignment="Center"
                                MaxLines="1" />
                            <Label
                                x:Name="lblExerciseName"
                                HorizontalOptions="Center"
                                MaxLines="2"
                                LineBreakMode="TailTruncation"
                                Style="{StaticResource LabelStyle}"
                                FontSize="18"
                                TextColor="{x:Static app:AppThemeConstants.OffBlackColor}" />
                            <Grid Margin="0,17,0,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition
                                        Width="10" />
                                    <ColumnDefinition
                                        Width="*" />
                                    <ColumnDefinition
                                        Width="*" />
                                    <ColumnDefinition
                                        Width="10" />
                                </Grid.ColumnDefinitions>
                                <StackLayout
                                    Grid.Column="1"
                                    Spacing="3"
                                    HorizontalOptions="FillAndExpand">
                                    <ffimageloading:CachedImage
                                        ErrorPlaceholder="backgroundblack.png"
                                        x:Name="IconHistory"
                                        Source="history.png"
                                        Aspect="AspectFit"
                                        HeightRequest="32"
                                        HorizontalOptions="CenterAndExpand" />
                                    <Label
                                        x:Name="lblResult3"
                                        Text=""
                                        IsVisible="true"
                                        HorizontalOptions="Center"
                                        FontSize="22"
                                        FontAttributes="Bold"
                                        Style="{StaticResource LabelStyle}"
                                        TextColor="Black" />
                                    <Label
                                        x:Name="lblResult33"
                                        Text="Last workout"
                                        IsVisible="true"
                                        HorizontalOptions="Center"
                                        Style="{StaticResource LabelStyle}"
                                        FontSize="18"
                                        TextColor="{x:Static app:AppThemeConstants.OffBlackColor}" />
                                </StackLayout>
                                <StackLayout
                                    Grid.Column="2"
                                    Spacing="3"

                                    HorizontalOptions="FillAndExpand">
                                    <ffimageloading:CachedImage
                                        ErrorPlaceholder="backgroundblack.png"
                                        x:Name="IconCalander"
                                        Source="calander.png"
                                        Aspect="AspectFit"
                                        HeightRequest="32"
                                        HorizontalOptions="CenterAndExpand" />
                                    <Label
                                        x:Name="lblResult21"
                                        Text=""
                                        IsVisible="true"
                                        HorizontalOptions="Center"
                                        Style="{StaticResource LabelStyle}"
                                        FontSize="22"
                                        FontAttributes="Bold"
                                        TextColor="Black" />
                                    <Label
                                        x:Name="lblResult211"
                                        Text="Today's 1RM"
                                        IsVisible="true"
                                        HorizontalOptions="Center"
                                        Style="{StaticResource LabelStyle}"
                                        FontSize="18"
                                        TextColor="{x:Static app:AppThemeConstants.OffBlackColor}" />
                                </StackLayout>
                                <!--<StackLayout
                                    Grid.Column="2"
                                    HorizontalOptions="FillAndExpand">
                                <Image
                                        x:Name="IconResultImage"
                                        Source="up_arrow.png"
                                        Aspect="AspectFit"
                                        HeightRequest="32"
                                        HorizontalOptions="CenterAndExpand" />

                                <Label
                                        x:Name="lblResult4"
                                        Text=""
                                        IsVisible="true"
                                        HorizontalOptions="Center"
                                        Font="Bold,17"
                                        Style="{StaticResource LabelStyle}"
                                        TextColor="Black" />
                                    
                                    
                                    <Label
                                        x:Name="lblResult44"
                                        Text="Progress"
                                        IsVisible="true"
                                        HorizontalOptions="Center"
                                        Style="{StaticResource LabelStyle}"
                                        TextColor="{x:Static app:AppThemeConstants.OffBlackColor}" />
                                </StackLayout>-->
                            </Grid>

                            <Grid
                                Margin="0,17,0,0"
                                HeightRequest="190">
                                <microcharts:ChartView
                                    x:Name="chartView"
                                    HorizontalOptions="FillAndExpand"
                                    Margin="-67,0"
                                   BackgroundColor="Transparent"
                                    HeightRequest="210" />
                                <!--<oxy:PlotView
                                    x:Name="plotView"
                                    IsVisible="true"
                                    VerticalOptions="Start"
                                    HeightRequest="170">
                                </oxy:PlotView>-->
                            </Grid>

                            <Label
                                VerticalOptions="FillAndExpand" />
                        </StackLayout>
                    </StackLayout>
           

            <StackLayout
                Orientation="Vertical"
                VerticalOptions="End"
                HorizontalOptions="FillAndExpand"
                HeightRequest="160"
                Margin="0,10,0,0"
                Spacing="5"
                Padding="0,0,0,25">
                
                <!--<t:DrMuscleButton x:Name="ShareWithFBButton" Padding="10,0,0,0" Image="facebook_f_white.png" VerticalOptions="EndAndExpand" HorizontalOptions="FillAndExpand" Text="Share record" IsVisible="true" Style="{StaticResource highEmphasisButtonStyle}" BackgroundColor="#7f3c5a99" BorderColor="#7f3c5a99" BorderWidth="2" TextColor="White" />-->

                <!--Share exercise button -->
                <!--<t:DrMuscleButton
                    Style="{StaticResource menubuttonStyle}"
                    Text="Share"
                    BackgroundColor="Transparent"
                    Margin="0"
                    Padding="0"
                    HeightRequest="60"
                    HorizontalOptions="FillAndExpand"
                    Clicked="ShareExerciseButton_Clicked"/>-->

                <!--New Custom Share Exercise Button-->
                <controls:DrMuscleImageButton
                    Text="Share"
                    Source="ic_share_exercise"
                    Clicked="ShareExerciseButton_Clicked"
                    HeightRequest="60"/>

                <!--Continue Option PancakeView-->
                <Border
                    Padding="0"
                    Margin="0"
                    Stroke="Transparent"
                    HorizontalOptions="FillAndExpand" 
                    Style="{StaticResource GradientBorderStyleBlue}"
                    HeightRequest="72">
                    <Button
                        x:Name="NextExerciseButton"
                        VerticalOptions="FillAndExpand"
                        HorizontalOptions="FillAndExpand"
                        IsVisible="true"
                        Style="{StaticResource highEmphasisButtonStyle}"
                        BackgroundColor="Transparent"
                        BorderColor="Transparent"
                        TextColor="White"/>
                </Border>
            </StackLayout>
        </StackLayout>

        <!--<forms:ParticleView
            x:Name="MyParticleCanvas"
            FallingParticlesPerSecond="25.0"
            IsActive="False"
            IsRunning="False"
            HasFallingParticles="True"
            VerticalOptions="FillAndExpand"
            HorizontalOptions="FillAndExpand"
            InputTransparent="True"/>-->
        </Grid>
    </Frame>


</toolkit:Popup>
