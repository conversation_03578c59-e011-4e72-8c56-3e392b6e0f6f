import Foundation
import SwiftUI

// Define view states for better state management
enum WorkoutListViewState {
    case loading
    case loaded([WorkoutTemplateModel])
    case empty
    case error(String)
}

class WorkoutListViewModel: ObservableObject {
    @Published private(set) var state: WorkoutListViewState = .loading
    @Published private(set) var workouts: [WorkoutTemplateModel] = []

    // Computed properties for view state checks
    var isLoading: Bool {
        if case .loading = state { return true }
        return false
    }

    var hasError: Bool {
        if case .error(_) = state { return true }
        return false
    }

    var errorMessage: String? {
        if case .error(let message) = state { return message }
        return nil
    }

    var isEmptyState: Bool {
        if case .empty = state { return true }
        return false
    }

    private let apiClient: DrMuscleAPIClient

    init(apiClient: DrMuscleAPIClient = DrMuscleAPIClient.shared) {
        self.apiClient = apiClient
    }

    @MainActor
    func fetchWorkouts() async {
        state = .loading

        do {
            let workoutGroup = try await apiClient.getUserWorkoutGroup()
            workouts = workoutGroup.workouts

            if workouts.isEmpty {
                state = .empty
            } else {
                state = .loaded(workouts)
            }
        } catch {
            let errorMessage = "Failed to load workouts. Please check your connection."
            state = .error(errorMessage)

            // Use the error handling service to show a banner
            if let apiError = error as? APIError {
                ErrorHandlingService.shared.handleAPIError(apiError)
            } else {
                ErrorHandlingService.shared.handleError(error)
            }
        }
    }

    func selectWorkout(_ workout: WorkoutTemplateModel) -> Int {
        // Return the workout ID for navigation to the workout detail view
        return workout.id
    }
}
