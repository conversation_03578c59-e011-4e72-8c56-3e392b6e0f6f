import SwiftUI

struct WorkoutListView: View {
    @ObservedObject var viewModel: WorkoutListViewModel

    var body: some View {
        content
            .navigationTitle("Workouts")
            .task {
                await viewModel.fetchWorkouts()
            }
    }

    @ViewBuilder
    private var content: some View {
        switch viewModel.state {
        case .loading:
            loadingView
        case .loaded:
            workoutListView
        case .empty:
            emptyStateView
        case .error(let message):
            errorView(message: message)
        }
    }

    private var loadingView: some View {
        ProgressView("Loading workouts...")
            .progressViewStyle(CircularProgressViewStyle(tint: AppTheme.primaryTextColor))
    }

    private var workoutListView: some View {
        List(viewModel.workouts, id: \.id) { workout in
            NavigationLink(destination: WorkoutDetailView(viewModel: WorkoutDetailViewModel(workoutId: workout.id))) {
                Text(workout.name)
                    .font(.headline)
                    .padding(.vertical, 8)
                    .foregroundColor(AppTheme.primaryTextColor)
            }
        }
        .listStyle(.plain)
        .background(AppTheme.backgroundColor)
    }

    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "dumbbell")
                .font(.largeTitle)
                .foregroundColor(.gray)

            Text("No workouts scheduled")
                .font(.headline)
                .multilineTextAlignment(.center)
                .foregroundColor(AppTheme.primaryTextColor)

            Text("Check back later for your next workout")
                .font(.subheadline)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(AppTheme.backgroundColor)
    }

    private func errorView(message: String) -> some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle")
                .font(.largeTitle)
                .foregroundColor(.red)

            Text(message)
                .font(.headline)
                .multilineTextAlignment(.center)
                .foregroundColor(AppTheme.primaryTextColor)

            Button(action: {
                Task {
                    await viewModel.fetchWorkouts()
                }
            }) {
                Text("Retry")
                    .font(.headline)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
            }
            .buttonStyle(AppTheme.secondaryButtonStyle())
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(AppTheme.backgroundColor)
    }
}

#Preview {
    NavigationView {
        WorkoutListView(viewModel: WorkoutListViewModel())
    }
}
