# Dr. Muscle Watch App - API Reference

## Purpose

This document provides a reference for integrating the Dr. Muscle Watch app with the existing backend API. It outlines the key endpoints, data models, and implementation patterns to follow when implementing the API-related tasks in `docs/todos/core.md` and `docs/todos/workout.md`.

## API Client Structure

When implementing task **API-01**, use this structure as a reference:

```swift
class DrMuscleAPIClient {
    private let baseURL: String = "https://your-api-base-url.com/"
    private var token: String?
    
    static let shared = DrMuscleAPIClient()
    
    // AUTHENTICATION
    func signInWithApple(idToken: String) async throws -> UserInfosModel {
        // Maps to your existing RegisterWithApple endpoint
        return try await postJSON(route: "api/Account/RegisterWithApple", model: ["token": idToken])
    }
    
    // WORKOUT RETRIEVAL
    func getUserWorkoutGroup() async throws -> WorkoutTemplateGroupModel {
        // Maps to your existing GetUserWorkoutTemplateGroup endpoint
        return try await postJSON(route: "api/Workout/GetUserWorkoutTemplateGroup", model: nil)
    }
    
    func getUserWorkout() async throws -> WorkoutTemplateModel {
        // Maps to your existing GetUserWorkout endpoint
        return try await postJSON(route: "api/Workout/GetUserWorkout", model: nil)
    }
    
    // EXERCISE DATA
    func getRecommendationForExercise(model: RecommendationRequestModel) async throws -> RecommendationModel {
        // Maps to your existing GetRecommendationForExercise endpoint
        return try await postJSON(route: "api/Exercise/GetRecommendationForExercise", model: model)
    }
    
    // SET LOGGING
    func addWorkoutLogSerie(model: WorkoutLogSerieModel) async throws -> BooleanModel {
        // Maps to your existing AddWorkoutLogSerieNew endpoint
        return try await postJSON(route: "api/Exercise/AddWorkoutLogSerieNew", model: model)
    }
    
    // WORKOUT COMPLETION
    func saveWorkoutV3(model: SaveWorkoutModel) async throws -> BooleanModel {
        // Maps to your existing SaveWorkoutV3Pro endpoint
        return try await postJSON(route: "api/Workout/SaveWorkoutV3Pro", model: model)
    }
    
    // Generic request handler
    private func postJSON<T: Decodable, U>(route: String, model: U?) async throws -> T {
        // Implementation details...
    }
}
```

## Key Data Models

When implementing the data models for API requests and responses, use these structures as a reference:

```swift
// Core models mapped from your C# models

struct UserInfosModel: Codable {
    let id: String
    let email: String
    let token: String
    // Add other relevant user fields
}

struct WorkoutTemplateGroupModel: Codable {
    let id: Int
    let name: String
    let workouts: [WorkoutTemplateModel]
}

struct WorkoutTemplateModel: Codable {
    let id: Int
    let name: String
    let exercises: [ExerciseModel]
}

struct ExerciseModel: Codable {
    let id: Int
    let name: String
    let sets: [SetModel]
}

struct SetModel: Codable {
    let reps: Int
    let weight: Double
    let isWarmup: Bool
    let isFirstWorkSet: Bool
    let isLastPlannedSet: Bool
    let restDurationSeconds: Int
}

// Additional models...
```

## API Endpoints Reference

| Task ID | Feature | Endpoint | HTTP Method | Request Model | Response Model |
|---------|---------|----------|-------------|---------------|----------------|
| AUTH-01 | Sign in with Apple | `/api/Account/RegisterWithApple` | POST | `{"token": "apple_id_token"}` | `UserInfosModel` |
| WKOUT-LIST-01 | Get Workout List | `/api/Workout/GetUserWorkoutTemplateGroup` | POST | None | `WorkoutTemplateGroupModel` |
| WKOUT-DETAIL-01 | Get Workout Details | `/api/Workout/GetUserWorkout` | POST | None | `WorkoutTemplateModel` |
| WKOUT-EXEC-01 | Get Exercise Recommendation | `/api/Exercise/GetRecommendationForExercise` | POST | `RecommendationRequestModel` | `RecommendationModel` |
| LOGGING-01 | Log Completed Set | `/api/Exercise/AddWorkoutLogSerieNew` | POST | `WorkoutLogSerieModel` | `BooleanModel` |
| WKOUT-COMPLETE-01 | Save Completed Workout | `/api/Workout/SaveWorkoutV3Pro` | POST | `SaveWorkoutModel` | `BooleanModel` |

## Required API Flags

The watch app architecture requires these flags in the API responses:

- `isWarmup` (Boolean): Identifies warm-up sets
- `isFirstWorkSet` (Boolean): Identifies the first work set (where RIR is prompted)
- `isLastPlannedSet` (Boolean): Identifies the final planned set (triggers "Add Set" option)
- `restDurationSeconds` (Int): Specifies the rest period after a set

## Offline Sync Implementation

For tasks **OFFLINE-01** and **SYNC-01**, implement a sync service that:

1. Stores completed sets locally with a `needsSync` flag
2. Monitors network connectivity
3. Syncs pending data when connectivity is restored

```swift
class SyncService {
    static let shared = SyncService()
    
    // Network monitoring
    private let monitor = NWPathMonitor()
    private var isOnline = false
    
    // Start monitoring network status
    func startMonitoring() {
        // Implementation details...
    }
    
    // Attempt to sync offline data
    func attemptSync() async {
        // Implementation details...
    }
}
```

## Authentication Token Storage

For task **AUTH-01**, securely store the authentication token using Apple's recommended practices:

```swift
func storeAuthToken(_ token: String) {
    // Store in Keychain
    let query: [String: Any] = [
        kSecClass as String: kSecClassGenericPassword,
        kSecAttrAccount as String: "DrMuscleAuthToken",
        kSecValueData as String: token.data(using: .utf8)!,
        kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlocked
    ]
    
    SecItemDelete(query as CFDictionary)
    SecItemAdd(query as CFDictionary, nil)
}
```

## References

- Existing C# API Client: `DrMuscleRestClient.cs`
- Backend API Documentation: [Internal Link]
- Apple Authentication Services: [Apple Documentation](https://developer.apple.com/documentation/authenticationservices)