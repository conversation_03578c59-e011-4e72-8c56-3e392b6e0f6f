# Dr. Muscle Watch App - Workout Flow Tasks (V1)

## Purpose

This file lists the development tasks directly related to the user's active workout flow for the V1 Dr. Muscle Apple Watch app.

## Instructions

*   Mark tasks as "Pending", "In Progress", or "Completed".
*   Reference related ADRs or specific requirements where applicable.
*   Follow the TDD cycle (Red, Green, Refactor, Documentation) for each scenario within a task, as defined in `docs/rules.md`.
*   For API-related tasks, refer to `docs/api-reference.md` for detailed endpoint specifications, data models, and implementation patterns.
*   For UI-related tasks, refer to `docs/ui-components.md` for detailed component documentation, styling guidelines, and implementation patterns. The actual UI components can be found in `v1/DrMuscleWatchApp/Components/` and views in `v1/DrMuscleWatchApp/Views/`.

## Workout Flow Tasks

*   **Task ID:** WKOUT-LIST-01
*   **Status:** Completed
*   **Task:** Fetch and display list of available workouts from API after login.
*   **Completion Date:** May 4, 2024
*   **Implementation Notes:** Created WorkoutListViewModel with state management for loading, empty, error, and loaded states. Implemented WorkoutListView with list, empty state, error state, and loading state. Updated ContentView to show WorkoutListView when authenticated. Added comprehensive tests for all states and scenarios.
    *   **Acceptance Criteria:**
        *   **Scenario:** Display Workout List on Successful Fetch
            *   Given the user is authenticated
            *   When the main authenticated view (Workout List screen) appears
            *   Then the app calls the API Client to fetch the list of workouts (using endpoint `/api/Workout/GetUserWorkoutTemplateGroup` as specified in `docs/api-reference.md`).
            *   And the API returns a successful response containing a list of workouts (e.g., `[{ "id": 1, "name": "Push Day" }, { "id": 2, "name": "Leg Day" }]`)
            *   Then the screen displays the names of the fetched workouts (e.g., "Push Day", "Leg Day") in a list format.
        *   **Scenario:** Display Message When No Workouts Available
            *   Given the user is authenticated
            *   When the Workout List screen appears
            *   And the API returns a successful response with an empty list of workouts
            *   Then the screen displays a message indicating there are no workouts scheduled (e.g., "No workout scheduled for today.").
        *   **Scenario:** Display Error on API Failure
            *   Given the user is authenticated
            *   When the Workout List screen appears
            *   And the API call to fetch workouts fails (e.g., network error, server error)
            *   Then the screen displays an appropriate error message (e.g., "Failed to load workouts. Please check connection.").
            *   And a retry mechanism might be offered (Optional for V1, but good practice).
    *   **Reference:** See `docs/api-reference.md` for endpoint details and the `WorkoutTemplateGroupModel` data structure.
        *   **Scenario:** Selecting a Workout
            *   Given the workout list is displayed with one or more workouts
            *   When the user taps on a specific workout in the list (e.g., "Push Day")
            *   Then the app navigates to the Pre-Workout Detail screen for the selected workout.
    *   **Depends On:** AUTH-01, API-01

*   **Task ID:** WKOUT-DETAIL-01
*   **Status:** Completed
*   **Task:** Implement Pre-Workout Screen: Display selected workout name and list of exercises (fetched from API).
*   **Completion Date:** May 4, 2024
*   **Implementation Notes:** Created WorkoutDetailViewModel with state management for loading, error, and loaded states. Implemented WorkoutDetailView with workout name, exercise list, and "Start Workout" button. Updated WorkoutListView to navigate to WorkoutDetailView when a workout is selected. Added comprehensive tests for all states and scenarios. Refactored the UI to show warm-up and work sets separately for each exercise.
    *   **Acceptance Criteria:**
        *   **Scenario:** Display Workout Details Successfully
            *   Given the user has selected a workout from the workout list (e.g., "Push Day" with ID 1)
            *   When the Pre-Workout screen appears for workout ID 1
            *   Then the app calls the API Client to fetch the details for workout ID 1 (using endpoint `/api/Workout/GetUserWorkout` as specified in `docs/api-reference.md`).
            *   And the API returns a successful response containing the workout name and a list of exercises with their details (e.g., `{"id": 1, "name": "Push Day", "exercises": [{"id": 10, "name": "Bench Press", "sets": [...]}, {"id": 11, "name": "Overhead Press", "sets": [...]}]}`).
            *   Then the screen displays the workout name prominently (e.g., "Push Day").
            *   And the screen displays the names of the exercises in the correct order (e.g., "Bench Press", "Overhead Press").
            *   And a "Start Workout" button is displayed.
        *   **Scenario:** Handle API Error When Fetching Details
            *   Given the user has selected a workout from the workout list
            *   When the Pre-Workout screen appears
            *   And the API call to fetch workout details fails
            *   Then the screen displays an appropriate error message (e.g., "Failed to load workout details.").
            *   And the "Start Workout" button might be hidden or disabled.
            *   And a back navigation option should be available.
        *   **Scenario:** Start Workout Navigation
            *   Given the Pre-Workout screen is displayed with workout details and the "Start Workout" button
            *   When the user taps the "Start Workout" button
            *   Then the app initiates the workout session (including starting HealthKit tracking - Task HK-02).
            *   And the app navigates to the Set Screen for the *first set* of the *first exercise* in the fetched workout plan.
            *   And the workout data (exercises, sets, etc.) is stored locally (Task STORAGE-01).
    *   **Depends On:** WKOUT-LIST-01, API-01, STORAGE-01, HK-02
    *   **Reference:** See `docs/api-reference.md` for endpoint details and the `WorkoutTemplateModel` data structure.

*   **Task ID:** WKOUT-EXEC-01
*   **Status:** Completed
*   **Task:** Implement basic Set Screen UI (Display Exercise Name, Target Reps, Target Weight from API data/local storage).
*   **Completion Date:** May 4, 2024
*   **Implementation Notes:** Created SetViewModel with state management for loading, error, and loaded states. Updated SetView to use SetViewModel and display exercise name, target reps, and target weight. Added set type indicator (warmup or work set) and set counter. Implemented navigation from WorkoutDetailView to SetView with the correct parameters. Added comprehensive tests for all states and scenarios.
    *   **Reference:** See `docs/api-reference.md` for the API endpoint `/api/Exercise/GetRecommendationForExercise` and the `RecommendationModel` data structure.
    *   **Acceptance Criteria:**
        *   **Scenario:** Display First Set Details
            *   Given the user has started a workout and the plan is stored locally
            *   And the app has navigated to the Set Screen for the first set of the first exercise (e.g., Bench Press, Target: 8 reps, 100 lbs)
            *   When the Set Screen appears
            *   Then the screen displays the current Exercise Name ("Bench Press").
            *   Then the screen displays the Target Reps ("8").
            *   Then the screen displays the Target Weight ("100").
            *   Then the screen displays the Weight Unit ("lbs" or "kg", based on user settings/API data).
            *   Then the main action button displays the calculated performance text (e.g., "Save +0.00%" or similar initial state - Task DYN-BTN-01).
        *   **Scenario:** Display Subsequent Set Details
            *   Given the user has completed a set (and any subsequent RIR/rest) from the locally stored plan
            *   And the app navigates to the next set of the *same* exercise (e.g., Bench Press, Set 2, Target: 8 reps, 105 lbs)
            *   When the Set Screen appears for this next set
            *   Then the screen displays the current Exercise Name ("Bench Press").
            *   Then the screen displays the *new* Target Reps ("8").
            *   Then the screen displays the *new* Target Weight ("105").
            *   Then the main action button displays the *updated* calculated performance text.
        *   **Scenario:** Display Set Details for Next Exercise
            *   Given the user has completed the last set of an exercise (and the inter-exercise transition) from the locally stored plan
            *   And the app navigates to the first set of the *next* exercise (e.g., Overhead Press, Target: 10 reps, 50 lbs)
            *   When the Set Screen appears for this new exercise's set
            *   Then the screen displays the *new* Exercise Name ("Overhead Press").
            *   Then the screen displays the Target Reps ("10").
            *   Then the screen displays the Target Weight ("50").
            *   Then the main action button displays the calculated performance text relevant to *this* exercise.
    *   **Depends On:** WKOUT-DETAIL-01, STORAGE-01

*   **Task ID:** INPUT-01
*   **Status:** Completed
*   **Task:** Implement Tap-to-Open Picker for Reps input.
*   **Completion Date:** May 4, 2024
*   **Implementation Notes:** Enhanced SetViewModel to recalculate performance percentage when reps change. Updated ValueDisplay component to make it visually clear that it's tappable with a background and indicator. Added proper handling for sheet dismissal to preserve original value when dismissed without selection. Ensured the picker is properly titled and initially scrolled to the current value.
    *   **Acceptance Criteria:**
        *   **Scenario:** Open Reps Picker
            *   Given the user is on the Set Screen displaying target reps (e.g., "8")
            *   When the user taps on the displayed reps value ("8")
            *   Then a modal sheet appears presenting the Reps Picker UI.
            *   And the picker is titled appropriately (e.g., "Select Reps").
            *   And the picker displays a scrollable list of numbers (e.g., 1 to 100).
            *   And the picker is initially scrolled to and highlights the current target reps value ("8").
        *   **Scenario:** Select New Reps Value
            *   Given the Reps Picker sheet is presented
            *   And the current value is "8"
            *   When the user scrolls and taps on a different value (e.g., "10")
            *   Then the modal sheet is dismissed.
            *   And the Set Screen updates to display the newly selected reps value ("10").
            *   And the underlying state holding the reps for the current set is updated to 10.
            *   And the dynamic save button text recalculates based on the new reps value (Task DYN-BTN-01).
        *   **Scenario:** Dismiss Picker Without Changing Value
            *   Given the Reps Picker sheet is presented
            *   When the user dismisses the sheet without selecting a new value (e.g., swipes down)
            *   Then the modal sheet is dismissed.
            *   And the displayed reps value on the Set Screen remains unchanged.
    *   **Depends On:** WKOUT-EXEC-01
    *   **ADR:** `docs/adr/0005-tap-to-open-picker-input.md`

*   **Task ID:** INPUT-02
*   **Status:** Completed
*   **Task:** Implement Tap-to-Open Picker for Weight input.
*   **Completion Date:** May 5, 2024
*   **Implementation Notes:** Verified that the weight picker functionality was already implemented correctly in the SetView. The SetView has the isWeightPickerPresented state variable, the sheet for the weight picker is set up with the correct binding, the ValueDisplay component for weight has the tap action to toggle the weight picker, and the SetViewModel has the updateWeight method to update the weight value. Added a comprehensive test to verify that the weight picker updates the view model correctly when a new weight is selected.
    *   **Acceptance Criteria:**
        *   **Scenario:** Open Weight Picker
            *   Given the user is on the Set Screen displaying target weight (e.g., "100")
            *   When the user taps on the displayed weight value ("100")
            *   Then a modal sheet appears presenting the Weight Picker UI.
            *   And the picker is titled appropriately (e.g., "Select Weight").
            *   And the picker displays a scrollable list of numbers within a reasonable range (e.g., 0 to 500 or based on unit).
            *   And the picker is initially scrolled to and highlights the current target weight value ("100").
        *   **Scenario:** Select New Weight Value
            *   Given the Weight Picker sheet is presented
            *   And the current value is "100"
            *   When the user scrolls and taps on a different value (e.g., "105")
            *   Then the modal sheet is dismissed.
            *   And the Set Screen updates to display the newly selected weight value ("105").
            *   And the underlying state holding the weight for the current set is updated to 105.
            *   And the dynamic save button text recalculates based on the new weight value (Task DYN-BTN-01).
        *   **Scenario:** Dismiss Picker Without Changing Value
            *   Given the Weight Picker sheet is presented
            *   When the user dismisses the sheet without selecting a new value
            *   Then the modal sheet is dismissed.
            *   And the displayed weight value on the Set Screen remains unchanged.
    *   **Depends On:** WKOUT-EXEC-01
    *   **ADR:** `docs/adr/0005-tap-to-open-picker-input.md`

*   **Task ID:** LOGGING-01
*   **Status:** Completed
*   **Task:** Implement saving completed set data (reps, weight) to local storage.
*   **Completion Date:** May 5, 2024
*   **Implementation Notes:** Added storageService property to SetViewModel. Implemented saveCurrentSet method in SetViewModel that saves the current set data to local storage using the StorageService. Updated SetView to call saveCurrentSet when the user taps the "Save Set" button. Updated RIRPicker to save the set with the selected RIR value.
    *   **Acceptance Criteria:**
        *   **Scenario:** Save Set Data Locally (Non-RIR Set)
            *   Given the user is on the Set Screen for a set that is *not* the first work set
            *   And the user has potentially adjusted reps to 10 and weight to 100 lbs
            *   When the user taps the "Save Set" button
            *   Then a new SetLog record is created in the Core Data context.
            *   And the record contains the correct exercise identifier, workout identifier, performed reps (10), performed weight (100), and a timestamp.
            *   And the record's RIR attribute is marked as not applicable or null.
            *   And the record is flagged as `needsSync = true`.
            *   And the Core Data context is saved successfully.
        *   **Scenario:** Save Set Data Locally (RIR Set - covered by RIR-02)
            *   *(This specific scenario is handled by RIR-02, which includes saving the RIR value)*
    *   **Depends On:** STORAGE-01, WKOUT-EXEC-01

*   **Task ID:** RIR-01
*   **Status:** Completed
*   **Task:** Implement RIR Picker UI (using descriptive options).
*   **Completion Date:** May 5, 2024
*   **Implementation Notes:** Updated RIRPicker component to use descriptive options instead of numeric values. Added 5 options: "Very hard (0 left)", "Could do 1-2 more", "Could do 3-4 more", "Could do 5-6 more", "Could do 7+ more". Improved UI with larger tap targets and better visual design. Added a Cancel button to dismiss the picker without making a selection. Created tests for the RIRPicker component.
    *   **Acceptance Criteria:**
        *   **Scenario:** Display RIR Picker
            *   Given the app needs to prompt the user for RIR
            *   When the RIR Picker view is presented (modally)
            *   Then the view displays a list of tappable options corresponding to RIR levels (e.g., "Very hard", "Could do 1-2 more", "Could do 3-4 more", "Could do 5-6 more", "Could do 7+ more").
            *   And the options are easily selectable (sufficient tap target size).
    *   **Depends On:** SETUP-01
    *   **ADR:** `docs/adr/0006-rir-capture-after-first-work-set.md`

*   **Task ID:** RIR-02
*   **Status:** Completed
*   **Task:** Implement logic to display RIR picker only after the first work set (requires API flags) and save RIR value locally.
*   **Completion Date:** May 6, 2024
*   **Implementation Notes:** Added shouldShowRIRPicker method to SetViewModel that checks if the current set is the first work set and not a warmup set. Updated SetView to use this method when deciding whether to show the RIR picker. Added comprehensive tests to verify the RIR picker is shown only for the first work set, not for warmup sets, and not for subsequent work sets. Verified that RIR values are saved correctly with the set.
    *   **Reference:** See `docs/api-reference.md` for the required API flags, particularly the `isFirstWorkSet` flag in the `SetModel` data structure.
    *   **Acceptance Criteria:**
        *   **Scenario:** Prompt for RIR After First Work Set
            *   Given the user is on the Set Screen for a set identified by the API/local plan as `isFirstWorkSet = true`
            *   When the user taps "Save Set"
            *   Then the RIR Picker UI (Task RIR-01) is presented *before* the rest timer starts.
        *   **Scenario:** Do Not Prompt for RIR After Warm-up Set
            *   Given the user is on the Set Screen for a set identified by the API/local plan as `isWarmup = true` (and `isFirstWorkSet = false`)
            *   When the user taps "Save Set"
            *   Then the RIR Picker UI is *not* presented.
            *   And the app proceeds directly to the rest timer logic (Task TIMER-01).
        *   **Scenario:** Do Not Prompt for RIR After Subsequent Work Set
            *   Given the user is on the Set Screen for a work set that is *not* the first one (e.g., `isFirstWorkSet = false`, `isWarmup = false`)
            *   When the user taps "Save Set"
            *   Then the RIR Picker UI is *not* presented.
            *   And the app proceeds directly to the rest timer logic (Task TIMER-01).
        *   **Scenario:** Save Set Data with RIR Value
            *   Given the RIR Picker is presented after the first work set
            *   And the user selects an RIR option (e.g., "Could do 1-2 more", corresponding to RIR value 1 or 2)
            *   When the picker is dismissed
            *   Then a new SetLog record is created/saved in Core Data (as in LOGGING-01).
            *   And the record includes the performed reps and weight.
            *   And the record includes the selected RIR value (e.g., 1 or 2).
            *   And the record is flagged as `needsSync = true`.
            *   And the app proceeds to the rest timer logic (Task TIMER-01).
        *   **Scenario:** Handle Missing API Flag Gracefully (Assumption: API provides flag)
            *   Given the API *fails* to provide the `isFirstWorkSet` flag for a set in the downloaded plan
            *   When the user taps "Save Set"
            *   Then the app should default to *not* showing the RIR picker (to avoid incorrect prompts).
            *   And the set is logged without an RIR value.
            *   *(This highlights the critical dependency on the API)*.
    *   **Depends On:** LOGGING-01, RIR-01, API-01 (for flags in plan)
    *   **ADR:** `docs/adr/0006-rir-capture-after-first-work-set.md`

*   **Task ID:** TIMER-01
*   **Status:** Completed
*   **Task:** Implement intra-set rest timer logic (start automatically after save/RIR, display countdown on button).
*   **Completion Date:** May 7, 2024
*   **Implementation Notes:** Created TimerButton component that extends CTAButton functionality to display a countdown timer. Added timer properties and methods to SetViewModel (isTimerActive, secondsRemaining, startTimer, timerTick, timerComplete, skipTimer). Updated SetView to use the TimerButton and integrate with the timer functionality. Implemented timer skipping by tapping the button during countdown. Added comprehensive tests for all timer functionality.
    *   **Acceptance Criteria:**
        *   **Scenario:** Timer Starts Automatically After Non-RIR Set Save
            *   Given the user saves a set that does *not* require an RIR prompt
            *   And the workout plan specifies a rest duration of 90 seconds for the next interval
            *   When the set save operation completes
            *   Then the main action button's label changes to display a countdown starting from "Rest 1:30".
            *   And the countdown decreases accurately every second (e.g., "Rest 1:29", "Rest 1:28"...).
        *   **Scenario:** Timer Starts Automatically After RIR Selection
            *   Given the user saves the first work set
            *   And the RIR picker is displayed and the user selects an RIR value
            *   And the workout plan specifies a rest duration of 120 seconds for the next interval
            *   When the RIR picker is dismissed and the RIR value is saved
            *   Then the main action button's label changes to display a countdown starting from "Rest 2:00".
            *   And the countdown decreases accurately every second.
        *   **Scenario:** Timer Reaches Zero
            *   Given the rest timer is counting down on the main action button
            *   When the countdown reaches "Rest 0:00"
            *   Then a notification is triggered (Haptic, Sound, Visual - specific implementation TBD).
            *   And the main action button's label changes back to "Save Set".
            *   And the app is ready for the user to start the next set.
        *   **Scenario:** Skipping Rest by Tapping Timer Button
            *   Given the rest timer is counting down on the main action button (e.g., "Rest 1:15")
            *   When the user taps the main action button while the timer is active
            *   Then the timer stops immediately.
            *   And the main action button's label changes back to "Save Set".
            *   And the app is ready for the user to start the next set.
        *   **Scenario:** Timer Continues if App Backgrounded (Best Effort)
            *   Given the rest timer is counting down
            *   When the user briefly backgrounds the app (e.g., checks a notification) and returns
            *   Then the timer should reflect the elapsed time as accurately as possible (within watchOS background execution limits).
            *   *(Note: Precise background timers on watchOS can be tricky; aim for reasonable accuracy)*.
    *   **Depends On:** LOGGING-01, RIR-02

*   **Task ID:** DYN-BTN-01
*   **Status:** Completed
*   **Task:** Implement calculation and display of performance % change on Save button (requires API historical data).
*   **Completion Date:** May 9, 2024
*   **Implementation Notes:** Created models for historical performance data (HistoryModel, OneRMModel). Added API client methods to fetch historical data. Enhanced SetViewModel to fetch and use historical data for performance calculation. Implemented Epley formula for 1RM calculation (weight * (1 + 0.0333 * reps)). Added comprehensive unit tests for performance percentage calculation with various scenarios.
    *   **Acceptance Criteria:**
        *   **Scenario:** Display Initial Performance on Set Screen (First Time Exercise)
            *   Given the user is on the Set Screen for an exercise they haven't performed recently (or ever)
            *   And the API response for workout details indicates no relevant historical data for this exercise/rep/weight range
            *   When the Set Screen appears
            *   Then the main action button displays a neutral or baseline text (e.g., "Save Set" or "Save +0.00%").
        *   **Scenario:** Display Positive Performance Change
            *   Given the user is on the Set Screen for an exercise (e.g., Target: 10 reps @ 100 lbs)
            *   And the API provided historical data indicating the last comparable performance was equivalent to 95 lbs for 10 reps
            *   When the Set Screen appears
            *   Then the performance change is calculated based on the target reps/weight vs historical data.
            *   And the main action button displays the positive percentage change (e.g., "Save +5.26%").
        *   **Scenario:** Display Negative Performance Change
            *   Given the user is on the Set Screen for an exercise (e.g., Target: 8 reps @ 150 lbs)
            *   And the API provided historical data indicating the last comparable performance was equivalent to 160 lbs for 8 reps
            *   When the Set Screen appears
            *   Then the performance change is calculated.
            *   And the main action button displays the negative percentage change (e.g., "Save -6.25%").
        *   **Scenario:** Display No Performance Change
            *   Given the user is on the Set Screen for an exercise (e.g., Target: 12 reps @ 50 lbs)
            *   And the API provided historical data indicating the last comparable performance was equivalent to 50 lbs for 12 reps
            *   When the Set Screen appears
            *   Then the performance change is calculated as zero.
            *   And the main action button displays a neutral indicator (e.g., "Save +0.00%").
        *   **Scenario:** Performance Recalculates After Reps Change
            *   Given the Set Screen is displaying a performance change (e.g., "Save *****%") based on target reps/weight
            *   When the user uses the Reps Picker to change the target reps (e.g., increases reps)
            *   Then the performance change is recalculated based on the *new* reps value and the target weight vs historical data.
            *   And the main action button text updates immediately to reflect the *new* percentage change.
        *   **Scenario:** Performance Recalculates After Weight Change
            *   Given the Set Screen is displaying a performance change (e.g., "Save *****%") based on target reps/weight
            *   When the user uses the Weight Picker to change the target weight (e.g., increases weight)
            *   Then the performance change is recalculated based on the target reps and the *new* weight value vs historical data.
            *   And the main action button text updates immediately to reflect the *new* percentage change.
    *   **Depends On:** WKOUT-EXEC-01, API-01 (for historical data), INPUT-01, INPUT-02
    *   **Note:** Requires the specific calculation logic (e.g., estimated 1RM comparison) used in the main app/alpha to be implemented. API must provide necessary historical data points.

*   **Task ID:** ADDSET-01
*   **Status:** Completed
*   **Task:** Implement "Add Set" / "Next Exercise" choice screen after last planned set.
*   **Completion Date:** May 8, 2024
*   **Implementation Notes:** Added showingChoiceScreen state to SetViewModel to track when the choice screen should be shown. Updated saveCurrentSet method to show the choice screen when isLastPlannedSet is true. Added shouldShowChoiceScreen method to SetViewModel to check if the choice screen should be shown. Updated SetView to show different content based on whether the choice screen should be shown. Added "Add Set" button and "Next Exercise" button to the choice screen. Implemented addSet and nextExercise methods in SetViewModel to handle the respective actions. Added comprehensive tests for all scenarios.
    *   **Acceptance Criteria:**
        *   **Scenario:** Choice Screen Appears After Last Planned Set
            *   Given the user is on the Set Screen for a set identified by the API/local plan as `isLastPlannedSet = true`
            *   When the user saves the set (and completes RIR prompt if applicable)
            *   Then the screen transitions to the choice layout *immediately*.
            *   And a tappable text label "Add Set" is displayed (secondary action area).
            *   And the main action button displays the fixed 1-minute countdown timer labeled "Next Exercise 1:00".
        *   **Scenario:** Choice Screen Does Not Appear After Non-Last Set
            *   Given the user is on the Set Screen for a set identified by the API/local plan as `isLastPlannedSet = false`
            *   When the user saves the set (and completes RIR prompt if applicable)
            *   Then the screen does *not* show the "Add Set" text label.
            *   And the app proceeds directly to the standard intra-set rest timer (Task TIMER-01).
    *   **Depends On:** LOGGING-01, RIR-02, API-01 (for flags in plan)

*   **Task ID:** ADDSET-02
*   **Status:** Completed
*   **Task:** Implement "Add Set" action (revert to Set Screen with "Save Set" button).
*   **Completion Date:** May 10, 2024
*   **Implementation Notes:** Enhanced the addSet method in SetViewModel to preserve the reps and weight from the completed set. Added clear documentation in the code to explain the behavior. Created a comprehensive test (testAddSetPreservesRepsAndWeight) to verify that the reps and weight are preserved when adding a set.
    *   **Acceptance Criteria:**
        *   **Scenario:** Tapping "Add Set" Reverts Screen
            *   Given the choice screen is displayed with the "Add Set" text label and the "Next Exercise 1:XX" timer running
            *   When the user taps the "Add Set" text label
            *   Then the "Add Set" text label disappears.
            *   And the screen layout reverts to the standard Set Screen (Exercise Name, Reps, Weight, Main Button).
            *   And the Reps and Weight displayed are copied from the set that was just completed.
            *   And the main action button stops the "Next Exercise" timer.
            *   And the main action button's label changes immediately to "Save Set".
            *   And the internal state reflects that an additional set for the current exercise has been added.
    *   **Depends On:** ADDSET-01

*   **Task ID:** TRANSITION-01
*   **Status:** Completed
*   **Task:** Implement "Next Exercise" action (start 1-min timer, handle skip, transition on complete/skip/expiry).
*   **Completion Date:** May 11, 2024
*   **Implementation Notes:** Created a reusable CheckmarkView component for the transition animation. Enhanced SetViewModel with transition state management and navigation logic. Updated SetView to handle the transition and navigation. Added comprehensive tests for all transition scenarios. Refactored the code to improve maintainability and reusability.
    *   **Acceptance Criteria:**
        *   **Scenario:** Skipping Inter-Exercise Rest
            *   Given the choice screen is displayed with the "Add Set" text label and the "Next Exercise 1:XX" timer running
            *   When the user taps the main action button (showing "Next Exercise 1:XX")
            *   Then the "Add Set" text label disappears.
            *   And the timer stops immediately.
            *   And the checkmark/progress animation plays.
            *   And the app navigates to the Set Screen for the first set of the *next* exercise (if one exists).
            *   Or the app navigates to the Workout Complete screen (if this was the last exercise).
        *   **Scenario:** Inter-Exercise Rest Timer Completes Naturally
            *   Given the choice screen is displayed with the "Add Set" text label and the "Next Exercise 1:XX" timer running
            *   And the user does *not* tap "Add Set" or the main action button
            *   When the "Next Exercise" timer reaches 0:00
            *   Then the main action button's label changes to static text "Next Exercise".
            *   And the "Add Set" text label remains visible.
            *   And the screen remains in the choice state indefinitely.
        *   **Scenario:** Proceeding After Timer Expiry
            *   Given the choice screen is displayed, the timer has expired, and the main button shows static "Next Exercise"
            *   When the user taps the main action button ("Next Exercise")
            *   Then the "Add Set" text label disappears.
            *   And the checkmark/progress animation plays.
            *   And the app navigates to the Set Screen for the first set of the *next* exercise (if one exists).
            *   Or the app navigates to the Workout Complete screen (if this was the last exercise).
        *   **Scenario:** Transition to Workout Complete Screen
            *   Given the user has just completed the flow for the *last set* of the *last exercise* in the workout plan
            *   When the user triggers the transition (either by skipping rest or proceeding after timer expiry)
            *   Then the checkmark/progress animation plays.
            *   And the app navigates to the Workout Complete screen (Task WKOUT-COMP-01).
    *   **Depends On:** ADDSET-01, WKOUT-EXEC-01, WKOUT-COMP-01

*   **Task ID:** WKOUT-COMP-01
*   **Status:** Completed
*   **Task:** Implement Workout Complete screen and logic to mark workout as finished locally.
*   **Completion Date:** May 12, 2024
*   **Implementation Notes:** Created WorkoutCompleteViewModel with methods to mark workout as completed locally and sync with server. Created WorkoutCompleteView with congratulations message and finish button. Updated SetViewModel to navigate to WorkoutCompleteView when the last exercise is completed. Added HealthKitService to handle workout tracking. Implemented error handling for storage and API errors.
    *   **Acceptance Criteria:**
        *   **Scenario:** Display Workout Complete Screen
            *   Given the user has navigated from the last exercise transition (Task TRANSITION-01)
            *   When the Workout Complete screen appears
            *   Then the screen displays a confirmation message (e.g., "Workout Complete!", "Good Job!").
            *   And it might display summary statistics if available (e.g., total time, number of sets - *Optional for V1*).
            *   And a "Finish" or "Done" button is displayed.
        *   **Scenario:** Mark Workout Complete Locally
            *   Given the Workout Complete screen is displayed
            *   When the user taps the "Finish" / "Done" button
            *   Then the local workout state is marked as completed in Core Data.
            *   And the HealthKit session is stopped and saved (Task HK-03).
            *   And the app attempts to trigger a final sync of any remaining data (Task SYNC-01).
            *   And the app navigates back to the main view (e.g., the Workout List screen).
        *   **Scenario:** Data Flagged for Sync on Completion
            *   Given the workout is marked as complete locally
            *   Then the overall workout completion status is flagged as `needsSync = true` in local storage (or relevant SetLogs are already flagged).
    *   **Depends On:** TRANSITION-01, STORAGE-01, HK-03, SYNC-01

*   **Task ID:** TRANSITION-02
*   **Status:** Completed
*   **Task:** Enhance exercise transition with performance feedback and haptic confirmation.
*   **Completion Date:** May 17, 2024
*   **Implementation Notes:** Added CheckmarkPerformanceView to display a checkmark animation with performance percentage when transitioning between exercises. Renamed "Next Exercise" button to "Finish Exercise" for clarity. Integrated HapticService to provide tactile feedback during transitions. Updated ChoiceViewModel to calculate and display performance metrics. Added loading state to prevent multiple button taps during transition.
    *   **Acceptance Criteria:**
        *   **Scenario:** Tapping "Finish Exercise" Shows Performance Feedback
            *   Given the choice screen is displayed after completing the last planned set
            *   When the user taps the "Finish Exercise" button
            *   Then the button shows a loading indicator to prevent multiple taps.
            *   And the exercise is marked as complete in the backend.
            *   And a haptic success feedback is played.
            *   And a checkmark animation appears with the performance percentage for the completed exercise.
            *   And after the animation completes, the app automatically transitions to the next exercise.
        *   **Scenario:** Performance Percentage Shows Improvement
            *   Given the user has completed an exercise with better performance than previous attempts
            *   When the checkmark animation appears during transition
            *   Then the animation displays a positive percentage (e.g., "+5.2%").
            *   And the text is formatted with a "+" sign and one decimal place.
        *   **Scenario:** Performance Percentage Shows Decline
            *   Given the user has completed an exercise with worse performance than previous attempts
            *   When the checkmark animation appears during transition
            *   Then the animation displays a negative percentage (e.g., "-2.1%").
            *   And the text is formatted with one decimal place.
        *   **Scenario:** Workout Completion Shows Overall Performance
            *   Given the user has completed the last exercise in a workout
            *   When the user taps the "Finish Workout" button
            *   Then a checkmark animation appears with the overall workout performance percentage.
            *   And after the animation completes, the app navigates to the Workout Complete screen.
    *   **Depends On:** TRANSITION-01, DYN-BTN-01
    *   **Note:** This enhancement improves user feedback during exercise transitions by providing visual and tactile confirmation along with performance metrics, reinforcing the app's focus on progressive overload.
