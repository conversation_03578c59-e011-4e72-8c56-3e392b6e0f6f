import XCTest
import Swift<PERSON>
@testable import DrMuscleWatchApp

class RIRPickerLogicTests: XCTestCase {

    func testRIRPickerDisplayedForFirstWorkSet() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockStorageService = MockStorageService()

        let mockSets = [
            SetRecommendationModel(
                reps: 8,
                weight: WeightModel(value: 100, unit: .lb),
                isWarmup: false,
                isFirstWorkSet: true, // This is the first work set
                isLastPlannedSet: false,
                restDurationSeconds: 90
            )
        ]
        let mockRecommendation = RecommendationModel(
            sets: mockSets,
            exerciseId: 1,
            exerciseName: "Bench Press",
            isBodyweight: false,
            isWeighted: true,
            isAssisted: false,
            isTimeBased: false,
            isUnilateral: false
        )

        mockAPIClient.mockRecommendation = mockRecommendation

        let viewModel = SetViewModel(
            exerciseId: 1,
            workoutId: 1,
            apiClient: mockAPIClient,
            storageService: mockStorageService
        )

        // When
        await viewModel.fetchExerciseDetails()

        // Then
        XCTAssertTrue(viewModel.isFirstWorkSet, "isFirstWorkSet should be true for the first work set")
        XCTAssertFalse(viewModel.isWarmupSet, "isWarmupSet should be false for the first work set")

        // Test the shouldShowRIRPicker method
        XCTAssertTrue(viewModel.shouldShowRIRPicker(), "shouldShowRIRPicker should return true for the first work set")
    }

    func testRIRPickerNotDisplayedForWarmupSet() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockStorageService = MockStorageService()

        let mockSets = [
            SetRecommendationModel(
                reps: 8,
                weight: WeightModel(value: 100, unit: .lb),
                isWarmup: true, // This is a warmup set
                isFirstWorkSet: false,
                isLastPlannedSet: false,
                restDurationSeconds: 60
            )
        ]
        let mockRecommendation = RecommendationModel(
            sets: mockSets,
            exerciseId: 1,
            exerciseName: "Bench Press",
            isBodyweight: false,
            isWeighted: true,
            isAssisted: false,
            isTimeBased: false,
            isUnilateral: false
        )

        mockAPIClient.mockRecommendation = mockRecommendation

        let viewModel = SetViewModel(
            exerciseId: 1,
            workoutId: 1,
            apiClient: mockAPIClient,
            storageService: mockStorageService
        )

        // When
        await viewModel.fetchExerciseDetails()

        // Then
        XCTAssertTrue(viewModel.isWarmupSet, "isWarmupSet should be true for a warmup set")
        XCTAssertFalse(viewModel.isFirstWorkSet, "isFirstWorkSet should be false for a warmup set")

        // Test the shouldShowRIRPicker method
        XCTAssertFalse(viewModel.shouldShowRIRPicker(), "shouldShowRIRPicker should return false for warmup sets")
    }

    func testRIRPickerNotDisplayedForSubsequentWorkSet() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockStorageService = MockStorageService()

        let mockSets = [
            SetRecommendationModel(
                reps: 8,
                weight: WeightModel(value: 100, unit: .lb),
                isWarmup: false,
                isFirstWorkSet: false, // This is not the first work set
                isLastPlannedSet: false,
                restDurationSeconds: 90
            )
        ]
        let mockRecommendation = RecommendationModel(
            sets: mockSets,
            exerciseId: 1,
            exerciseName: "Bench Press",
            isBodyweight: false,
            isWeighted: true,
            isAssisted: false,
            isTimeBased: false,
            isUnilateral: false
        )

        mockAPIClient.mockRecommendation = mockRecommendation

        let viewModel = SetViewModel(
            exerciseId: 1,
            workoutId: 1,
            apiClient: mockAPIClient,
            storageService: mockStorageService
        )

        // When
        await viewModel.fetchExerciseDetails()

        // Then
        XCTAssertFalse(viewModel.isWarmupSet, "isWarmupSet should be false for a non-warmup set")
        XCTAssertFalse(viewModel.isFirstWorkSet, "isFirstWorkSet should be false for a subsequent work set")

        // Test the shouldShowRIRPicker method
        XCTAssertFalse(viewModel.shouldShowRIRPicker(), "shouldShowRIRPicker should return false for subsequent work sets")
    }

    func testSaveSetWithRIRValue() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockStorageService = MockStorageService()

        let mockSets = [
            SetRecommendationModel(
                reps: 8,
                weight: WeightModel(value: 100, unit: .lb),
                isWarmup: false,
                isFirstWorkSet: true, // This is the first work set
                isLastPlannedSet: false,
                restDurationSeconds: 90
            )
        ]
        let mockRecommendation = RecommendationModel(
            sets: mockSets,
            exerciseId: 1,
            exerciseName: "Bench Press",
            isBodyweight: false,
            isWeighted: true,
            isAssisted: false,
            isTimeBased: false,
            isUnilateral: false
        )

        mockAPIClient.mockRecommendation = mockRecommendation

        let viewModel = SetViewModel(
            exerciseId: 1,
            workoutId: 1,
            apiClient: mockAPIClient,
            storageService: mockStorageService
        )

        // When
        await viewModel.fetchExerciseDetails()
        let success = await viewModel.saveCurrentSet(rir: 3)

        // Then
        XCTAssertTrue(success, "Saving set with RIR should succeed")
        XCTAssertEqual(mockStorageService.lastSavedRIR, 3, "RIR value should be saved correctly")

        // The saveCurrentSet method already handles saving the RIR value correctly
    }

    func testHandleMissingAPIFlagGracefully() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockStorageService = MockStorageService()

        // Create a recommendation with no sets (simulating missing API data)
        let mockRecommendation = RecommendationModel(
            sets: [],
            exerciseId: 1,
            exerciseName: "Bench Press",
            isBodyweight: false,
            isWeighted: true,
            isAssisted: false,
            isTimeBased: false,
            isUnilateral: false
        )

        mockAPIClient.mockRecommendation = mockRecommendation

        let viewModel = SetViewModel(
            exerciseId: 1,
            workoutId: 1,
            apiClient: mockAPIClient,
            storageService: mockStorageService
        )

        // When
        await viewModel.fetchExerciseDetails()

        // Then
        XCTAssertFalse(viewModel.isFirstWorkSet, "isFirstWorkSet should default to false when API data is missing")

        // The SetViewModel already handles missing API flags gracefully by defaulting isFirstWorkSet to false
    }

    func testShouldShowRIRPickerLogic() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockStorageService = MockStorageService()

        // Create a view model with a mock implementation of shouldShowRIRPicker
        class TestViewModel: SetViewModel {
            override init(exerciseId: Int64, workoutId: Int64, apiClient: DrMuscleAPIClient, storageService: StorageService = MockStorageService()) {
                super.init(exerciseId: exerciseId, workoutId: workoutId, apiClient: apiClient, storageService: storageService)
            }

            func testShouldShowRIRPicker(isFirstWorkSet: Bool, isWarmup: Bool) -> Bool {
                // Implementation of the shouldShowRIRPicker logic for testing
                return isFirstWorkSet && !isWarmup
            }
        }

        let viewModel = TestViewModel(
            exerciseId: 1,
            workoutId: 1,
            apiClient: mockAPIClient,
            storageService: mockStorageService
        )

        // When/Then
        // Test various combinations of isFirstWorkSet and isWarmup

        // Case 1: First work set, not warmup - should show RIR picker
        XCTAssertTrue(viewModel.testShouldShowRIRPicker(isFirstWorkSet: true, isWarmup: false),
                     "Should show RIR picker for first work set")

        // Case 2: Not first work set, warmup - should not show RIR picker
        XCTAssertFalse(viewModel.testShouldShowRIRPicker(isFirstWorkSet: false, isWarmup: true),
                     "Should not show RIR picker for warmup set")

        // Case 3: Not first work set, not warmup - should not show RIR picker
        XCTAssertFalse(viewModel.testShouldShowRIRPicker(isFirstWorkSet: false, isWarmup: false),
                     "Should not show RIR picker for subsequent work set")

        // Case 4: First work set, warmup (edge case) - should not show RIR picker
        XCTAssertFalse(viewModel.testShouldShowRIRPicker(isFirstWorkSet: true, isWarmup: true),
                     "Should not show RIR picker for warmup set even if marked as first work set")
    }
}
