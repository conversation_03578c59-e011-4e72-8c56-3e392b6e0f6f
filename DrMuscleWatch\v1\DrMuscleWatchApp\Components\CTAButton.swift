import SwiftUI

/// A Call-to-Action button with the yellow gradient styling from the original app
struct CTAButton: View {
    let title: String
    let icon: String? // Optional system icon name
    let action: () -> Void
    let showPercentage: Bool
    let percentage: Double
    
    init(title: String, icon: String? = "arrow.right", percentage: Double = 0.0, showPercentage: Bool = false, action: @escaping () -> Void) {
        self.title = title
        self.icon = icon
        self.percentage = percentage
        self.showPercentage = showPercentage
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack {
                if showPercentage {
                    Text("\(title) \(percentage >= 0 ? "+" : "")\(String(format: "%.2f", percentage))%")
                        .font(AppTheme.ctaButtonTextStyle)
                        .foregroundColor(.black)
                } else {
                    Text(title)
                        .font(AppTheme.ctaButtonTextStyle)
                        .foregroundColor(.black)
                }
                
                if let iconName = icon {
                    Image(systemName: iconName)
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(.black)
                }
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        AppTheme.yellowGradientStart,
                        AppTheme.yellowGradientEnd
                    ]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
                .cornerRadius(AppTheme.cornerRadius)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}
