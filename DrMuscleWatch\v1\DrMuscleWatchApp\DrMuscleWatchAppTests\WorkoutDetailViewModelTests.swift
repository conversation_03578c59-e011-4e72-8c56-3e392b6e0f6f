import XCTest
@testable import DrMuscleWatchApp

class WorkoutDetailViewModelTests: XCTestCase {

    func testFetchWorkoutDetailsSuccess() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockExercises = [
            ExerciseModel(id: 1, name: "Bench Press", sets: []),
            ExerciseModel(id: 2, name: "Overhead Press", sets: [])
        ]
        let mockWorkout = WorkoutTemplateModel(id: 1, name: "Push Day", exercises: mockExercises)
        mockAPIClient.mockWorkoutTemplate = mockWorkout

        let viewModel = WorkoutDetailViewModel(workoutId: 1, apiClient: mockAPIClient)

        // When
        await viewModel.fetchWorkoutDetails()

        // Then
        XCTAssertFalse(viewModel.isLoading, "Loading state should be false after fetch completes")
        XCTAssertFalse(viewModel.hasError, "Error state should be false after successful fetch")
        XCTAssertEqual(viewModel.workout?.name, "Push Day", "Workout name should be Push Day")
        XCTAssertEqual(viewModel.workout?.exercises.count, 2, "Should have 2 exercises")
        XCTAssertEqual(viewModel.workout?.exercises[0].name, "Bench Press", "First exercise should be Bench Press")
        XCTAssertEqual(viewModel.workout?.exercises[1].name, "Overhead Press", "Second exercise should be Overhead Press")
    }

    func testFetchWorkoutDetailsError() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        mockAPIClient.shouldThrowError = true

        let viewModel = WorkoutDetailViewModel(workoutId: 1, apiClient: mockAPIClient)

        // When
        await viewModel.fetchWorkoutDetails()

        // Then
        XCTAssertFalse(viewModel.isLoading, "Loading state should be false after fetch completes")
        XCTAssertTrue(viewModel.hasError, "Error state should be true after failed fetch")
        XCTAssertNil(viewModel.workout, "Workout should be nil on error")
        XCTAssertNotNil(viewModel.errorMessage, "Error message should not be nil")
    }

    func testStartWorkoutSuccess() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockExercises = [
            ExerciseModel(id: 1, name: "Bench Press", sets: []),
            ExerciseModel(id: 2, name: "Overhead Press", sets: [])
        ]
        let mockWorkout = WorkoutTemplateModel(id: 1, name: "Push Day", exercises: mockExercises)
        mockAPIClient.mockWorkoutTemplate = mockWorkout

        let viewModel = WorkoutDetailViewModel(workoutId: 1, apiClient: mockAPIClient)
        await viewModel.fetchWorkoutDetails()

        // When
        let result = await viewModel.startWorkout()

        // Then
        XCTAssertTrue(result, "Starting workout should return true")
        XCTAssertFalse(viewModel.isLoading, "View model should not be in loading state")
        XCTAssertFalse(viewModel.hasError, "View model should not have an error")
        // In a real implementation, we would verify that the workout was stored locally
        // and that HealthKit tracking was started
    }

    func testStartWorkoutWithoutWorkout() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let viewModel = WorkoutDetailViewModel(workoutId: 1, apiClient: mockAPIClient)
        // Note: We're not fetching workout details, so workout is nil

        // When
        let result = await viewModel.startWorkout()

        // Then
        XCTAssertFalse(result, "Starting workout should return false when workout is nil")
        XCTAssertTrue(viewModel.hasError, "View model should have an error")
        XCTAssertNotNil(viewModel.errorMessage, "Error message should not be nil")
        XCTAssertTrue(viewModel.errorMessage?.contains("Cannot start workout") ?? false, "Error message should indicate workout details not loaded")
    }
}

// Mock API Client for testing
class MockDrMuscleAPIClient: DrMuscleAPIClient {
    var mockWorkoutTemplate: WorkoutTemplateModel?
    var shouldThrowError = false

    override func getUserWorkout(workoutId: Int) async throws -> WorkoutTemplateModel {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 0, userInfo: [NSLocalizedDescriptionKey: "Mock API error"])
        }

        return mockWorkoutTemplate ?? WorkoutTemplateModel(id: 0, name: "", exercises: [])
    }
}
