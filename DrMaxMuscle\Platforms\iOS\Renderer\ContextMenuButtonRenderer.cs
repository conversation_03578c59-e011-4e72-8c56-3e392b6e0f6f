﻿using System;
using System.ComponentModel;
using DrMaxMuscle.Controls;
using Microsoft.Maui.Controls.Compatibility.Platform.iOS;
using Microsoft.Maui.Handlers;
using UIKit;


//[assembly: ExportRenderer(typeof(ContextMenuButton), typeof(ContextMenuButtonRenderer))]
namespace DrMaxMuscle.Platforms.iOS.Renderer
{
    public class ContextMenuButtonRenderer : ImageButtonHandler
    {

        public ContextMenuButtonRenderer()
        {
            Mapper.AppendToMapping(nameof(ContextMenuButton), (handler, view) =>
            {
                try
                {
                    if (view is ContextMenuButton)
                    {
                        ((ContextMenuButton)view).GetCoordinates = () => GetCoordinatesNative(handler);
                    }
                }
                catch (Exception ex)
                {
                }
            });
        }


        private (int x, int y) GetCoordinatesNative(IImageButtonHandler buttonHandler)
        {
            if(buttonHandler != null)
            {
                try
                {
                    //let frame = yourButton.superview.convertRect(yourButton.frame, toView: self.view)
                    CoreGraphics.CGRect frme = buttonHandler.PlatformView.Superview.ConvertRectToView(buttonHandler.PlatformView.Frame, null);
                    return ((int)buttonHandler.PlatformView.Superview.Superview.Frame.Left + 120, (int)frme.Top + 20);
                }
                catch (Exception ex)
                {
                    return (120, 20);
                }
            }
            else
            {
                return (120, 20);
            }
            

        }
    }
}
