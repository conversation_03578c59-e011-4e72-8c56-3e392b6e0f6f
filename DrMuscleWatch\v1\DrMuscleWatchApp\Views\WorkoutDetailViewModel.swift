import Foundation
import SwiftUI

// Define view states for better state management
enum WorkoutDetailViewState {
    case loading
    case loaded(WorkoutTemplateModel)
    case error(String)
}

class WorkoutDetailViewModel: ObservableObject {
    @Published private(set) var state: WorkoutDetailViewState = .loading
    @Published private(set) var workout: WorkoutTemplateModel?

    // Computed properties for view state checks
    var isLoading: Bool {
        if case .loading = state { return true }
        return false
    }

    var hasError: Bool {
        if case .error(_) = state { return true }
        return false
    }

    var errorMessage: String? {
        if case .error(let message) = state { return message }
        return nil
    }

    private let apiClient: DrMuscleAPIClient
    private let storageService: StorageService
    private let healthKitService: HealthKitServiceProtocol
    private let workoutId: Int

    init(
        workoutId: Int,
        apiClient: DrMuscleAPIClient = DrMuscleAPIClient.shared,
        storageService: StorageService = StorageService.shared,
        healthKitService: HealthKitService = HealthKitService.shared
    ) {
        self.workoutId = workoutId
        self.apiClient = apiClient
        self.storageService = storageService
        self.healthKitService = healthKitService
    }

    @MainActor
    func fetchWorkoutDetails() async {
        state = .loading

        do {
            let workoutDetails = try await apiClient.getUserWorkout(workoutId: workoutId)
            workout = workoutDetails
            state = .loaded(workoutDetails)
        } catch {
            let errorMessage = "Failed to load workout details. Please check your connection."
            state = .error(errorMessage)

            // Use the error handling service to show a banner
            if let apiError = error as? APIError {
                ErrorHandlingService.shared.handleAPIError(apiError)
            } else {
                ErrorHandlingService.shared.handleError(error)
            }
        }
    }

    @MainActor
    func startWorkout() async -> Bool {
        // Starting a workout requires network connectivity to fetch the workout plan
        guard let workout = workout else {
            state = .error("Cannot start workout: workout details not loaded")
            return false
        }

        do {
            // 1. Store the workout data locally
            let workoutId64 = Int64(workoutId)
            let timestamp = Date()

            // Save the workout to local storage
            let savedWorkout = try storageService.saveWorkout(
                id: workoutId64,
                name: workout.name,
                timestamp: timestamp
            )

            // Save each exercise to local storage
            for exercise in workout.exercises {
                let savedExercise = try storageService.saveExercise(
                    id: Int64(exercise.id),
                    name: exercise.name,
                    workoutID: workoutId64
                )
            }

            // 2. Start HealthKit tracking
            let authResult = await healthKitService.requestAuthorization()
            if authResult {
                let startResult = await healthKitService.startWorkout()
                if !startResult {
                    print("Warning: Failed to start HealthKit workout session")
                    // Continue anyway, as this is not critical
                }
            } else {
                print("Warning: HealthKit authorization not granted")
                // Continue anyway, as this is not critical
            }

            return true
        } catch {
            let errorMessage = "Failed to start workout: \(error.localizedDescription)"
            state = .error(errorMessage)

            // Use the error handling service to show a banner
            ErrorHandlingService.shared.showError(message: errorMessage)

            return false
        }
    }
}
