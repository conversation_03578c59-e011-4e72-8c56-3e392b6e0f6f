# ADR-0004: HealthKit Integration V1

*   **Status:** Accepted
*   **Date:** {{CURRENT_DATE}}
*   **Deciders:** Project Lead
*   **Consulted:** AI Assistant
*   **Informed:** Development Team

## Context and Problem Statement

Users expect fitness apps on Apple Watch to integrate with HealthKit to contribute to their activity rings and provide workout tracking (heart rate, calories). We need to define the scope of HealthKit integration for V1.

## Decision Drivers

*   **User Expectation:** Standard feature for watchOS fitness apps.
*   **Platform Integration:** Leverage Apple's health ecosystem.
*   **Value Add:** Provide users with calorie and heart rate data associated with their workouts.
*   **V1 Scope:** Keep the initial implementation focused and achievable.

## Considered Options

*   **Option 1: No HealthKit Integration:** Skip HealthKit for V1.
    *   *Pros:* Simplifies V1 development significantly.
    *   *Cons:* Fails to meet basic user expectations for a watch fitness app, misses key value proposition.
*   **Option 2: Basic Workout Session Tracking:** Request authorization for Heart Rate and Active Energy. Start an `HKWorkoutSession` when the user starts a workout in the app. Save the session with associated HR and calorie data to HealthKit upon workout completion. Do not display live HR/calorie data in the app UI for V1.
    *   *Pros:* Meets core user expectation of logging workouts to HealthKit, provides HR/calorie data post-workout, relatively contained scope.
    *   *Cons:* Requires implementing HealthKit authorization flow and session management.
*   **Option 3: Advanced Integration (Live Data):** Implement Option 2, plus display live Heart Rate and accumulating Active Calories within the watch app UI during the workout.
    *   *Pros:* Provides real-time biofeedback during the workout.
    *   *Cons:* Adds significant UI complexity, requires handling live data streams, increases V1 scope considerably.

## Decision Outcome

**Chosen Option:** Basic Workout Session Tracking (Option 2).

This option strikes the right balance for V1. It delivers the essential HealthKit integration users expect (logging workouts, HR, calories) without adding the complexity of displaying live data in the UI, which can be deferred to a future version. The app will manage an `HKWorkoutSession` in the background during workouts.

### Negative Consequences

*   Users won't see live HR or calorie data within the Dr. Muscle app itself during the workout in V1 (though they can see it in Apple's Workout app or other apps if running concurrently).

## Links

*   `docs/architecture.md`
*   `docs/plan.md`