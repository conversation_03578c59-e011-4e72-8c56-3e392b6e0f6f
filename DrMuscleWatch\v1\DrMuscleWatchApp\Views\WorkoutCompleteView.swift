import SwiftUI

/// View for the workout complete screen
struct WorkoutCompleteView: View {
    /// The view model for the workout complete screen
    @ObservedObject var viewModel: WorkoutCompleteViewModel

    /// Whether to navigate back to the workout list
    @State private var navigateToWorkoutList = false

    /// The error handling service
    @ObservedObject private var errorService = ErrorHandlingService.shared

    var body: some View {
        NavigationView {
            VStack(spacing: 16) {
                // Success icon
                Image(systemName: "checkmark.circle.fill")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 60, height: 60)
                    .foregroundColor(.green)
                    .padding(.bottom, 8)

                // Congratulations message
                Text("Workout Complete!")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(AppTheme.primaryTextColor)
                    .multilineTextAlignment(.center)

                // Workout name
                Text(viewModel.workoutName)
                    .font(.headline)
                    .foregroundColor(AppTheme.secondaryTextColor)
                    .multilineTextAlignment(.center)

                // Motivational message
                Text("Great job! Your progress has been saved.")
                    .font(.caption)
                    .foregroundColor(AppTheme.secondaryTextColor)
                    .multilineTextAlignment(.center)
                    .padding(.top, 8)

                Spacer()

                // Finish button
                Button(action: {
                    Task {
                        let success = await viewModel.finishWorkout()
                        if success {
                            navigateToWorkoutList = true
                        } else {
                            // Error is already shown by the view model via ErrorHandlingService
                            // Wait a moment to let the user see the error before navigating
                            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                                navigateToWorkoutList = true
                            }
                        }
                    }
                }) {
                    if viewModel.isFinishing {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    } else {
                        Text("Finish")
                            .font(.headline)
                            .padding(.horizontal, 24)
                            .padding(.vertical, 12)
                    }
                }
                .buttonStyle(AppTheme.primaryButtonStyle())
                .disabled(viewModel.isFinishing)
                .padding(.bottom, 16)
            }
            .padding()
            .background(AppTheme.backgroundColor)
            .withErrorBanner(errorService: errorService)
            .navigationBarHidden(true)
            .navigationBarBackButtonHidden(true)
            .background(
                NavigationLink(
                    destination: WorkoutListView(viewModel: WorkoutListViewModel()),
                    isActive: $navigateToWorkoutList,
                    label: { EmptyView() }
                )
            )
        }
    }
}

#Preview {
    WorkoutCompleteView(viewModel: WorkoutCompleteViewModel(workoutId: 1, workoutName: "Push Day"))
}
