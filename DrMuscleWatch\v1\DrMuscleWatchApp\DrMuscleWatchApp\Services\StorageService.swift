import Foundation
import CoreData

/// Service for managing local storage operations using Core Data
class StorageService {
    /// Shared instance for the entire application
    static let shared = StorageService()

    /// The persistence controller managing the Core Data stack
    private let persistenceController: PersistenceController

    /// The main view context for UI operations
    var viewContext: NSManagedObjectContext {
        return persistenceController.container.viewContext
    }

    /// Initializer that allows dependency injection for testing
    init(persistenceController: PersistenceController = .shared) {
        self.persistenceController = persistenceController
    }

    // MARK: - Workout Operations

    /// Saves a workout to the local database
    /// - Parameters:
    ///   - id: The workout ID from the API
    ///   - name: The name of the workout
    ///   - timestamp: The timestamp of the workout
    ///   - isCompleted: Whether the workout is completed
    ///   - needsSync: Whether the workout needs to be synced with the server
    /// - Returns: The saved Workout entity
    /// - Throws: An error if the save operation fails
    func saveWorkout(id: Int64, name: String, timestamp: Date, isCompleted: Bool = false, needsSync: Bool = true) throws -> Workout {
        let context = viewContext

        // Check if workout already exists
        let fetchRequest: NSFetchRequest<Workout> = Workout.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "id == %lld", id)

        let existingWorkouts = try context.fetch(fetchRequest)
        let workout: Workout

        if let existingWorkout = existingWorkouts.first {
            // Update existing workout
            workout = existingWorkout
        } else {
            // Create new workout
            workout = Workout(context: context)
            workout.id = id
        }

        // Set or update properties
        workout.name = name
        workout.timestamp = timestamp
        workout.isCompleted = isCompleted
        workout.needsSync = needsSync

        // Save changes
        try persistenceController.save(context)

        return workout
    }

    /// Fetches a workout by its ID
    /// - Parameter id: The workout ID
    /// - Returns: The workout if found, nil otherwise
    /// - Throws: An error if the fetch operation fails
    func getWorkout(id: Int64) throws -> Workout? {
        let fetchRequest: NSFetchRequest<Workout> = Workout.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "id == %lld", id)

        let results = try viewContext.fetch(fetchRequest)
        return results.first
    }

    /// Fetches all workouts
    /// - Parameter predicate: Optional predicate to filter the results
    /// - Returns: An array of workouts
    /// - Throws: An error if the fetch operation fails
    func getAllWorkouts(predicate: NSPredicate? = nil) throws -> [Workout] {
        let fetchRequest: NSFetchRequest<Workout> = Workout.fetchRequest()
        fetchRequest.predicate = predicate

        return try viewContext.fetch(fetchRequest)
    }

    /// Marks a workout as completed
    /// - Parameters:
    ///   - id: The workout ID
    ///   - timestamp: The completion timestamp
    /// - Throws: An error if the operation fails
    func completeWorkout(id: Int64, timestamp: Date) throws {
        guard let workout = try getWorkout(id: id) else {
            throw NSError(domain: "DrMuscleStorage", code: 404, userInfo: [NSLocalizedDescriptionKey: "Workout not found"])
        }

        workout.isCompleted = true
        workout.timestamp = timestamp
        workout.needsSync = true

        try persistenceController.save(viewContext)
    }

    // MARK: - Exercise Operations

    /// Saves an exercise to the local database
    /// - Parameters:
    ///   - id: The exercise ID from the API
    ///   - name: The name of the exercise
    ///   - workoutID: The ID of the workout this exercise belongs to
    ///   - isBodyweight: Whether this is a bodyweight exercise
    /// - Returns: The saved Exercise entity
    /// - Throws: An error if the save operation fails
    func saveExercise(id: Int64, name: String, workoutID: Int64, isBodyweight: Bool = false) throws -> Exercise {
        let context = viewContext

        // Check if exercise already exists
        let fetchRequest: NSFetchRequest<Exercise> = Exercise.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "id == %lld", id)

        let existingExercises = try context.fetch(fetchRequest)
        let exercise: Exercise

        if let existingExercise = existingExercises.first {
            // Update existing exercise
            exercise = existingExercise
        } else {
            // Create new exercise
            exercise = Exercise(context: context)
            exercise.id = id
        }

        // Set or update properties
        exercise.name = name
        exercise.workoutID = workoutID
        exercise.isBodyweight = isBodyweight

        // Link to workout if it exists
        if let workout = try getWorkout(id: workoutID) {
            exercise.workout = workout
        }

        // Save changes
        try persistenceController.save(context)

        return exercise
    }

    /// Fetches an exercise by its ID
    /// - Parameter id: The exercise ID
    /// - Returns: The exercise if found, nil otherwise
    /// - Throws: An error if the fetch operation fails
    func getExercise(id: Int64) throws -> Exercise? {
        let fetchRequest: NSFetchRequest<Exercise> = Exercise.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "id == %lld", id)

        let results = try viewContext.fetch(fetchRequest)
        return results.first
    }

    /// Fetches all exercises for a workout
    /// - Parameter workoutID: The workout ID
    /// - Returns: An array of exercises
    /// - Throws: An error if the fetch operation fails
    func getExercisesForWorkout(workoutID: Int64) throws -> [Exercise] {
        let fetchRequest: NSFetchRequest<Exercise> = Exercise.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "workoutID == %lld", workoutID)

        return try viewContext.fetch(fetchRequest)
    }

    // MARK: - Set Log Operations

    /// Saves a set log to the local database
    /// - Parameters:
    ///   - exerciseID: The exercise ID
    ///   - workoutID: The workout ID
    ///   - reps: The number of repetitions
    ///   - weight: The weight used
    ///   - weightUnit: The unit of the weight (kg or lb)
    ///   - rir: The reps in reserve (optional)
    ///   - timestamp: The timestamp of the set
    ///   - isWarmup: Whether this is a warmup set
    ///   - needsSync: Whether this set needs to be synced with the server
    /// - Returns: The saved SetLog entity
    /// - Throws: An error if the save operation fails
    func saveSetLog(
        exerciseID: Int64,
        workoutID: Int64,
        reps: Int16,
        weight: Double,
        weightUnit: String,
        rir: Int16? = nil,
        timestamp: Date,
        isWarmup: Bool = false,
        needsSync: Bool = true
    ) throws -> SetLog {
        let context = viewContext

        // Create new set log
        let setLog = SetLog(context: context)
        setLog.exerciseID = exerciseID
        setLog.workoutID = workoutID
        setLog.reps = reps
        setLog.weight = weight
        setLog.weightUnit = weightUnit
        if let rir = rir {
            setLog.rir = rir
        }
        setLog.timestamp = timestamp
        setLog.isWarmup = isWarmup
        setLog.needsSync = needsSync

        // Link to exercise and workout if they exist
        if let exercise = try getExercise(id: exerciseID) {
            setLog.exercise = exercise
        }

        if let workout = try getWorkout(id: workoutID) {
            setLog.workout = workout
        }

        // Save changes
        try persistenceController.save(context)

        return setLog
    }

    /// Fetches all set logs for an exercise
    /// - Parameter exerciseID: The exercise ID
    /// - Returns: An array of set logs
    /// - Throws: An error if the fetch operation fails
    func getSetLogsForExercise(exerciseID: Int64) throws -> [SetLog] {
        let fetchRequest: NSFetchRequest<SetLog> = SetLog.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "exerciseID == %lld", exerciseID)
        fetchRequest.sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: true)]

        return try viewContext.fetch(fetchRequest)
    }

    /// Fetches all set logs for a workout
    /// - Parameter workoutID: The workout ID
    /// - Returns: An array of set logs
    /// - Throws: An error if the fetch operation fails
    func getSetLogsForWorkout(workoutID: Int64) throws -> [SetLog] {
        let fetchRequest: NSFetchRequest<SetLog> = SetLog.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "workoutID == %lld", workoutID)
        fetchRequest.sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: true)]

        return try viewContext.fetch(fetchRequest)
    }

    /// Fetches all set logs that need to be synced with the server
    /// - Returns: An array of set logs
    /// - Throws: An error if the fetch operation fails
    func getSetLogsNeedingSync() throws -> [SetLog] {
        let fetchRequest: NSFetchRequest<SetLog> = SetLog.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "needsSync == %@", NSNumber(value: true))

        return try viewContext.fetch(fetchRequest)
    }

    /// Marks a set log as synced
    /// - Parameter setLog: The set log to mark as synced
    /// - Throws: An error if the save operation fails
    func markSetLogAsSynced(_ setLog: SetLog) throws {
        setLog.needsSync = false
        try persistenceController.save(viewContext)
    }

    // MARK: - Cleanup Operations

    /// Deletes all data for a specific workout
    /// - Parameter workoutID: The workout ID
    /// - Throws: An error if the delete operation fails
    func deleteWorkout(workoutID: Int64) throws {
        let context = viewContext

        // Fetch the workout
        let workoutFetchRequest: NSFetchRequest<Workout> = Workout.fetchRequest()
        workoutFetchRequest.predicate = NSPredicate(format: "id == %lld", workoutID)

        if let workout = try context.fetch(workoutFetchRequest).first {
            context.delete(workout)
            try persistenceController.save(context)
        }
    }

    /// Deletes all data older than the specified date
    /// - Parameter date: The cutoff date
    /// - Throws: An error if the delete operation fails
    func deleteDataOlderThan(_ date: Date) throws {
        let context = persistenceController.newBackgroundContext()

        // Delete old workouts
        let workoutFetchRequest: NSFetchRequest<Workout> = Workout.fetchRequest()
        workoutFetchRequest.predicate = NSPredicate(format: "timestamp < %@", date as NSDate)

        try persistenceController.batchDelete(workoutFetchRequest, in: context)

        // Note: Due to cascade delete rules, associated exercises and set logs will also be deleted
    }
}
