﻿using Acr.UserDialogs;
using CommunityToolkit.Maui.Core;
using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Utility;
using RGPopup.Maui.Pages;
using RGPopup.Maui.Services;

namespace DrMaxMuscle.Views;

public partial class RGGeneralPopup : Popup
{
    TapGestureRecognizer okGuesture;
    string buttonText = "";
    public event EventHandler OkButtonPress;
    public bool _isHide { get; set; }
    bool IsDataLoaded = false;
    bool IsChangesToMealPlanLoaded = false;
    bool _isMealPlanLoading = false;
    bool isFirstLayoutClicked = false;
    bool isReviewTips = false;
    string _totalMacros = "";
    string _title = "";
    int count = 0;
    public RGGeneralPopup(string image, string title, string subtitle, string buttonText, Thickness? thickness = null, bool isTips = false, bool isSummary = false, string isShowLearnMore = "false", string isShowSettings = "false", string ismealPlan = "false", string isNotNow = "false", string isAutoHide = "false", string isNewFeature = "false", string isNotYet = "false", bool isChatLoading = false, bool isMealPlanLoading = false, string totalMacros = "", string others = "")
    {
        InitializeComponent();
        var screenWidth = DeviceDisplay.MainDisplayInfo.Width / DeviceDisplay.MainDisplayInfo.Density;
        var screenheight = DeviceDisplay.MainDisplayInfo.Height / DeviceDisplay.MainDisplayInfo.Density;

        this.Size = new Size(screenWidth * 0.9, screenheight * 0.75);
        okGuesture = new TapGestureRecognizer();
        okGuesture.Tapped += OkButton_Clicked;
        OkAction.GestureRecognizers.Add(okGuesture);
        ImgName.Source = image;
        if (thickness != null)
            ImgName.Margin = (Thickness)thickness;
        LblHeading.Text = title;
        LblSubHead.Text = subtitle;
        _title = title;
        OkButton.Text = buttonText;
        this.buttonText = buttonText;
        LblTipText.IsVisible = false;
        //MyParticleCanvas.ParticleColors = AppThemeConstants.CalculateConfettieColors();
        if (isShowLearnMore == "true")
        {
            BtnLearnMore.IsVisible = false;
            LblSubHead.Margin = new Thickness(15, 0, 15, 5);


        }
        else if (isShowSettings == "true")
        {
            BtnLearnMore.IsVisible = true;
            LblSubHead.Margin = new Thickness(15, 0, 15, 5);
            BtnLearnMore.Text = "Open Settings";

        }
        else if (ismealPlan == "true")
        {
            BtnCancel.IsVisible = true;
            //LblSubHead.Margin = new Thickness(15, 0, 15, 5);
            BtnLearnMore.IsVisible = false;
        }
        else if (isMealPlanLoading == true)
        {
            _isMealPlanLoading = isMealPlanLoading;
            isFirstLayoutClicked = false;
            BtnCancel.IsVisible = false;
            BtnLearnMore.IsVisible = false;
            OkButton.IsVisible = true;
            OkButton.Text = buttonText;
            OkAction.IsVisible = true;
            //MyParticleCanvas.IsActive = true;
            //MyParticleCanvas.IsRunning = true;
            isReviewTips = false;
            if (buttonText == "Finalizing...")
            {
                //OkAction.GestureRecognizers.Remove(okGuesture);
                LblCountDown.IsVisible = true;
                _totalMacros = totalMacros;
                try
                {

                    MessagingCenter.Unsubscribe<Message.GeneralMessage>(this, "FinalizeMealPlan");
                    MessagingCenter.Subscribe<Message.GeneralMessage>(this, "FinalizeMealPlan", async (obj) =>
                    {
                        if (obj.GeneralText == "Loaded")
                        {
                            IsDataLoaded = true;
                            //if (PopupNavigation.Instance.PopupStack.Count > 0)
                            //    PopupNavigation.Instance.PopAsync();
                        }

                    });
                    UpdateButtonText();

                }
                catch (Exception ex)
                {

                }
            }
            else if (buttonText == "Review tips")
            {
                _totalMacros = totalMacros;
                isReviewTips = true;
                OkButton.Text = "Continue";
                //if (MyParticleCanvas != null)
                //{
                //    MyParticleCanvas.IsActive = false;
                //    MyParticleCanvas.IsRunning = false;
                //    MyParticleCanvas = null;
                //}
                OkButton_Clicked(OkAction, EventArgs.Empty);
            }
            else if (others == "Change Result")
            {
                MessagingCenter.Unsubscribe<Message.GeneralMessage>(this, "ChangesToMealPlan");
                MessagingCenter.Subscribe<Message.GeneralMessage>(this, "ChangesToMealPlan", async (obj) =>
                {
                    if (obj.GeneralText == "Loaded")
                    {
                        IsChangesToMealPlanLoaded = true;
                      
                    }

                });
                UpdateButtonTextForGPT4();
            }
            else
                SetTimerHidePopupForMeal();
        }
        else if (isNotNow == "true")
        {
            BtnCancel.IsVisible = true;
            BtnLearnMore.IsVisible = false;
            BtnCancel.Text = "Not now";
        }
        else if (isNotYet == "true")
        {
            BtnCancel.IsVisible = true;
            BtnLearnMore.IsVisible = false;
            BtnCancel.Text = "Not yet";
        }
        else if (isNewFeature == "true")
        {
            BtnCancel.IsVisible = false;
            BtnLearnMore.IsVisible = false;
            BtnCancel.Text = "Learn more";
            LblSubHead.HorizontalTextAlignment = TextAlignment.Start;
        }
        else
        {
            BtnLearnMore.IsVisible = false;
        }

        if (isTips)
        {
            LblCountDown.IsVisible = true;
            if (buttonText == "Continue")
            {
                SetLoadingWorkout(title);
                //OkAction?.GestureRecognizers?.Add(okGuesture);
            }
            else
            {
                SetLoading(title);
                SetTimerHidePopup();
            }
            //OkButton.Clicked -= OkButton_Clicked;
            //OkAction.GestureRecognizers.Remove(okGuesture);
            //OkButton.Text = "Customizing workout...";
            OkAction.IsVisible = false;
            LblCountDown.IsVisible = true;
            MessagingCenter.Subscribe<Message.WorkoutLoadedMessage>(this, "WorkoutLoadedMessage", (obj) =>
            {
                Device.BeginInvokeOnMainThread(() =>
                {
                    LblTipText.IsVisible = false;
                    OkAction.IsVisible = true;
                    LblCountDown.IsVisible = true;
                    //OkButton.Text = "Start workout";
                    //OkButton.Clicked += OkButton_Clicked;
                    //OkAction?.GestureRecognizers?.Add(okGuesture);
                });
            });
        }
        if (isChatLoading)
        {
            SetLoadingChat(title);
            SetTimerHidePopup();
            //OkButton.Clicked -= OkButton_Clicked;
            OkAction.GestureRecognizers.Remove(okGuesture);
            //OkButton.Text = "Customizing workout...";
            OkAction.IsVisible = false;
            //MessagingCenter.Subscribe<Message.WorkoutLoadedMessage>(this, "WorkoutLoadedMessage", (obj) =>
            //{
            //    Device.BeginInvokeOnMainThread(() =>
            //    {
            //        LblTipText.IsVisible = false;
            //        OkAction.IsVisible = true;

            //        //OkButton.Text = "Start workout";
            //        //OkButton.Clicked += OkButton_Clicked;
            //        OkAction.GestureRecognizers.Add(okGuesture);
            //    });
            //});
        }
        if (isSummary)
        {

            //OkButton.Clicked -= OkButton_Clicked;
            OkAction?.GestureRecognizers?.Remove(okGuesture);
            SetLoadingSummary(okGuesture);
            //MessagingCenter.Subscribe<Message.WorkoutLoadedMessage>(this, "WorkoutLoadedMessage", (obj) =>
            //{
            //Device.BeginInvokeOnMainThread(() =>
            //{

            //OkButton.Clicked += OkButton_Clicked;

            //});
            //});
        }
        if (isAutoHide == "true")
        {


        }

        this.Closed += Popup_Closed;
    }

    private async Task UpdateButtonText()
    {
        OkButton.Text = "Learn more";
        var closingText = "Closing";
        LblCountDown.Text = $"{closingText} 5:00";
        LblCountDown.IsVisible = true;
        if (Device.RuntimePlatform.Equals(Device.Android))
        {
            await Task.Factory.StartNew(async () =>
            {
                int firstOverlaySeconds = 10; // 5 minutes
                for (int seconds = firstOverlaySeconds; seconds >= 0; seconds--)
                {
                    if (isFirstLayoutClicked)
                        break;
                    int minutes = 0;
                    int remainingSeconds = seconds % 60;

                    MainThread.BeginInvokeOnMainThread(() => {
                        LblCountDown.Text = (minutes > 0) ? $"{closingText} {minutes:0}:{remainingSeconds:00}" : $"{closingText} {(remainingSeconds > 9 ? remainingSeconds.ToString("00") : remainingSeconds.ToString())}";
                        //LblCountDown.Text = (minutes > 0) ? $"{closingText} {minutes:0}:{remainingSeconds:00}" : $"{closingText} {remainingSeconds:00}";
                    });

                    await Task.Delay(1000);
                    if (seconds == 0)
                    {
                        OkButton_Clicked(OkAction, EventArgs.Empty);
                        break;
                    }
                }
                if (isFirstLayoutClicked) // Only proceed to the second loop if not already terminated
                {
                    int totalSeconds = 5 * 60; // 5 minutes
                    for (int seconds = totalSeconds; seconds >= 0; seconds--)
                    {
                        int minutes = seconds / 60;
                        int remainingSeconds = seconds % 60;

                        MainThread.BeginInvokeOnMainThread(() =>
                        {
                            LblCountDown.Text = (minutes > 0) ? $"{closingText} {minutes:0}:{remainingSeconds:00}" : $"{closingText} {(remainingSeconds > 9 ? remainingSeconds.ToString("00") : remainingSeconds.ToString())}";
                            //LblCountDown.Text = (minutes > 0) ? $"{closingText} {minutes:0}:{remainingSeconds:00}" : $"{closingText} {remainingSeconds:00}";
                        });
                        if (IsDataLoaded && PopupNavigation.Instance.PopupStack?.Count > 0)
                        {
                            var topPopup = PopupNavigation.Instance.PopupStack.LastOrDefault();
                            if (topPopup != null && topPopup.GetType() == typeof(GeneralPopup))
                            {
                                await this.CloseAsync();
                                //await PopupNavigation.Instance.PopAsync() ;
                            }
                            break;
                        }

                        await Task.Delay(1000);
                    }
                }
                MainThread.BeginInvokeOnMainThread(async () => {
                    LblCountDown.Text = $" ";
                    LblCountDown.IsVisible = false;
                    while (!IsDataLoaded)
                    {
                        OkButton.Text = "Finalizing.";
                        await Task.Delay(500);
                        OkButton.Text = "Finalizing..";
                        await Task.Delay(500);
                        OkButton.Text = "Finalizing...";
                        await Task.Delay(500);
                    }
                    if (PopupNavigation.Instance.PopupStack?.Count > 0)
                    {
                        var topPopup = PopupNavigation.Instance.PopupStack.LastOrDefault();
                        if (topPopup != null && topPopup.GetType() == typeof(GeneralPopup))
                        {
                            await this.CloseAsync();
                            //await PopupNavigation.Instance.PopAsync() ;
                        }
                    }
                });

            });
        }
        else
        {
            Device.BeginInvokeOnMainThread(async () =>
            {
                int firstOverlaySeconds = 10; // 5 minutes
                for (int seconds = firstOverlaySeconds; seconds >= 0; seconds--)
                {
                    if (isFirstLayoutClicked)
                        break;
                    int minutes = 0;
                    int remainingSeconds = seconds % 60;
                    LblCountDown.Text = (minutes > 0) ? $"{closingText} {minutes:0}:{remainingSeconds:00}" : $"{closingText} {(remainingSeconds > 9 ? remainingSeconds.ToString("00") : remainingSeconds.ToString())}";
                    //LblCountDown.Text = $"{closingText} {minutes:00}:{remainingSeconds:00}";

                    await Task.Delay(1000);
                    if (seconds == 0)
                    {
                        OkButton_Clicked(OkAction, EventArgs.Empty);
                        break;
                    }
                }
                if (isFirstLayoutClicked) // Only proceed to the second loop if not already terminated
                {
                    int totalSeconds = 5 * 60; // 5 minutes
                    for (int seconds = totalSeconds; seconds >= 0; seconds--)
                    {
                        int minutes = seconds / 60;
                        int remainingSeconds = seconds % 60;
                        LblCountDown.Text = (minutes > 0) ? $"{closingText} {minutes:0}:{remainingSeconds:00}" : $"{closingText} {(remainingSeconds > 9 ? remainingSeconds.ToString("00") : remainingSeconds.ToString())}";
                        //LblCountDown.Text = $"{closingText} {minutes:00}:{remainingSeconds:00}";
                        if (IsDataLoaded && PopupNavigation.Instance.PopupStack.Count > 0)
                        {
                            var topPopup = PopupNavigation.Instance.PopupStack.LastOrDefault();
                            if (topPopup != null && topPopup.GetType() == typeof(GeneralPopup))
                            {
                                await this.CloseAsync();
                                //await PopupNavigation.Instance.PopAsync() ;
                            }
                        }

                        await Task.Delay(1000);
                    }
                }
                LblCountDown.Text = $" ";
                LblCountDown.IsVisible = false;
                while (!IsDataLoaded)
                {

                    OkButton.Text = "Finalizing.";
                    await Task.Delay(500);
                    OkButton.Text = "Finalizing..";
                    await Task.Delay(500);
                    OkButton.Text = "Finalizing...";
                    await Task.Delay(500);
                }

                if (PopupNavigation.Instance.PopupStack?.Count > 0)
                {
                    var topPopup = PopupNavigation.Instance.PopupStack.LastOrDefault();
                    if (topPopup != null && topPopup.GetType() == typeof(GeneralPopup))
                    {
                        await this.CloseAsync();
                        //await PopupNavigation.Instance.PopAsync() ;
                    }
                }
            });
        }
        //while (!IsDataLoaded)
        //{
        //    if (Device.RuntimePlatform.Equals(Device.Android))
        //    {
        //        OkButton.Text = "Finalizing.";
        //        await Task.Delay(500);
        //        OkButton.Text = "Finalizing..";
        //        await Task.Delay(500);
        //        OkButton.Text = "Finalizing...";
        //        await Task.Delay(500);
        //    }
        //    else
        //    {
        //        OkButton.Text = "Finalizing.";
        //        await Task.Delay(500);
        //        OkButton.Text = "Finalizing..";
        //        await Task.Delay(500);
        //        OkButton.Text = "Finalizing...";
        //        await Task.Delay(500);
        //    }
        //}
        //OkAction.GestureRecognizers.Add(okGuesture);
    }
    private async Task UpdateButtonTextForGPT4()
    {
        OkButton.Text = "Continue";
        var closingText = "Closing";
        LblCountDown.Text = $"{closingText} 30";
        LblCountDown.IsVisible = true;
        if (Device.RuntimePlatform.Equals(Device.Android))
        {
            await Task.Factory.StartNew(async () =>
            {
                int totalSeconds = 30; // 5 minutes
                for (int seconds = totalSeconds; seconds >= 0; seconds--)
                {
                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        LblCountDown.Text = $"{closingText} {seconds}";
                    });
                    if (IsChangesToMealPlanLoaded && PopupNavigation.Instance.PopupStack?.Count > 0)
                    {
                        var topPopup = PopupNavigation.Instance.PopupStack.LastOrDefault();
                        if (topPopup != null && topPopup.GetType() == typeof(GeneralPopup))
                        {
                            await this.CloseAsync();
                            //await PopupNavigation.Instance.PopAsync() ;
                        }
                        break;
                    }

                    await Task.Delay(1000);
                }
                MainThread.BeginInvokeOnMainThread(async () => {
                    LblCountDown.Text = $" ";
                    LblCountDown.IsVisible = false;
                    while (!IsChangesToMealPlanLoaded)
                    {
                        OkButton.Text = "Finalizing.";
                        await Task.Delay(500);
                        OkButton.Text = "Finalizing..";
                        await Task.Delay(500);
                        OkButton.Text = "Finalizing...";
                        await Task.Delay(500);
                    }
                    if (PopupNavigation.Instance.PopupStack?.Count > 0)
                    {
                        var topPopup = PopupNavigation.Instance.PopupStack.LastOrDefault();
                        if (topPopup != null && topPopup.GetType() == typeof(GeneralPopup))
                        {
                            await this.CloseAsync();
                            //await PopupNavigation.Instance.PopAsync() ;
                        }
                    }
                });

            });
        }
        else
        {
            Device.BeginInvokeOnMainThread(async () =>
            {
                int totalSeconds = 30; // 5 minutes
                for (int seconds = totalSeconds; seconds >= 0; seconds--)
                {
                    LblCountDown.Text = $"{closingText} {seconds}";
                    if (IsChangesToMealPlanLoaded && PopupNavigation.Instance.PopupStack?.Count > 0)
                    {
                        var topPopup = PopupNavigation.Instance.PopupStack.LastOrDefault();
                        if (topPopup != null && topPopup.GetType() == typeof(GeneralPopup))
                        {
                            await this.CloseAsync();
                            //PopupNavigation.Instance.PopAsync() ;
                        }
                    }

                    await Task.Delay(1000);
                }
                LblCountDown.Text = $" ";
                LblCountDown.IsVisible = false;
                while (!IsChangesToMealPlanLoaded)
                {

                    OkButton.Text = "Finalizing.";
                    await Task.Delay(500);
                    OkButton.Text = "Finalizing..";
                    await Task.Delay(500);
                    OkButton.Text = "Finalizing...";
                    await Task.Delay(500);
                }

                if (PopupNavigation.Instance.PopupStack?.Count > 0)
                {
                    var topPopup = PopupNavigation.Instance.PopupStack.LastOrDefault();
                    if (topPopup != null && topPopup.GetType() == typeof(GeneralPopup))
                    {
                        await this.CloseAsync();
                        //await PopupNavigation.Instance.PopAsync() ;
                    }
                }
            });
        }
    }
    private void Popup_Closed(object sender, PopupClosedEventArgs e)
    {
        //base.OnDisappearing();
        _isHide = true;
        MessagingCenter.Unsubscribe<Message.WorkoutLoadedMessage>(this, "WorkoutLoadedMessage");

        //try
        //{
        //    if (MyParticleCanvas != null)
        //    {
        //        MyParticleCanvas.IsActive = false;
        //        MyParticleCanvas.IsRunning = false;
        //        MyParticleCanvas = null;
        //    }

        //}
        //catch (Exception ex)
        //{

        //}
    }
    private async Task SetLoadingSummary(TapGestureRecognizer okGuesture)
    {
        await Task.Delay(250);

        OkButton.Text = "Loading.";

        await Task.Delay(700);
        OkButton.Text = "Loading..";

        await Task.Delay(700);

        OkButton.Text = "Loading...";
        await Task.Delay(700);
        OkButton.Text = this.buttonText;
        OkAction.GestureRecognizers.Add(okGuesture);

    }

    private async void SetLoadingChat(string title)
    {
        //LblHeading.FontAttributes = LblSubHead.FontAttributes;
        //LblHeading.FontSize = LblSubHead.FontSize;
        //LblHeading.TextColor = LblSubHead.TextColor;

        if (Device.RuntimePlatform.Equals(Device.Android))
        {
            LblTipText.Text = "";
            LblTipText.IsVisible = true;
            OkAction.IsVisible = false;

            await Task.Factory.StartNew(async () =>
            {

                MainThread.BeginInvokeOnMainThread(() => {
                    LblTipText.Text = "Loading chat...";
                });


                await Task.Delay(800);
                if (LblTipText.Text == " ")
                    return;
                MainThread.BeginInvokeOnMainThread(() => {

                    LblTipText.Text = "Loading messages...";
                });
                await Task.Delay(750);
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    LblTipText.IsVisible = false;
                    OkAction.IsVisible = true;

                    //OkButton.Text = "Start workout";
                    //OkButton.Clicked += OkButton_Clicked;
                    OkAction.GestureRecognizers.Add(okGuesture);
                });

            });
        }
        else
        {
            LblTipText.IsVisible = true;

            //ImgLoader.IsVisible = true;
            Device.BeginInvokeOnMainThread(async () =>
            {

                LblTipText.Text = "Loading chat...";
                await Task.Delay(800);
                if (LblTipText.Text == " ")
                    return;
                LblTipText.Text = "Loading messages...";
                await Task.Delay(750);
                LblTipText.IsVisible = false;
                OkAction.IsVisible = true;

                //OkButton.Text = "Start workout";
                //OkButton.Clicked += OkButton_Clicked;
                OkAction.GestureRecognizers.Add(okGuesture);
            });
        }

    }

    private async void SetLoadingWorkout(string title)
    {
        //LblHeading.FontAttributes = LblSubHead.FontAttributes;
        //LblHeading.FontSize = LblSubHead.FontSize;
        //LblHeading.TextColor = LblSubHead.TextColor;
        try
        {

            if (Device.RuntimePlatform.Equals(Device.Android))
            {
                LblTipText.Text = "";
                LblTipText.IsVisible = true;
                await Task.Factory.StartNew(async () =>
                {

                    MainThread.BeginInvokeOnMainThread(() => {
                        LblTipText.Text = "Loading equipment...";
                    });


                    await Task.Delay(1500);
                    if (LblTipText.Text == " ")
                        return;
                    MainThread.BeginInvokeOnMainThread(() => {

                        LblTipText.Text = "Loading profile...";
                    });
                    await Task.Delay(1400);
                    if (LblTipText.Text == " ")
                        return;
                    MainThread.BeginInvokeOnMainThread(() => {

                        LblTipText.Text = "Loading program...";
                    });
                    await Task.Delay(1300);
                    if (LblTipText.Text == " ")
                        return;
                    MainThread.BeginInvokeOnMainThread(() => {
                        LblTipText.Text = "Loading calendar...";
                    });
                    await Task.Delay(1200);
                    if (LblTipText.Text == " ")
                        return;
                    MainThread.BeginInvokeOnMainThread(() => {
                        LblTipText.Text = "Loading custom tips";
                    });
                    await Task.Delay(1100);
                    if (LblTipText.Text == " ")
                        return;
                    MainThread.BeginInvokeOnMainThread(() => {
                        LblTipText.Text = "Loading progression...";
                    });
                    await Task.Delay(1000);
                    if (LblTipText.Text == " ")
                        return;
                    MainThread.BeginInvokeOnMainThread(() => {
                        LblTipText.Text = "Loading progression...";
                    });

                    await Task.Delay(1000);
                    if (LblTipText.Text == " ")
                        return;
                    MainThread.BeginInvokeOnMainThread(() => {
                        LblTipText.Text = "Loading new records...";
                    });

                    await Task.Delay(900);
                    if (LblTipText.Text == " ")
                        return;
                    MainThread.BeginInvokeOnMainThread(() => {
                        LblTipText.Text = "Loading a big pump...";
                    });
                    await Task.Delay(800);
                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        LblTipText.IsVisible = false;
                        OkAction.IsVisible = true;


                        //OkAction?.GestureRecognizers?.Add(okGuesture);
                        SetTimerForContinue();
                    });
                });
            }
            else
            {
                LblTipText.IsVisible = true;

                //ImgLoader.IsVisible = true;
                Device.BeginInvokeOnMainThread(async () =>
                {

                    LblTipText.Text = "Loading equipment...";


                    await Task.Delay(1500);
                    if (LblTipText.Text == " ")
                        return;

                    LblTipText.Text = "Loading profile...";
                    await Task.Delay(1400);
                    if (LblTipText.Text == " ")
                        return;

                    LblTipText.Text = "Loading program...";
                    await Task.Delay(1300);
                    if (LblTipText.Text == " ")
                        return;
                    LblTipText.Text = "Loading calendar...";
                    await Task.Delay(1200);
                    if (LblTipText.Text == " ")
                        return;
                    LblTipText.Text = "Loading custom tips";
                    await Task.Delay(1100);
                    if (LblTipText.Text == " ")
                        return;
                    LblTipText.Text = "Loading progression...";
                    await Task.Delay(1000);
                    if (LblTipText.Text == " ")
                        return;
                    LblTipText.Text = "Loading progression...";


                    await Task.Delay(1000);
                    if (LblTipText.Text == " ")
                        return;

                    LblTipText.Text = "Loading new records...";


                    await Task.Delay(900);
                    if (LblTipText.Text == " ")
                        return;

                    LblTipText.Text = "Loading a big pump...";

                    await Task.Delay(800);
                    LblTipText.IsVisible = false;
                    OkAction.IsVisible = true;



                    SetTimerForContinue();
                });
            }

        }
        catch (Exception ex)
        {

        }
    }

    private async void SetLoading(string title)
    {
        //LblHeading.FontAttributes = LblSubHead.FontAttributes;
        //LblHeading.FontSize = LblSubHead.FontSize;
        //LblHeading.TextColor = LblSubHead.TextColor;

        if (Device.RuntimePlatform.Equals(Device.Android))
        {
            LblTipText.Text = "";
            LblTipText.IsVisible = true;
            LblCountDown.IsVisible = true;
            await Task.Factory.StartNew(async () =>
            {

                MainThread.BeginInvokeOnMainThread(() => {
                    LblTipText.Text = "Loading sets...";
                });


                await Task.Delay(500);
                if (LblTipText.Text == " ")
                    return;
                MainThread.BeginInvokeOnMainThread(() => {

                    LblTipText.Text = "Loading reps...";
                });
                await Task.Delay(750);
                if (LblTipText.Text == " ")
                    return;
                MainThread.BeginInvokeOnMainThread(() => {

                    LblTipText.Text = "Loading weights...";
                });
                await Task.Delay(500);
                if (LblTipText.Text == " ")
                    return;
                MainThread.BeginInvokeOnMainThread(() => {
                    LblTipText.Text = "Loading a big pump...";
                });
                await Task.Delay(500);
                if (LblTipText.Text == " ")
                    return;
                MainThread.BeginInvokeOnMainThread(() => {
                    LblTipText.Text = "Let's go!";
                });
            });
        }
        else
        {
            LblTipText.IsVisible = true;
            LblCountDown.IsVisible = true;
            //ImgLoader.IsVisible = true;
            Device.BeginInvokeOnMainThread(async () =>
            {

                LblTipText.Text = "Loading sets...";
                await Task.Delay(700);
                if (LblTipText.Text == " ")
                    return;
                LblTipText.Text = "Loading reps...";
                await Task.Delay(750);
                if (LblTipText.Text == " ")
                    return;
                LblTipText.Text = "Loading weights...";
                await Task.Delay(800);
                if (LblTipText.Text == " ")
                    return;
                if (LblTipText.Text == " ")
                    return;
                LblTipText.Text = "Loading a big pump...";
                await Task.Delay(800);
                if (LblTipText.Text == " ")
                    return;
                LblTipText.Text = "Let's go!";

            });
        }
    }

    private async void SetTimerHidePopup()
    {
        //LblHeading.FontAttributes = LblSubHead.FontAttributes;
        //LblHeading.FontSize = LblSubHead.FontSize;
        //LblHeading.TextColor = LblSubHead.TextColor;
        await Task.Delay(1000);
        OkButton.Text = $"{this.buttonText}";
        var closingText = "Closing";
        LblCountDown.Text = $"{closingText} 5";
        LblCountDown.IsVisible = true;
        if (Device.RuntimePlatform.Equals(Device.Android))
        {
            LblTipText.Text = "";
            LblTipText.IsVisible = false;
            OkButton.IsVisible = true;
            OkAction.IsVisible = true;
            await Task.Factory.StartNew(async () =>
            {
                await Task.Delay(1000);
                MainThread.BeginInvokeOnMainThread(() => {
                    LblCountDown.Text = $"{closingText} 4";
                });


                await Task.Delay(1000);
                MainThread.BeginInvokeOnMainThread(() => {

                    LblCountDown.Text = $"{closingText} 3";
                });

                await Task.Delay(1000);
                MainThread.BeginInvokeOnMainThread(() => {

                    LblCountDown.Text = $"{closingText} 2";
                });
                await Task.Delay(1000);
                MainThread.BeginInvokeOnMainThread(() => {

                    LblCountDown.Text = $"{closingText} 1";
                });
                await Task.Delay(1000);
                MainThread.BeginInvokeOnMainThread(() => {
                    LblCountDown.Text = $" ";
                });
                if (PopupNavigation.Instance.PopupStack?.Count > 0 && !_isHide)
                {
                    await this.CloseAsync();
                    //await PopupNavigation.Instance.PopAsync();
                }
            });
        }
        else
        {
            LblTipText.IsVisible = true;

            //ImgLoader.IsVisible = true;
            Device.BeginInvokeOnMainThread(async () =>
            {

                LblCountDown.Text = $"{closingText} 5";
                await Task.Delay(1000);
                LblCountDown.Text = $"{closingText} 4";
                await Task.Delay(1000);
                LblCountDown.Text = $"{closingText} 3";
                await Task.Delay(1000);
                LblCountDown.Text = $"{closingText} 2";
                await Task.Delay(1000);
                LblCountDown.Text = $"{closingText} 1";
                await Task.Delay(1000);
                LblCountDown.Text = $" ";
                if (PopupNavigation.Instance.PopupStack?.Count > 0 && !_isHide)
                {
                    await this.CloseAsync();
                    //await PopupNavigation.Instance.PopAsync(); 
                }

            });
        }
    }
    private async void SetTimerHidePopupForMeal()
    {
        await Task.Delay(1300);
        var closingText = "Closing";
        LblCountDown.IsVisible = true;
        int totalSeconds = 20;
        LblCountDown.Text = $"{closingText} {totalSeconds}";

        if (Device.RuntimePlatform.Equals(Device.Android))
        {
            await Task.Factory.StartNew(async () =>
            {
                for (int seconds = totalSeconds; seconds >= 0; seconds--)
                {
                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        LblCountDown.Text = $"{closingText} {seconds}";
                    });
                    if (App.IsMealReponseLoaded)
                    {
                        if (PopupNavigation.Instance.PopupStack?.Count > 0 && PopupNavigation.Instance.PopupStack.LastOrDefault() is GeneralPopup)
                            await this.CloseAsync();
                        break;
                    }
                    await Task.Delay(1000);
                    if (seconds == 0 && PopupNavigation.Instance.PopupStack?.Count > 0 && PopupNavigation.Instance.PopupStack.LastOrDefault() is GeneralPopup)
                    {
                        await this.CloseAsync();
                        //await PopupNavigation.Instance.PopAsync() ;
                    }
                }

            });
        }
        else
        {
            Device.BeginInvokeOnMainThread(async () =>
            {
                for (int seconds = totalSeconds; seconds >= 0; seconds--)
                {
                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        LblCountDown.Text = $"{closingText} {seconds}";
                    });
                    if (App.IsMealReponseLoaded)
                    {
                        if (PopupNavigation.Instance.PopupStack?.Count > 0 && PopupNavigation.Instance.PopupStack.LastOrDefault() is GeneralPopup)
                            await this.CloseAsync();
                        break;
                    }
                    await Task.Delay(1000);
                    if (seconds == 0 && PopupNavigation.Instance.PopupStack?.Count > 0 && PopupNavigation.Instance.PopupStack.LastOrDefault() is GeneralPopup)
                    {
                        await this.CloseAsync();
                        //await PopupNavigation.Instance.PopAsync() ;
                    }
                }

            });
        }
    }
    public async void CheckMealLoaded()
    {
        try
        {
            if (App.IsMealReponseLoaded && PopupNavigation.Instance.PopupStack?.Count > 0)
                await this.CloseAsync();
        }
        catch (Exception ex)
        {

        }
    }
    private async void SetTimerForContinue()
    {

        try
        {

            OkButton.Text = $"{this.buttonText} 5";

            if (Device.RuntimePlatform.Equals(Device.Android))
            {
                LblTipText.Text = "";
                LblTipText.IsVisible = false;
                OkButton.IsVisible = true;
                OkAction.IsVisible = true;
                await Task.Factory.StartNew(async () =>
                {
                    await Task.Delay(1000);
                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        OkButton.Text = $"{this.buttonText} 4";
                    });


                    await Task.Delay(1000);
                    MainThread.BeginInvokeOnMainThread(() =>
                    {

                        OkButton.Text = $"{this.buttonText} 3";
                    });

                    await Task.Delay(1000);
                    MainThread.BeginInvokeOnMainThread(() =>
                    {

                        OkButton.Text = $"{this.buttonText} 2";
                    });
                    await Task.Delay(1000);
                    MainThread.BeginInvokeOnMainThread(() =>
                    {

                        OkButton.Text = $"{this.buttonText} 1";
                    });
                    await Task.Delay(1000);
                    MainThread.BeginInvokeOnMainThread(() =>
                    {

                        OkButton.Text = $"{this.buttonText}";
                    });
                });
            }
            else
            {
                LblTipText.IsVisible = false;

                //ImgLoader.IsVisible = true;
                Device.BeginInvokeOnMainThread(async () =>
                {

                    OkButton.Text = $"{this.buttonText} 5";
                    await Task.Delay(1000);
                    OkButton.Text = $"{this.buttonText} 4";
                    await Task.Delay(1000);
                    OkButton.Text = $"{this.buttonText} 3";
                    await Task.Delay(1000);
                    OkButton.Text = $"{this.buttonText} 2";
                    await Task.Delay(1000);
                    OkButton.Text = $"{this.buttonText} 1";
                    await Task.Delay(1000);
                    OkButton.Text = $"{this.buttonText}";

                });


            }
        }
        catch (Exception ex)
        {

        }

    }

    void OkButton_Clicked(System.Object sender, System.EventArgs e)
    {
        if (_isMealPlanLoading && !string.IsNullOrEmpty(_totalMacros))
        {
            //if (MyParticleCanvas != null)
            //{
            //    MyParticleCanvas.IsActive = false;
            //    MyParticleCanvas.IsRunning = false;
            //    MyParticleCanvas = null;
            //}
            LocalDBManager.Instance.SetDBSetting("totalMacros", _totalMacros);
            var splitedData = _totalMacros.Split(',');
            if (!App.IsProteinShakeExist && count == 7)
            {
                count++;
            }
            switch (count)
            {
                case 0:
                    SetLabelText("Free drinks", "", "You are free to drink coffee and tea, as long as they contain zero calories. Healthy in moderation, and may even help with fat loss.");
                    isFirstLayoutClicked = true;
                    break;
                case 1:
                    SetLabelText("2 cups water", "", "Essential for peak performance. Keeps your body running smoothly, energy high, and hunger low. Make drinking two cups with each meal non-negotiable.");
                    break;
                case 2:
                    SetLabelText(splitedData[0], "g protein", "Build muscle faster. Repair damage after you lift. Fuel the growth of stronger, larger muscles. Stave off hunger, and sculpt your physique.");
                    break;
                case 3:
                    SetLabelText("Protein timing", "", "Eat within 30 min of working out to jump-start muscle repair. Spread your intake across the day to fuel muscle growth.");
                    break;
                case 4:
                    SetLabelText(splitedData[1], "g carbs", "Your ally for powering through intense workouts. Carbs fuel your muscles and brain and help you push harder to achieve that lean, formidable frame.");
                    break;
                case 5:
                    SetLabelText(splitedData[2], "g fat", "Your secret weapon for hormonal balance and sustained energy. Helps produce testosterone, absorb vital nutrients, and support your metabolism.");
                    break;
                case 6:
                    SetLabelText("Meal timing", "", "Time your meals to optimize absorption and energy. Eat before and after your workouts and consider a protein-rich snack or shake just before bed for maximum benefit.");
                    break;
                case 7:
                    SetLabelText("Protein shake", "", "Drink before or after your workouts to maximize fat loss and muscle gain. On rest days, have it before going to bed, or whenever convenient.");
                    break;
                case 8:
                    SetLabelText("Snack smarter", "", "Easy, portable snacks keep your energy up and cravings down. Don't let long gaps between meals derail your progress. Maintain muscle, manage hunger.");
                    break;
                case 9:
                    SetLabelText("Stay flexible", "", "If you miss a meal or snack, don't stress. Instead, eat it later. Focus on maintaining your overall daily totals rather than perfect meal timing.");
                    break;
                case 10:
                    SetLabelText("Eating out", "", "Choose wisely. Opt for dishes with lean proteins and vegetables. Request sauces on the side to control calories. Drink plenty of water.");
                    break;
                case 11:
                    SetLabelText("Alcohol", "", "Impacts recovery and fat loss. One drink is okay, but too much can undo your efforts. Choose lighter options and keep your goals in sight.");
                    break;
                case 12:
                    SetLabelText("Supplementation", "", "Supplements can fill nutritional gaps, but they're not a silver bullet. Focus on whole foods first, supplements second. Creatine is a popular option.");
                    break;
                case 13:
                    SetLabelText("Creatine: proven power", "", "One of the most researched supplements for over 30 years. Boosts strength, muscle growth, and recovery. Try 3-5 g a day for best results. Safe, effective, and time-tested.");
                    break;
                case 14:
                    SetLabelText("Vitamin D", "", "Essential for muscle, bone, and hormonal health. Boosts performance and recovery. Ensure adequate intake through sunlight, food, or supplements.");
                    break;
                case 15:
                    SetLabelText("Prep smart", "", "Meal prep can save you time and keep you on track. Prepare meals in bulk for easy, grab-and-go eating. Set yourself up for success, make Sunday your meal prep day.");
                    break;
                case 16:
                    SetLabelText("Feedback loop", "", "Regularly assess how your meal plan is affecting your energy, performance, and body composition. Log your weight often. Update your plan when progress stalls.");
                    break;
                default:
                    OkButton.Text = isReviewTips ? "Continue" : "Finalizing...";
                    LblHeading.Text = isReviewTips ? "" : _title;
                    LblSubHead.Text = isReviewTips ? "" : "Your plan is almost ready.";
                    //var finalMealPlan = LocalDBManager.Instance.GetDBSetting("FinalMealPlan")?.Value;
                    if (IsDataLoaded || isReviewTips)
                    {
                        this.Close();
                        //if (PopupNavigation.Instance.PopupStack.Count() > 0)
                        //    PopupNavigation.Instance.PopAllAsync();
                    }
                    break;
            }
            count++;
        }
        else
        {
            try
            {
                this.Close();
                //MauiProgram.SafeDismissAllPopups();
                //if (PopupNavigation.Instance.PopupStack.Count() > 0)
                //    PopupNavigation.Instance.PopAllAsync();
            }
            catch (Exception ex)
            {

            }

            if (BtnCancel.IsVisible)
            {
                if (OkButtonPress != null)
                    OkButtonPress.Invoke(sender, EventArgs.Empty);
            }
        }


    }
    void SetLabelText(string heading, string unit, string subHead)
    {
        try
        {
            Device.BeginInvokeOnMainThread(() =>
            {
                OkButton.Text = "Continue";
                LblHeading.Text = heading + " " + unit;
                LblSubHead.Text = subHead;
            });
        }
        catch (Exception ex)
        {

        }
    }
    async void DrMuscleButton_Clicked(System.Object sender, System.EventArgs e)
    {
        if (LblHeading.Text.Equals("New features!"))
        {
            Browser.OpenAsync(new Uri("https://dr-muscle.com/timeline"));
        }
        else if (BtnLearnMore.Text.Equals("Open Settings"))
        {
            await this.CloseAsync();
            //await MauiProgram.SafeDismissTopPopup();
            //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
            //    PopupNavigation.Instance.PopAsync() ;
            //PagesFactory.PushAsync<SettingsPage>();
        }
        else if (BtnLearnMore.Text.Equals("Cancel"))
        {
            await this.CloseAsync();
            //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
            //    PopupNavigation.Instance.PopAsync() ;
            // PagesFactory.PopAsync();
        }
        else
        {
            if (await CheckTrialUserAsync())
                return;
            await this.CloseAsync();
            //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
            //    PopupNavigation.Instance.PopAsync() ;
            //PagesFactory.PushAsync<LearnPage>();
        }
    }

    private async Task<bool> CheckTrialUserAsync()
    {
        if (App.IsFreePlan)
        {
            var ShowWelcomePopUp2 = await HelperClass.DisplayCustomPopup("You discovered a premium feature!","Upgrading will unlock custom coaching tips based on your goals and progression.",
            "Upgrade","Maybe later");
            ShowWelcomePopUp2.ActionSelected += (sender,action) => {
                if(action == PopupAction.OK){
                    //PagesFactory.PushAsync<SubscriptionPage>();
                }
            };
            // ConfirmConfig ShowWelcomePopUp2 = new ConfirmConfig()
            // {
            //     Message = "Upgrading will unlock custom coaching tips based on your goals and progression.",
            //     Title = "You discovered a premium feature!",
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     OkText = "Upgrade",
            //     CancelText = "Maybe later",
            //     OnAction = async (bool ok) =>
            //     {
            //         if (ok)
            //         {
            //             //PagesFactory.PushAsync<SubscriptionPage>();
            //         }
            //         else
            //         {

            //         }
            //     }
            // };
            // UserDialogs.Instance.Confirm(ShowWelcomePopUp2);
        }
        return App.IsFreePlan;
    }

    async void DrMuscleButtonCancel_Clicked(System.Object sender, System.EventArgs e)
    {
        if (BtnCancel.Text == "Learn more")
        {
            Browser.OpenAsync(new Uri("https://dr-muscle.com/timeline"));
        }
        else
        {
            await this.CloseAsync();
            //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
            //    PopupNavigation.Instance.PopAsync() ;
        }
    }
}
