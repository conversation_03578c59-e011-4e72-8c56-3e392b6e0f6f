﻿using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Views;
using Android.Widget;
using DrMuscle.Dependencies;
using DrMuscle.Droid.Renderer;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Xamarin.Forms;

[assembly: Dependency(typeof(WindowBackgroundColorImplementation))]
namespace DrMuscle.Droid.Renderer
{
    public class WindowBackgroundColorImplementation : IWindowBackgroundColor
    {
        public void SetBackgroundColor()
        {
            // Access the current Android Application
            var context = Xamarin.Essentials.Platform.CurrentActivity as MainActivity;

            // Set the window background color to white
            if (context != null)
            {
                context.Window.SetBackgroundDrawableResource(Android.Resource.Color.Black);
            }
        }
    }
}