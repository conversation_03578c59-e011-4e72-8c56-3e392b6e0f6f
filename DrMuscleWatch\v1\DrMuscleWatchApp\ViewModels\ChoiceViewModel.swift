class ChoiceViewModel: ObservableObject {
    @Published var isLastExercise: Bool = false
    @Published var showingError: Bool = false
    @Published var errorMessage: String = ""
    
    private let workoutService: WorkoutServiceProtocol
    private let coordinator: WorkoutCoordinator
    private let exerciseId: Int64
    private let workoutId: Int64
    
    init(workoutService: WorkoutService<PERSON>rotoc<PERSON>, coordinator: WorkoutCoordinator, exerciseId: Int64, workoutId: Int64) {
        self.workoutService = workoutService
        self.coordinator = coordinator
        self.exerciseId = exerciseId
        self.workoutId = workoutId
        
        Task {
            await checkIfLastExercise()
        }
    }
    
    @MainActor
    private func checkIfLastExercise() async {
        do {
            isLastExercise = try await workoutService.isLastExercise()
        } catch {
            errorMessage = "Failed to check exercise status: \(error.localizedDescription)"
            showingError = true
        }
    }
    
    func addSetSelected() {
        coordinator.navigateBackToSetScreen()
    }
    
    @MainActor
    func nextExerciseSelected() async {
        do {
            // Calculate performance percentage
            let performancePercentage = await calculatePerformancePercentage()
            
            // Mark current exercise as complete
            try await workoutService.completeCurrentExercise()
            
            // Show checkmark animation with performance percentage
            // This will play haptic feedback and show the animation
            coordinator.showCheckmarkAnimation(performancePercentage: performancePercentage) {
                // This closure is called when animation completes
                Task {
                    await self.proceedToNextExercise()
                }
            }
        } catch {
            HapticService.shared.playError()
            errorMessage = "Failed to complete exercise: \(error.localizedDescription)"
            showingError = true
        }
    }
    
    @MainActor
    private func proceedToNextExercise() async {
        do {
            // Move to next exercise
            try await workoutService.moveToNextExercise()
            
            // Navigate to next exercise
            coordinator.navigateToNextExercise()
        } catch {
            HapticService.shared.playError()
            errorMessage = "Failed to move to next exercise: \(error.localizedDescription)"
            showingError = true
        }
    }
    
    @MainActor
    func finishWorkoutSelected() async {
        do {
            // Calculate overall workout performance
            let overallPerformance = await calculateOverallWorkoutPerformance()
            
            // Mark workout as complete
            try await workoutService.completeWorkout()
            
            // Show checkmark animation with overall performance
            coordinator.showCheckmarkAnimation(performancePercentage: overallPerformance) {
                // This closure is called when animation completes
                self.coordinator.navigateToWorkoutComplete()
            }
        } catch {
            HapticService.shared.playError()
            errorMessage = "Failed to complete workout: \(error.localizedDescription)"
            showingError = true
        }
    }
    
    private func calculatePerformancePercentage() async -> Double {
        // Calculate performance percentage based on current exercise data
        // This would compare the current performance to previous performance
        do {
            return try await workoutService.calculateExercisePerformance(exerciseId: exerciseId)
        } catch {
            print("Error calculating performance: \(error)")
            return 0.0
        }
    }
    
    private func calculateOverallWorkoutPerformance() async -> Double {
        // Calculate overall workout performance
        do {
            return try await workoutService.calculateWorkoutPerformance(workoutId: workoutId)
        } catch {
            print("Error calculating workout performance: \(error)")
            return 0.0
        }
    }
}