import SwiftUI
import CoreData

struct ContentView: View {
    
    // MARK: - State Variables
    	
    // Track user input and current state
    @State private var repsValue: Int = 10
        @State private var weightValue: Int = 50
        @State private var unit: String = "lbs" // Weight unit (lbs or kg)
        @State private var isRepsPickerPresented: Bool = false
        @State private var isWeightPickerPresented: Bool = false
        @State private var exerciseName: String = "Reverse Lunges Quads"
        @State private var setCount: Int = 1 // Track the number of sets (max of 2)
        @State private var models = WorkoutLogSerieModelRef() // Model reference for calculations
        @State private var percentage: Double = 0.0 // Percentage for progression
        @State private var showAlert: Bool = false // Alert trigger state
        @State private var currentExerciseIndex: Int = 0 // Current exercise index
        @State private var count: Int = 0 // Tracks the number of sets saved
        
        // List of exercises
        let exerciseList = [
            "Reverse Lunges Quads",
            "Squats"
        ]
        
        private var dbManager = DatabaseManager() // Instance of the Database Manager to handle DB operations


    // MARK: - Body of the View
       var body: some View {
           VStack {
               // Exercise Name with Fading Effect
               HStack {
                   ScrollView(.horizontal, showsIndicators: false) {
                       Text(exerciseName) // Dynamic Exercise Name
                           .font(.system(size: 20, weight: .semibold))
                           .foregroundColor(.white)
                           .lineLimit(1)
                           .truncationMode(.tail)
                           .background(
                               LinearGradient(
                                   gradient: Gradient(colors: [.clear, .black]),
                                   startPoint: .leading,
                                   endPoint: .trailing
                               )
                           )
                           .frame(maxWidth: .infinity, alignment: .leading) // Align text to left
                           .opacity(1.0) // Set full opacity for visibility
                           .padding(.top, 0.0)
                   }
                   .mask(LinearGradient(
                       gradient: Gradient(stops: [
                           .init(color: .black, location: 0.75),
                           .init(color: .clear, location: 0.85)
                       ]),
                       startPoint: .leading,
                       endPoint: .trailing
                   ))
                   .frame(maxWidth: 150, alignment: .leading) // Ensures text fades off screen
                   .padding(.top, -28.0)
                   .padding(.leading, -20.0)
               }

               Spacer(minLength: 3)

               // REPS and WEIGHT Value Pickers
               HStack {
                   // Reps Picker
                   VStack(spacing: 0.0) {
                       Text("\(repsValue)")
                           .font(.system(size: 36, weight: .bold))
                           .foregroundColor(.white)
                           .padding(.trailing, 1.0)
                           .onTapGesture {
                               isRepsPickerPresented.toggle() // Toggle the reps picker
                           }
                       Text("REPS")
                           .font(.system(size: 16, weight: .regular))
                           .foregroundColor(.gray)
                   }

                   Spacer()

                   // Weight Picker
                   VStack {
                       Text("\(weightValue)")
                           .font(.system(size: 36, weight: .bold))
                           .foregroundColor(.white)
                           .padding(.trailing, 0.0)
                           .onTapGesture {
                               isWeightPickerPresented.toggle() // Toggle the weight picker
                           }
                       Text(unit.uppercased())
                           .font(.system(size: 16, weight: .regular))
                           .foregroundColor(.gray)
                   }
               }
               .padding(36.0)

               Spacer()

               // Save Button with Dynamic Text (Shows percentage progression)
               Button(action: {
                   saveExerciseSet() // Call to save the exercise set
               }) {
                   HStack {
                       Text("Save \(percentage >= 0 ? "+" : "")\(String(format: "%.2f", percentage))%")
                           .font(.system(size: 20, weight: .bold))
                           .foregroundColor(.black)
                       Image(systemName: "arrow.right")
                           .font(.system(size: 20, weight: .bold))
                           .foregroundColor(.black)
                   }
                   .frame(maxWidth: 160)
                   .padding()
                   .background(
                       LinearGradient(
                           gradient: Gradient(colors: [Color(red: 223/255, green: 255/255, blue: 105/255),
                                                        Color(red: 233/255, green: 255/255, blue: 151/255)]),
                           startPoint: .leading,
                           endPoint: .trailing
                       )
                       .cornerRadius(30)
                   )
               }
               .buttonStyle(PlainButtonStyle()) // Custom button style
               .background(Color.clear)
               .shadow(color: .clear, radius: 0)

               // Dots Indicator for Page Navigation
               TabView {
                   ForEach(0..<exerciseList.count, id: \.self) { _ in
                       EmptyView() // Empty view to only show dots (no text)
                   }
               }
               .tabViewStyle(PageTabViewStyle(indexDisplayMode: .always))
               .frame(height: 0) // Hide TabView content, show only dots

               Spacer()

           }
           .background(Color.black.ignoresSafeArea()) // Full screen background
           .onAppear {
               // Perform initial calculation when view appears
               percentage = calculateSetTitle(
                   models: &models,
                   currentReps: repsValue,
                   currentWeight: Double(weightValue),
                   lastReps: 8, // Last reps value (to be replaced with real data)
                   lastWeight: 45.0, // Last weight value (to be replaced with real data)
                   isKg: unit == "kg", // Check if unit is kg
                   userBodyWeight: 70.0, // User body weight (replace with real data)
                   isWeighted: false,
                   isBodyweight: false,
                   isAssisted: false
               )
           }
           .onChange(of: repsValue) { oldValue , newValue in
                    // Trigger calculation when repsValue changes
                    updatePercentage()
                }
                .onChange(of: weightValue) { oldValue , newValue in
                    // Trigger calculation when weightValue changes
                    updatePercentage()
                }

           // Reps Picker Sheet
           .sheet(isPresented: $isRepsPickerPresented) {
               if #available(watchOS 8.0, *) {
                   CustomPickerView(
                       title: "Select Reps",
                       range: 1...100,
                       selectedValue: $repsValue
                   )
               }
           }

           // Weight Picker Sheet
           .sheet(isPresented: $isWeightPickerPresented) {
               if #available(watchOS 8.0, *) {
                   CustomPickerView(
                       title: "Select Weight",
                       range: 0...500,
                       selectedValue: $weightValue
                   )
               }
           }

           // Limit Reached Alert
           .alert(isPresented: $showAlert) {
               Alert(
                   title: Text("Limit Reached"),
                   message: Text("You can only save a maximum of 2 sets."),
                   dismissButton: .default(Text("OK"))
               )
           }
       }
    
    // MARK: - Helper Methods
    
    // Save the exercise set to the database
    private func saveExerciseSet() {
            // Increment the count on each save
            count += 1
            
            // Change exercise name after 2 sets
            if count == 2 {
                exerciseName = exerciseList[1] // Change to "Squats"
            }

            // Save the exercise set to the database
            dbManager.saveExerciseSet(
                repsValue: repsValue,
                weightValue: weightValue,
                unitValue: unit,
                nameValue: exerciseName
            )
            
            // Log for debugging
            print("Exercise Set saved. Current count: \(count), Current Exercise: \(exerciseName)")
        }

        // Update the percentage based on new values (reps, weight, etc.)
        private func updatePercentage() {
            percentage = calculateSetTitle(
                models: &models,
                currentReps: repsValue,
                currentWeight: Double(weightValue),
                lastReps: 8, // Replace with actual value
                lastWeight: 45.0, // Replace with actual value
                isKg: unit == "kg",
                userBodyWeight: 70.0,
                isWeighted: false,
                isBodyweight: false,
                isAssisted: false
            )
        }

        // MARK: - Custom Picker View

        @available(watchOS 8.0, *)
        struct CustomPickerView: View {
            let title: String
            let range: ClosedRange<Int>
            @Binding var selectedValue: Int
            @Environment(\.dismiss) private var dismiss
            
            var body: some View {
                VStack {
                    Text(title)
                        .font(.system(size: 22, weight: .semibold))
                        .padding()
                    
                    ScrollViewReader { scrollProxy in
                        ScrollView(.vertical, showsIndicators: false) {
                            VStack {
                                ForEach(range, id: \.self) { value in
                                    Text("\(value)")
                                        .font(.system(size: 28, weight: .bold))
                                        .padding()
                                        .frame(maxWidth: .infinity)
                                        .background(selectedValue == value ? Color.yellow.opacity(0.2) : Color.clear)
                                        .cornerRadius(8)
                                        .onTapGesture {
                                            selectedValue = value
                                            dismiss() // Dismiss picker sheet
                                        }
                                        .id(value)
                                }
                            }
                            .onAppear {
                                // Scroll to the selected value when the view appears
                                DispatchQueue.main.async {
                                    scrollProxy.scrollTo(selectedValue, anchor: .center)
                                }
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                }
                .padding()
            }
        }

        // MARK: - Preview

        struct ContentView_Previews: PreviewProvider {
            static var previews: some View {
                ContentView()
            }
        }
    }
