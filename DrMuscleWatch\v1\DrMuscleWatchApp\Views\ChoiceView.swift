struct ChoiceView: View {
    @ObservedObject var viewModel: ChoiceViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var isProcessing = false
    
    var body: some View {
        VStack(spacing: 15) {
            Text(viewModel.isLastExercise ? "Workout complete!" : "Exercise complete!")
                .font(.headline)
                .multilineTextAlignment(.center)
            
            if !viewModel.isLastExercise {
                Button(action: {
                    viewModel.addSetSelected()
                    dismiss()
                }) {
                    Text("Add Set")
                        .frame(maxWidth: .infinity)
                }
                .buttonStyle(PrimaryButtonStyle())
                
                Button(action: {
                    isProcessing = true
                    Task {
                        await viewModel.nextExerciseSelected()
                        // Note: We don't dismiss here because the checkmark animation
                        // will take over and then navigate to the next exercise
                    }
                }) {
                    if isProcessing {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                    } else {
                        Text("Finish Exercise")
                            .frame(maxWidth: .infinity)
                    }
                }
                .buttonStyle(SecondaryButtonStyle())
                .disabled(isProcessing)
            } else {
                But<PERSON>(action: {
                    isProcessing = true
                    Task {
                        await viewModel.finishWorkoutSelected()
                        // Note: We don't dismiss here because the checkmark animation
                        // will take over and then navigate to workout complete
                    }
                }) {
                    if isProcessing {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                    } else {
                        Text("Finish Workout")
                            .frame(maxWidth: .infinity)
                    }
                }
                .buttonStyle(PrimaryButtonStyle())
                .disabled(isProcessing)
            }
        }
        .padding()
        .alert(isPresented: $viewModel.showingError) {
            Alert(
                title: Text("Error"),
                message: Text(viewModel.errorMessage),
                dismissButton: .default(Text("OK"))
            )
        }
    }
}