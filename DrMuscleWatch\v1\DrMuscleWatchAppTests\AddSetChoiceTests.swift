import XCTest
@testable import DrMuscleWatchApp

class AddSetChoiceTests: XCTestCase {

    // Test that the choice screen appears after saving the last planned set
    func testChoiceScreenAppearsAfterLastPlannedSet() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockStorageService = MockStorageService()

        let mockSets = [
            SetRecommendationModel(
                reps: 8,
                weight: WeightModel(value: 100, unit: .lb),
                isWarmup: false,
                isFirstWorkSet: false,
                isLastPlannedSet: true, // This is the last planned set
                restDurationSeconds: 60
            )
        ]
        let mockRecommendation = RecommendationModel(
            sets: mockSets,
            exerciseId: 1,
            exerciseName: "Bench Press",
            isBodyweight: false,
            isWeighted: true,
            isAssisted: false,
            isTimeBased: false,
            isUnilateral: false
        )

        mockAPIClient.mockRecommendation = mockRecommendation

        let viewModel = SetViewModel(
            exerciseId: 1,
            workoutId: 1,
            apiClient: mockAPIClient,
            storageService: mockStorageService
        )

        // When
        await viewModel.fetchExerciseDetails()
        let saveResult = await viewModel.saveCurrentSet()

        // Then
        XCTAssertTrue(saveResult, "Set should be saved successfully")
        XCTAssertTrue(viewModel.isLastPlannedSet, "isLastPlannedSet should be true")
        XCTAssertTrue(viewModel.shouldShowChoiceScreen(), "shouldShowChoiceScreen should return true after saving the last planned set")
    }

    // Test that the choice screen does not appear after non-last sets
    func testChoiceScreenDoesNotAppearAfterNonLastSet() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockStorageService = MockStorageService()

        let mockSets = [
            SetRecommendationModel(
                reps: 8,
                weight: WeightModel(value: 100, unit: .lb),
                isWarmup: false,
                isFirstWorkSet: false,
                isLastPlannedSet: false, // This is NOT the last planned set
                restDurationSeconds: 60
            )
        ]
        let mockRecommendation = RecommendationModel(
            sets: mockSets,
            exerciseId: 1,
            exerciseName: "Bench Press",
            isBodyweight: false,
            isWeighted: true,
            isAssisted: false,
            isTimeBased: false,
            isUnilateral: false
        )

        mockAPIClient.mockRecommendation = mockRecommendation

        let viewModel = SetViewModel(
            exerciseId: 1,
            workoutId: 1,
            apiClient: mockAPIClient,
            storageService: mockStorageService
        )

        // When
        await viewModel.fetchExerciseDetails()
        let saveResult = await viewModel.saveCurrentSet()

        // Then
        XCTAssertTrue(saveResult, "Set should be saved successfully")
        XCTAssertFalse(viewModel.isLastPlannedSet, "isLastPlannedSet should be false")
        XCTAssertFalse(viewModel.shouldShowChoiceScreen(), "shouldShowChoiceScreen should return false after saving a non-last set")
    }

    // Test the "Add Set" action
    func testAddSetAction() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockStorageService = MockStorageService()

        let mockSets = [
            SetRecommendationModel(
                reps: 8,
                weight: WeightModel(value: 100, unit: .lb),
                isWarmup: false,
                isFirstWorkSet: false,
                isLastPlannedSet: true, // This is the last planned set
                restDurationSeconds: 60
            )
        ]
        let mockRecommendation = RecommendationModel(
            sets: mockSets,
            exerciseId: 1,
            exerciseName: "Bench Press",
            isBodyweight: false,
            isWeighted: true,
            isAssisted: false,
            isTimeBased: false,
            isUnilateral: false
        )

        mockAPIClient.mockRecommendation = mockRecommendation

        let viewModel = SetViewModel(
            exerciseId: 1,
            workoutId: 1,
            apiClient: mockAPIClient,
            storageService: mockStorageService
        )

        // When
        await viewModel.fetchExerciseDetails()
        let saveResult = await viewModel.saveCurrentSet()
        XCTAssertTrue(viewModel.shouldShowChoiceScreen(), "Choice screen should be shown")

        // When user taps "Add Set"
        viewModel.addSet()

        // Then
        XCTAssertFalse(viewModel.shouldShowChoiceScreen(), "Choice screen should be hidden after adding a set")
        XCTAssertFalse(viewModel.isTimerActive, "Timer should not be active")
        XCTAssertFalse(viewModel.isLastPlannedSet, "isLastPlannedSet should be false after adding a set")
    }

    // Test the "Next Exercise" action (skipping the timer)
    func testNextExerciseAction() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockStorageService = MockStorageService()

        let mockSets = [
            SetRecommendationModel(
                reps: 8,
                weight: WeightModel(value: 100, unit: .lb),
                isWarmup: false,
                isFirstWorkSet: false,
                isLastPlannedSet: true, // This is the last planned set
                restDurationSeconds: 60
            )
        ]
        let mockRecommendation = RecommendationModel(
            sets: mockSets,
            exerciseId: 1,
            exerciseName: "Bench Press",
            isBodyweight: false,
            isWeighted: true,
            isAssisted: false,
            isTimeBased: false,
            isUnilateral: false
        )

        mockAPIClient.mockRecommendation = mockRecommendation

        let viewModel = SetViewModel(
            exerciseId: 1,
            workoutId: 1,
            apiClient: mockAPIClient,
            storageService: mockStorageService
        )

        // When
        await viewModel.fetchExerciseDetails()
        let saveResult = await viewModel.saveCurrentSet()
        XCTAssertTrue(viewModel.shouldShowChoiceScreen(), "Choice screen should be shown")

        // When user taps "Next Exercise" (skipping the timer)
        let result = viewModel.nextExercise()

        // Then
        XCTAssertTrue(result, "nextExercise should return true")
        XCTAssertFalse(viewModel.shouldShowChoiceScreen(), "Choice screen should be hidden after moving to next exercise")
        XCTAssertFalse(viewModel.isTimerActive, "Timer should not be active")
    }

    // Test the timer expiry behavior
    func testTimerExpiryBehavior() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockStorageService = MockStorageService()

        let mockSets = [
            SetRecommendationModel(
                reps: 8,
                weight: WeightModel(value: 100, unit: .lb),
                isWarmup: false,
                isFirstWorkSet: false,
                isLastPlannedSet: true, // This is the last planned set
                restDurationSeconds: 60
            )
        ]
        let mockRecommendation = RecommendationModel(
            sets: mockSets,
            exerciseId: 1,
            exerciseName: "Bench Press",
            isBodyweight: false,
            isWeighted: true,
            isAssisted: false,
            isTimeBased: false,
            isUnilateral: false
        )

        mockAPIClient.mockRecommendation = mockRecommendation

        let viewModel = SetViewModel(
            exerciseId: 1,
            workoutId: 1,
            apiClient: mockAPIClient,
            storageService: mockStorageService
        )

        // When
        await viewModel.fetchExerciseDetails()
        let saveResult = await viewModel.saveCurrentSet()
        XCTAssertTrue(viewModel.shouldShowChoiceScreen(), "Choice screen should be shown")

        // When the timer is started
        viewModel.startTimer()
        XCTAssertTrue(viewModel.isTimerActive, "Timer should be active")

        // Simulate timer expiry
        viewModel.secondsRemaining = 0
        viewModel.timerComplete()

        // Then
        XCTAssertFalse(viewModel.isTimerActive, "Timer should not be active after completion")
        XCTAssertTrue(viewModel.shouldShowChoiceScreen(), "Choice screen should still be shown after timer expiry")
    }

    // Test that the "Add Set" action preserves the reps and weight from the completed set
    func testAddSetPreservesRepsAndWeight() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockStorageService = MockStorageService()

        // Create a mock set with specific reps and weight
        let initialReps = 8
        let initialWeight = 100.0

        let mockSets = [
            SetRecommendationModel(
                reps: initialReps,
                weight: WeightModel(value: initialWeight, unit: .lb),
                isWarmup: false,
                isFirstWorkSet: false,
                isLastPlannedSet: true, // This is the last planned set
                restDurationSeconds: 60
            )
        ]
        let mockRecommendation = RecommendationModel(
            sets: mockSets,
            exerciseId: 1,
            exerciseName: "Bench Press",
            isBodyweight: false,
            isWeighted: true,
            isAssisted: false,
            isTimeBased: false,
            isUnilateral: false
        )

        mockAPIClient.mockRecommendation = mockRecommendation

        let viewModel = SetViewModel(
            exerciseId: 1,
            workoutId: 1,
            apiClient: mockAPIClient,
            storageService: mockStorageService
        )

        // When - Load the exercise and modify reps and weight
        await viewModel.fetchExerciseDetails()

        // Change the reps and weight to simulate user input
        let modifiedReps = 10
        let modifiedWeight = 105.0
        viewModel.updateReps(modifiedReps)
        viewModel.updateWeight(modifiedWeight)

        // Save the set to trigger the choice screen
        let saveResult = await viewModel.saveCurrentSet()
        XCTAssertTrue(saveResult, "Set should be saved successfully")
        XCTAssertTrue(viewModel.shouldShowChoiceScreen(), "Choice screen should be shown")

        // When - User taps "Add Set"
        viewModel.addSet()

        // Then - Verify the reps and weight are preserved from the completed set
        XCTAssertFalse(viewModel.shouldShowChoiceScreen(), "Choice screen should be hidden after adding a set")
        XCTAssertEqual(viewModel.currentReps, modifiedReps, "Reps should be preserved from the completed set")
        XCTAssertEqual(viewModel.currentWeight, modifiedWeight, "Weight should be preserved from the completed set")
        XCTAssertFalse(viewModel.isLastPlannedSet, "isLastPlannedSet should be false after adding a set")
    }
}
