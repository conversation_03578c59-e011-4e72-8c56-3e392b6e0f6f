# Dr. Muscle Watch App - UI Components

This document describes the UI components used in the Dr. Muscle Watch app, their styling, and usage guidelines. The goal is to maintain a consistent look and feel throughout the app that matches the original implementation.

## Color Scheme

The app uses a dark theme with the following key colors:

- **Background**: Black (#000000)
- **Primary Text**: White (#FFFFFF)
- **Secondary Text**: Light Gray
- **Accent Blue**: #195377 (for buttons and highlights)
- **Green**: #5CD196 (for success indicators)
- **Red**: #BA1C31 (for warnings/errors)
- **Yellow CTA Gradient**:
  - Start: #DFE769 (223, 255, 105)
  - End: #E9FF97 (233, 255, 151)

## Typography

- **Title**: System font, 20pt, Semibold
- **Large Values**: System font, 36pt, Bold (for reps and weight numbers)
- **Body Text**: System font, 16pt, Regular
- **Small Text**: System font, 14pt, Regular
- **CTA Button Text**: System font, 20pt, Bold

## Components

### CTAButton

The primary call-to-action button with a yellow gradient background. Used for the most important actions like "Save Set".

**Usage:**
```swift
CTAButton(
    title: "Save",
    icon: "arrow.right",
    percentage: 5.25,
    showPercentage: true
) {
    // Action to perform
}
```

**Properties:**
- `title`: The button text
- `icon`: Optional system icon name (default: "arrow.right")
- `percentage`: Progress percentage to display (default: 0.0)
- `showPercentage`: Whether to show the percentage (default: false)
- `action`: Closure to execute when button is tapped

### ValueDisplay

A component for displaying a value with a label, used for reps and weight.

**Usage:**
```swift
ValueDisplay(
    value: "\(repsValue)",
    label: "REPS"
) {
    // Action when tapped (e.g., show picker)
}
```

**Properties:**
- `value`: The value to display (as a string)
- `label`: The label text (e.g., "REPS" or "LBS")
- `action`: Closure to execute when tapped

### ExerciseHeader

A component for displaying the exercise name with proper styling and truncation.

**Usage:**
```swift
ExerciseHeader(exerciseName: "Bench Press")
```

**Properties:**
- `exerciseName`: The name of the exercise to display

### CustomPicker

A custom picker for selecting values like reps or weight.

**Usage:**
```swift
CustomPicker(
    title: "Select Reps",
    range: 1...100,
    selectedValue: $repsValue
)
```

**Properties:**
- `title`: The picker title
- `range`: The range of values to display
- `selectedValue`: Binding to the selected value

### TimerView

A timer view for rest periods.

**Usage:**
```swift
TimerView(
    secondsRemaining: $timerSeconds,
    isTimerRunning: $isTimerRunning
) {
    // Action when hide button is tapped
}
```

**Properties:**
- `secondsRemaining`: Binding to the remaining seconds
- `isTimerRunning`: Binding to whether the timer is running
- `onHide`: Closure to execute when hide button is tapped

### RIRPicker

A picker for Reps In Reserve (RIR).

**Usage:**
```swift
RIRPicker(
    selectedRIR: $selectedRIR
) { rir in
    // Action when confirmed with selected RIR
}
```

**Properties:**
- `selectedRIR`: Binding to the selected RIR value
- `onConfirm`: Closure to execute when confirmed, passing the selected RIR

## Button Styles

The app provides several button styles through the `AppTheme` struct:

1. **primaryButtonStyle()**: Yellow gradient CTA button style
2. **secondaryButtonStyle()**: Blue button style
3. **tertiaryButtonStyle()**: Transparent button with white text

**Usage:**
```swift
Button("Action") {
    // Action
}
.buttonStyle(AppTheme.primaryButtonStyle())
```

## Layout Guidelines

- Use standard padding of 8 points (`AppTheme.standardPadding`)
- Standard button height is 44 points (`AppTheme.buttonHeight`)
- Standard corner radius is 30 points (`AppTheme.cornerRadius`)
- Maintain adequate spacing between UI elements for touch targets
- Ensure text is legible on all watch sizes

## Accessibility Considerations

- Ensure text has sufficient contrast against backgrounds
- Provide adequate touch targets (minimum 44x44 points)
- Use system fonts which automatically adjust for accessibility settings
- Test with VoiceOver enabled

## Implementation Notes

All UI components are implemented using SwiftUI and are designed to be reusable across the app. The styling is centralized in the `AppTheme` struct to ensure consistency.

### Project Organization

The project is organized into two main directories:

- **v0/**: Contains the old implementation (Xamarin-based) for reference
- **v1/**: Contains the new implementation (Swift/SwiftUI-based) with all the components described in this document

### Component Locations

All UI components are located in the following directories:

- Style Guide: `v1/DrMuscleWatchApp/AppTheme.swift`
- Reusable Components: `v1/DrMuscleWatchApp/Components/`
- Views: `v1/DrMuscleWatchApp/Views/`

When creating new screens or components, refer to this document to maintain the established look and feel of the Dr. Muscle Watch app.
