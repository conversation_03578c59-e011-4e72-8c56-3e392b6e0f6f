using Acr.UserDialogs;
using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Resx;
using RGPopup.Maui.Pages;
using RGPopup.Maui.Services;
using System.Diagnostics;
using System.Text.RegularExpressions;

namespace DrMaxMuscle.Views;

public partial class CustomPromptConfig : PopupPage
{
    //public enum PopupAction
    //{
    //    Cancel,
    //    OK
    //}
    public event EventHandler<PopupAction> ActionSelected;
    public string text { get; set; }
    public string _okBtnText { get; set; }
    public CustomPromptConfig(string title, string placeholder, string okBtnText, string cancelBtnText, string subTitle = "", Keyboard inputType = default, string editText = "",int max = 0,bool isClose = true)
    {
        InitializeComponent();
        titleLbl.Text = title;
        _okBtnText = okBtnText;
        entryLbl.Keyboard = inputType;
        this.CloseWhenBackgroundIsClicked = isClose;
        //if(Device.RuntimePlatform == Device.Android)
        //{
        //    customPopup.WidthRequest = DeviceDisplay.Current.MainDisplayInfo.Width;
        //}
        //else if(Device.RuntimePlatform == Device.iOS)
        //{
        //    var displayInfo = DeviceDisplay.MainDisplayInfo;
        //    var width = displayInfo.Width / displayInfo.Density;
        //    customPopup.WidthRequest = width;
        //}

        if (!string.IsNullOrEmpty(placeholder) || !string.IsNullOrEmpty(editText))
        {
            entryLblStack.IsVisible = true;
            entryLbl.Placeholder = placeholder;
        }
        else
        {
            entryLblStack.IsVisible = false;
        }
        if (!string.IsNullOrEmpty(editText))
        {
            entryLbl.Text = editText;
        }
        if(max > 0)
        {
            entryLbl.MaxLength = max;
        }
        if (!string.IsNullOrEmpty(subTitle))
        {
            subtitleLbl.Text = subTitle;
            subtitleLbl.IsVisible = true;
        }
        else
        {
            subtitleLbl.IsVisible = false;
        }
        if(string.IsNullOrEmpty(title)){
            titleLbl.IsVisible = false;
        }
        okBtn.Text = okBtnText;
        if (!string.IsNullOrEmpty(cancelBtnText))
        {
            cancelBtn.IsVisible = true;
            cancelBtn.Text = cancelBtnText;
        }
        else
        {
            cancelBtn.IsVisible = false;
        }

    }

    private async void OK_Btn_Clicked(object sender, EventArgs e)
    {
        Debug.WriteLine("OK_Btn_Clicked popups to dismiss.");

        await entryLbl.HideSoftInputAsync(CancellationToken.None);
        text = entryLbl.Text;
        if (!string.IsNullOrEmpty(text) || titleLbl.Text == "Email already in use" || titleLbl.Text == AppResources.InvalidEmailAddress)
        {
            if (PopupNavigation.Instance.PopupStack?.Count() > 0)
                await MauiProgram.SafeDismissTopPopup();
            ActionSelected?.Invoke(this, PopupAction.OK);
        }
        
    }

    private async void Cancel_Btn_Clicked(object sender, EventArgs e)
    {
        Debug.WriteLine("Cancel_Btn_Clicked popups to dismiss.");

        await entryLbl.HideSoftInputAsync(CancellationToken.None);
        if (PopupNavigation.Instance.PopupStack?.Count() > 0)
            await MauiProgram.SafeDismissTopPopup();
        ActionSelected?.Invoke(this, PopupAction.Cancel);
    }

    private void entryLbl_TextChanged(object sender, TextChangedEventArgs e)
    {

        // if(entryLbl.Keyboard == Keyboard.Numeric){
        //      const string textRegex = @"^\d+(?:[\.,]\d{0,5})?$";
        //     var text = e.NewTextValue.Replace(",", ".");
        //     bool IsValid = Regex.IsMatch(text, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
        //     if (IsValid == false && !string.IsNullOrEmpty(e.NewTextValue))
        //     {
        //         double result;
        //         entryLbl.Text = e.NewTextValue.Substring(0, e.NewTextValue.Length - 1);
        //         double.TryParse(e.NewTextValue, out result);
        //         entryLbl.Text = result.ToString();
        //     }
        // }else if(entryLbl.Keyboard == Keyboard.Default){
        //     if (!string.IsNullOrEmpty(e.NewTextValue))
        //     {
        //         if (e.NewTextValue.Length == 1)
        //             entryLbl.Text = char.ToUpper(e.NewTextValue[0]) + "";
        //         else if (e.NewTextValue.Length > 1)
        //             entryLbl.Text = char.ToUpper(e.NewTextValue[0]) + e.NewTextValue.Substring(1);
        //     }
        // }
        // if(_okBtnText == AppResources.Add)
        // {
            //const string textRegex = @"^\d+(?:[\.,]\d{0,5})?$";
            //var text = e.NewTextValue.Replace(",", ".");
            //bool IsValid = Regex.IsMatch(text, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
            //if (IsValid == false && !string.IsNullOrEmpty(e.NewTextValue))
            //{
            //    double result;
            //    entryLbl.Text = e.NewTextValue.Substring(0, e.NewTextValue.Length - 1);
            //    double.TryParse(e.NewTextValue, out result);
            //    entryLbl.Text = result.ToString();
            //}

        // }
    }
}