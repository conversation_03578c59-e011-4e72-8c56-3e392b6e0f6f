import XCTest
@testable import DrMuscleWatchApp

class PerformanceCalculationTests: XCTestCase {
    
    // Mock API client that returns predefined data
    class MockAPIClient: DrMuscleAPIClient {
        var oneRMData: [OneRMModel] = []
        
        override func getOneRMForExercise(model: GetOneRMforExerciseModel) async throws -> [OneRMModel] {
            return oneRMData
        }
    }
    
    // Mock storage service
    class MockStorageService: StorageService {
        override func saveSetLog(exerciseID: Int64, workoutID: Int64, reps: Int16, weight: Double, weightUnit: String, rir: Int16?, timestamp: Date, isWarmup: Bool, needsSync: Bool) throws -> Bool {
            return true
        }
    }
    
    func testPerformanceCalculationWithNoHistoricalData() async {
        // Given
        let mockAPIClient = MockAPIClient()
        let viewModel = SetViewModel(
            exerciseId: 123,
            workoutId: 456,
            apiClient: mockAPIClient,
            storageService: MockStorageService()
        )
        
        // Set up recommendation with a set
        let set = SetRecommendationModel(
            reps: 10,
            weight: WeightModel(value: 100, unit: .lbs),
            isWarmup: false,
            isFirstWorkSet: true,
            isLastPlannedSet: false,
            restDurationSeconds: 60
        )
        let recommendation = RecommendationModel(
            exerciseName: "Bench Press",
            sets: [set]
        )
        
        // When
        // Manually set the recommendation and update current set details
        await MainActor.run {
            viewModel.updateCurrentSetDetails(set)
            
            // Test with same reps and weight (no change)
            XCTAssertEqual(viewModel.getPerformancePercentage(), 0.0, accuracy: 0.01)
            
            // Test with increased reps
            viewModel.updateReps(12)
            let increaseRepsPercentage = viewModel.getPerformancePercentage()
            XCTAssertGreaterThan(increaseRepsPercentage, 0.0)
            
            // Test with decreased weight
            viewModel.updateReps(10) // Reset reps
            viewModel.updateWeight(90)
            let decreaseWeightPercentage = viewModel.getPerformancePercentage()
            XCTAssertLessThan(decreaseWeightPercentage, 0.0)
            
            // Test with both increased
            viewModel.updateReps(12)
            viewModel.updateWeight(110)
            let bothIncreasedPercentage = viewModel.getPerformancePercentage()
            XCTAssertGreaterThan(bothIncreasedPercentage, 0.0)
        }
    }
    
    func testPerformanceCalculationWithHistoricalData() async {
        // Given
        let mockAPIClient = MockAPIClient()
        let viewModel = SetViewModel(
            exerciseId: 123,
            workoutId: 456,
            apiClient: mockAPIClient,
            storageService: MockStorageService()
        )
        
        // Set up historical data
        let historicalWeight = WeightModel(value: 100, unit: .lbs)
        let historicalOneRM = WeightModel(value: 133.3, unit: .lbs) // 100 * (1 + 0.0333 * 10)
        let historicalData = OneRMModel(
            exerciseId: 123,
            userId: "user123",
            oneRMDate: Date(),
            oneRM: historicalOneRM,
            reps: 10,
            weight: historicalWeight
        )
        mockAPIClient.oneRMData = [historicalData]
        
        // Set up recommendation with a set
        let set = SetRecommendationModel(
            reps: 10,
            weight: WeightModel(value: 100, unit: .lbs),
            isWarmup: false,
            isFirstWorkSet: true,
            isLastPlannedSet: false,
            restDurationSeconds: 60
        )
        
        // When
        // Manually set the recommendation and update current set details
        await MainActor.run {
            viewModel.updateCurrentSetDetails(set)
            
            // Manually set historical data
            await viewModel.fetchHistoricalData()
            
            // Test with same reps and weight (no change)
            XCTAssertEqual(viewModel.getPerformancePercentage(), 0.0, accuracy: 0.01)
            
            // Test with increased reps
            viewModel.updateReps(12)
            let increaseRepsPercentage = viewModel.getPerformancePercentage()
            XCTAssertGreaterThan(increaseRepsPercentage, 0.0)
            
            // Test with decreased weight
            viewModel.updateReps(10) // Reset reps
            viewModel.updateWeight(90)
            let decreaseWeightPercentage = viewModel.getPerformancePercentage()
            XCTAssertLessThan(decreaseWeightPercentage, 0.0)
            
            // Test with both increased
            viewModel.updateReps(12)
            viewModel.updateWeight(110)
            let bothIncreasedPercentage = viewModel.getPerformancePercentage()
            XCTAssertGreaterThan(bothIncreasedPercentage, 0.0)
        }
    }
}
