import SwiftUI
import AuthenticationServices

/// View for the login screen
struct LoginView: View {
    /// The view model for the login screen
    @StateObject private var viewModel = LoginViewModel()

    /// The authentication manager
    @EnvironmentObject private var authManager: AuthenticationManager

    var body: some View {
        ScrollView {
            VStack(spacing: 16) {
                // App logo
                Image(systemName: "dumbbell.fill")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 60, height: 60)
                    .foregroundColor(.yellow)
                    .padding(.bottom, 8)

                // App name
                Text("Dr. Muscle")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                // Welcome message
                Text("Welcome to the Watch App")
                    .font(.caption)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                    .padding(.bottom, 8)

                // Sign in with Apple button
                SignInWithAppleButton()
                    .frame(height: 44)
                    .cornerRadius(8)
                    .onTapGesture {
                        viewModel.signInWithApple()
                    }
                    .accessibilityLabel("Sign in with <PERSON>")
                    .accessibilityHint("Double tap to sign in with your Apple ID")

                // Error message
                if let errorMessage = viewModel.errorMessage {
                    Text(errorMessage)
                        .font(.caption)
                        .foregroundColor(.red)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                        .accessibilityLabel("Error: \(errorMessage)")
                }

                // Loading indicator
                if viewModel.isAuthenticating {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle())
                        .accessibilityLabel("Signing in")
                }
            }
            .padding()
        }
    }
}

/// Custom Sign in with Apple button for watchOS
struct SignInWithAppleButton: View {
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: "applelogo")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 16, height: 16)
                .foregroundColor(.white)

            Text("Sign in with Apple")
                .font(.footnote)
                .fontWeight(.semibold)
                .foregroundColor(.white)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .frame(maxWidth: .infinity)
        .background(Color.black)
        .cornerRadius(8)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
        )
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

#Preview {
    LoginView()
        .environmentObject(AuthenticationManager.shared)
}
