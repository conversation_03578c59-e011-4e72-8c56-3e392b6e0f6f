import XCTest
@testable import DrMuscleWatchApp

class SetViewModelTests: XCTestCase {

    func testFetchExerciseDetailsSuccess() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockSets = [
            SetRecommendationModel(
                reps: 8,
                weight: WeightModel(value: 100, unit: .lb),
                isWarmup: false,
                isFirstWorkSet: true,
                isLastPlannedSet: false,
                restDurationSeconds: 90
            ),
            SetRecommendationModel(
                reps: 8,
                weight: WeightModel(value: 105, unit: .lb),
                isWarmup: false,
                isFirstWorkSet: false,
                isLastPlannedSet: true,
                restDurationSeconds: 120
            )
        ]
        let mockRecommendation = RecommendationModel(
            sets: mockSets,
            exerciseId: 1,
            exerciseName: "Bench Press",
            isBodyweight: false,
            isWeighted: true,
            isAssisted: false,
            isTimeBased: false,
            isUnilateral: false
        )
        mockAPIClient.mockRecommendation = mockRecommendation

        let viewModel = SetViewModel(exerciseId: 1, workoutId: 1, apiClient: mockAPIClient)

        // When
        await viewModel.fetchExerciseDetails()

        // Then
        XCTAssertFalse(viewModel.isLoading, "Loading state should be false after fetch completes")
        XCTAssertFalse(viewModel.hasError, "Error state should be false after successful fetch")
        XCTAssertEqual(viewModel.exerciseName, "Bench Press", "Exercise name should be Bench Press")
        XCTAssertEqual(viewModel.currentSetIndex, 0, "Current set index should be 0")
        XCTAssertEqual(viewModel.currentReps, 8, "Current reps should be 8")
        XCTAssertEqual(viewModel.currentWeight, 100, "Current weight should be 100")
        XCTAssertEqual(viewModel.weightUnit, "lbs", "Weight unit should be lbs")
        XCTAssertTrue(viewModel.isFirstWorkSet, "Current set should be marked as first work set")
        XCTAssertFalse(viewModel.isLastPlannedSet, "Current set should not be marked as last planned set")
        XCTAssertEqual(viewModel.restDurationSeconds, 90, "Rest duration should be 90 seconds")
    }

    func testFetchExerciseDetailsError() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        mockAPIClient.shouldThrowError = true

        let viewModel = SetViewModel(exerciseId: 1, workoutId: 1, apiClient: mockAPIClient)

        // When
        await viewModel.fetchExerciseDetails()

        // Then
        XCTAssertFalse(viewModel.isLoading, "Loading state should be false after fetch completes")
        XCTAssertTrue(viewModel.hasError, "Error state should be true after failed fetch")
        XCTAssertNotNil(viewModel.errorMessage, "Error message should not be nil")
    }

    func testMoveToNextSet() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockSets = [
            SetRecommendationModel(
                reps: 8,
                weight: WeightModel(value: 100, unit: .lb),
                isWarmup: false,
                isFirstWorkSet: true,
                isLastPlannedSet: false,
                restDurationSeconds: 90
            ),
            SetRecommendationModel(
                reps: 8,
                weight: WeightModel(value: 105, unit: .lb),
                isWarmup: false,
                isFirstWorkSet: false,
                isLastPlannedSet: true,
                restDurationSeconds: 120
            )
        ]
        let mockRecommendation = RecommendationModel(
            sets: mockSets,
            exerciseId: 1,
            exerciseName: "Bench Press",
            isBodyweight: false,
            isWeighted: true,
            isAssisted: false,
            isTimeBased: false,
            isUnilateral: false
        )
        mockAPIClient.mockRecommendation = mockRecommendation

        let viewModel = SetViewModel(exerciseId: 1, workoutId: 1, apiClient: mockAPIClient)
        await viewModel.fetchExerciseDetails()

        // When
        let result = viewModel.moveToNextSet()

        // Then
        XCTAssertTrue(result, "Moving to next set should return true")
        XCTAssertEqual(viewModel.currentSetIndex, 1, "Current set index should be 1")
        XCTAssertEqual(viewModel.currentReps, 8, "Current reps should be 8")
        XCTAssertEqual(viewModel.currentWeight, 105, "Current weight should be 105")
        XCTAssertFalse(viewModel.isFirstWorkSet, "Current set should not be marked as first work set")
        XCTAssertTrue(viewModel.isLastPlannedSet, "Current set should be marked as last planned set")
        XCTAssertEqual(viewModel.restDurationSeconds, 120, "Rest duration should be 120 seconds")
    }

    func testMoveToNextSetAtEnd() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockSets = [
            SetRecommendationModel(
                reps: 8,
                weight: WeightModel(value: 100, unit: .lb),
                isWarmup: false,
                isFirstWorkSet: true,
                isLastPlannedSet: true,
                restDurationSeconds: 90
            )
        ]
        let mockRecommendation = RecommendationModel(
            sets: mockSets,
            exerciseId: 1,
            exerciseName: "Bench Press",
            isBodyweight: false,
            isWeighted: true,
            isAssisted: false,
            isTimeBased: false,
            isUnilateral: false
        )
        mockAPIClient.mockRecommendation = mockRecommendation

        let viewModel = SetViewModel(exerciseId: 1, workoutId: 1, apiClient: mockAPIClient)
        await viewModel.fetchExerciseDetails()

        // When
        let result = viewModel.moveToNextSet()

        // Then
        XCTAssertFalse(result, "Moving to next set should return false when at the end")
        XCTAssertEqual(viewModel.currentSetIndex, 0, "Current set index should remain 0")
    }

    func testUpdateReps() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockSets = [
            SetRecommendationModel(
                reps: 8,
                weight: WeightModel(value: 100, unit: .lb),
                isWarmup: false,
                isFirstWorkSet: true,
                isLastPlannedSet: false,
                restDurationSeconds: 90
            )
        ]
        let mockRecommendation = RecommendationModel(
            sets: mockSets,
            exerciseId: 1,
            exerciseName: "Bench Press",
            isBodyweight: false,
            isWeighted: true,
            isAssisted: false,
            isTimeBased: false,
            isUnilateral: false
        )
        mockAPIClient.mockRecommendation = mockRecommendation

        let viewModel = SetViewModel(exerciseId: 1, workoutId: 1, apiClient: mockAPIClient)
        await viewModel.fetchExerciseDetails()

        // When
        viewModel.updateReps(10)

        // Then
        XCTAssertEqual(viewModel.currentReps, 10, "Current reps should be updated to 10")

        // Verify the currentSetData is also updated
        let currentSetData = viewModel.getCurrentSetData()
        XCTAssertEqual(currentSetData.reps, 10, "Current set data reps should be updated to 10")
    }

    func testUpdateWeight() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockSets = [
            SetRecommendationModel(
                reps: 8,
                weight: WeightModel(value: 100, unit: .lb),
                isWarmup: false,
                isFirstWorkSet: true,
                isLastPlannedSet: false,
                restDurationSeconds: 90
            )
        ]
        let mockRecommendation = RecommendationModel(
            sets: mockSets,
            exerciseId: 1,
            exerciseName: "Bench Press",
            isBodyweight: false,
            isWeighted: true,
            isAssisted: false,
            isTimeBased: false,
            isUnilateral: false
        )
        mockAPIClient.mockRecommendation = mockRecommendation

        let viewModel = SetViewModel(exerciseId: 1, workoutId: 1, apiClient: mockAPIClient)
        await viewModel.fetchExerciseDetails()

        // When
        viewModel.updateWeight(105)

        // Then
        XCTAssertEqual(viewModel.currentWeight, 105, "Current weight should be updated to 105")

        // Verify the currentSetData is also updated
        let currentSetData = viewModel.getCurrentSetData()
        XCTAssertEqual(currentSetData.weight, 105, "Current set data weight should be updated to 105")
    }

    func testComputedProperties() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockSets = [
            SetRecommendationModel(
                reps: 8,
                weight: WeightModel(value: 100, unit: .lb),
                isWarmup: true,
                isFirstWorkSet: false,
                isLastPlannedSet: false,
                restDurationSeconds: 60
            ),
            SetRecommendationModel(
                reps: 8,
                weight: WeightModel(value: 105, unit: .lb),
                isWarmup: false,
                isFirstWorkSet: true,
                isLastPlannedSet: false,
                restDurationSeconds: 90
            )
        ]
        let mockRecommendation = RecommendationModel(
            sets: mockSets,
            exerciseId: 1,
            exerciseName: "Bench Press",
            isBodyweight: false,
            isWeighted: true,
            isAssisted: false,
            isTimeBased: false,
            isUnilateral: false
        )
        mockAPIClient.mockRecommendation = mockRecommendation

        let viewModel = SetViewModel(exerciseId: 1, workoutId: 1, apiClient: mockAPIClient)
        await viewModel.fetchExerciseDetails()

        // Then
        XCTAssertEqual(viewModel.totalSets, 2, "Total sets should be 2")
        XCTAssertEqual(viewModel.currentSetNumber, 1, "Current set number should be 1")
        XCTAssertTrue(viewModel.isWarmupSet, "First set should be a warmup set")

        // When
        _ = viewModel.moveToNextSet()

        // Then
        XCTAssertEqual(viewModel.currentSetNumber, 2, "Current set number should be 2")
        XCTAssertFalse(viewModel.isWarmupSet, "Second set should not be a warmup set")
    }

    func testSaveCurrentSet() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockStorageService = MockStorageService()

        let mockSets = [
            SetRecommendationModel(
                reps: 8,
                weight: WeightModel(value: 100, unit: .lb),
                isWarmup: false,
                isFirstWorkSet: false,
                isLastPlannedSet: false,
                restDurationSeconds: 90
            )
        ]
        let mockRecommendation = RecommendationModel(
            sets: mockSets,
            exerciseId: 1,
            exerciseName: "Bench Press",
            isBodyweight: false,
            isWeighted: true,
            isAssisted: false,
            isTimeBased: false,
            isUnilateral: false
        )
        mockAPIClient.mockRecommendation = mockRecommendation

        let viewModel = SetViewModel(exerciseId: 1, workoutId: 1, apiClient: mockAPIClient, storageService: mockStorageService)
        await viewModel.fetchExerciseDetails()

        // Modify the reps and weight to simulate user input
        viewModel.updateReps(10)
        viewModel.updateWeight(105.0)

        // When
        let result = await viewModel.saveCurrentSet()

        // Then
        XCTAssertTrue(result, "Saving set should return true")
        XCTAssertTrue(mockStorageService.saveSetLogCalled, "saveSetLog should be called")
        XCTAssertEqual(mockStorageService.lastSavedExerciseID, 1, "Exercise ID should be 1")
        XCTAssertEqual(mockStorageService.lastSavedWorkoutID, 1, "Workout ID should be 1")
        XCTAssertEqual(mockStorageService.lastSavedReps, 10, "Reps should be 10")
        XCTAssertEqual(mockStorageService.lastSavedWeight, 105.0, "Weight should be 105.0")
        XCTAssertEqual(mockStorageService.lastSavedWeightUnit, "lbs", "Weight unit should be lbs")
        XCTAssertNil(mockStorageService.lastSavedRIR, "RIR should be nil")
        XCTAssertFalse(mockStorageService.lastSavedIsWarmup, "isWarmup should be false")
        XCTAssertTrue(mockStorageService.lastSavedNeedsSync, "needsSync should be true")
    }

    func testSaveCurrentSetWithRIR() async {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockStorageService = MockStorageService()

        let mockSets = [
            SetRecommendationModel(
                reps: 8,
                weight: WeightModel(value: 100, unit: .lb),
                isWarmup: false,
                isFirstWorkSet: true, // This is the first work set, so RIR should be saved
                isLastPlannedSet: false,
                restDurationSeconds: 90
            )
        ]
        let mockRecommendation = RecommendationModel(
            sets: mockSets,
            exerciseId: 1,
            exerciseName: "Bench Press",
            isBodyweight: false,
            isWeighted: true,
            isAssisted: false,
            isTimeBased: false,
            isUnilateral: false
        )
        mockAPIClient.mockRecommendation = mockRecommendation

        let viewModel = SetViewModel(exerciseId: 1, workoutId: 1, apiClient: mockAPIClient, storageService: mockStorageService)
        await viewModel.fetchExerciseDetails()

        // When
        let result = await viewModel.saveCurrentSet(rir: 2) // Save with RIR value

        // Then
        XCTAssertTrue(result, "Saving set should return true")
        XCTAssertTrue(mockStorageService.saveSetLogCalled, "saveSetLog should be called")
        XCTAssertEqual(mockStorageService.lastSavedExerciseID, 1, "Exercise ID should be 1")
        XCTAssertEqual(mockStorageService.lastSavedWorkoutID, 1, "Workout ID should be 1")
        XCTAssertEqual(mockStorageService.lastSavedReps, 8, "Reps should be 8")
        XCTAssertEqual(mockStorageService.lastSavedWeight, 100.0, "Weight should be 100.0")
        XCTAssertEqual(mockStorageService.lastSavedWeightUnit, "lbs", "Weight unit should be lbs")
        XCTAssertEqual(mockStorageService.lastSavedRIR, 2, "RIR should be 2")
        XCTAssertFalse(mockStorageService.lastSavedIsWarmup, "isWarmup should be false")
        XCTAssertTrue(mockStorageService.lastSavedNeedsSync, "needsSync should be true")
    }
}

// Mock API Client for testing
class MockDrMuscleAPIClient: DrMuscleAPIClient {
    var mockRecommendation: RecommendationModel?
    var shouldThrowError = false

    override func getRecommendationForExercise(model: RecommendationRequestModel) async throws -> RecommendationModel {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 0, userInfo: [NSLocalizedDescriptionKey: "Mock API error"])
        }

        return mockRecommendation ?? RecommendationModel(
            sets: [],
            exerciseId: 0,
            exerciseName: "",
            isBodyweight: false,
            isWeighted: false,
            isAssisted: false,
            isTimeBased: false,
            isUnilateral: false
        )
    }
}

// Mock StorageService for testing
class MockStorageService: StorageService {
    var saveSetLogCalled = false
    var lastSavedExerciseID: Int64 = 0
    var lastSavedWorkoutID: Int64 = 0
    var lastSavedReps: Int16 = 0
    var lastSavedWeight: Double = 0.0
    var lastSavedWeightUnit: String = ""
    var lastSavedRIR: Int16? = nil
    var lastSavedIsWarmup: Bool = false
    var lastSavedNeedsSync: Bool = false

    override func saveSetLog(
        exerciseID: Int64,
        workoutID: Int64,
        reps: Int16,
        weight: Double,
        weightUnit: String,
        rir: Int16? = nil,
        timestamp: Date,
        isWarmup: Bool = false,
        needsSync: Bool = true
    ) throws -> SetLog {
        saveSetLogCalled = true
        lastSavedExerciseID = exerciseID
        lastSavedWorkoutID = workoutID
        lastSavedReps = reps
        lastSavedWeight = weight
        lastSavedWeightUnit = weightUnit
        lastSavedRIR = rir
        lastSavedIsWarmup = isWarmup
        lastSavedNeedsSync = needsSync

        // Return a mock SetLog
        let context = viewContext
        let setLog = SetLog(context: context)
        setLog.exerciseID = exerciseID
        setLog.workoutID = workoutID
        setLog.reps = reps
        setLog.weight = weight
        setLog.weightUnit = weightUnit
        if let rir = rir {
            setLog.rir = rir
        }
        setLog.timestamp = timestamp
        setLog.isWarmup = isWarmup
        setLog.needsSync = needsSync

        return setLog
    }
}
