import Foundation

/// Model representing user information returned from the API
struct UserInfosModel: Codable {
    /// The user's unique identifier
    let id: String
    
    /// The user's email address
    let email: String
    
    /// The authentication token for API requests
    let token: String
    
    /// The user's first name (optional)
    let firstName: String?
    
    /// The user's last name (optional)
    let lastName: String?
    
    /// Coding keys for JSON serialization/deserialization
    enum CodingKeys: String, CodingKey {
        case id = "Id"
        case email = "Email"
        case token = "Token"
        case firstName = "FirstName"
        case lastName = "LastName"
    }
}
