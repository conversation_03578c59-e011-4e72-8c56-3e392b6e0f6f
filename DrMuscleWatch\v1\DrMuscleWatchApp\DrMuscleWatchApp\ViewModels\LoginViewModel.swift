import Foundation
import AuthenticationServices

/// View model for the login screen
class LoginViewModel: NSObject, ObservableObject {
    /// The authentication manager
    private let authManager: AuthenticationManager

    /// Flag indicating whether authentication is in progress
    @Published var isAuthenticating = false

    /// Error message if authentication fails
    @Published var errorMessage: String?

    /// Initializes the view model with an authentication manager
    init(authManager: AuthenticationManager = .shared) {
        self.authManager = authManager
    }

    /// Initiates the Sign in with Apple flow
    func signInWithApple() {
        isAuthenticating = true
        errorMessage = nil

        // Create the Apple ID request
        let request = ASAuthorizationAppleIDProvider().createRequest()
        request.requestedScopes = [.fullName, .email]

        // Create the authorization controller
        let controller = ASAuthorizationController(authorizationRequests: [request])
        controller.delegate = self
        controller.presentationContextProvider = self

        // Present the Sign in with Apple dialog
        controller.performRequests()
    }
}

// MARK: - ASAuthorizationControllerDelegate

extension LoginViewModel: ASAuthorizationControllerDelegate {
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        // Handle successful authorization
        if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
            authManager.handleSignInWithAppleCompletion(
                credential: appleIDCredential,
                error: nil
            ) { [weak self] result in
                DispatchQueue.main.async {
                    self?.isAuthenticating = false

                    switch result {
                    case .success:
                        // Authentication successful, no need to set error message
                        break
                    case .failure(let error):
                        self?.errorMessage = error.localizedDescription
                    }
                }
            }
        }
    }

    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        // Handle failed authorization
        DispatchQueue.main.async { [weak self] in
            self?.isAuthenticating = false
            self?.errorMessage = error.localizedDescription
        }
    }
}

// MARK: - ASAuthorizationControllerPresentationContextProviding

extension LoginViewModel: ASAuthorizationControllerPresentationContextProviding {
    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        // On watchOS, the presentation context is handled differently
        // The system automatically presents the authorization UI
        // This method is still required by the protocol

        // In a real implementation, we would need to handle this differently
        // For now, we return an empty anchor since the system will handle the presentation
        return ASPresentationAnchor()
    }
}
