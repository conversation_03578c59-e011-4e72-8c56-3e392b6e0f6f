import Foundation
import Combine

/// View model for the workout complete screen
class WorkoutCompleteViewModel: ObservableObject {
    /// The ID of the completed workout
    let workoutId: Int64

    /// The name of the completed workout
    let workoutName: String

    /// Whether the workout is being finished
    @Published private(set) var isFinishing = false

    /// The API client for syncing data with the server
    private let apiClient: DrMuscleAPIClient

    /// The storage service for local data operations
    private let storageService: StorageService

    /// The HealthKit service for workout tracking
    private let healthKitService: HealthKitServiceProtocol

    /// Initialize the view model
    /// - Parameters:
    ///   - workoutId: The ID of the completed workout
    ///   - workoutName: The name of the completed workout
    ///   - apiClient: The API client for syncing data with the server
    ///   - storageService: The storage service for local data operations
    ///   - healthKitService: The HealthKit service for workout tracking
    init(
        workoutId: Int64,
        workoutName: String,
        apiClient: DrMuscleAPIClient = DrMuscleAPIClient.shared,
        storageService: StorageService = StorageService.shared,
        healthKitService: HealthKitService = HealthKitService.shared
    ) {
        self.workoutId = workoutId
        self.workoutName = workoutName
        self.apiClient = apiClient
        self.storageService = storageService
        self.healthKitService = healthKitService
    }

    /// Finish the workout
    /// - Returns: Whether the workout was finished successfully
    @MainActor
    func finishWorkout() async -> Bool {
        isFinishing = true

        // Create a timestamp for the workout completion
        let timestamp = Date()

        // Mark the workout as completed in local storage
        do {
            try storageService.completeWorkout(id: workoutId, timestamp: timestamp)
        } catch {
            print("Error marking workout as completed: \(error.localizedDescription)")

            // Use the error handling service to show a banner
            ErrorHandlingService.shared.showError(
                message: "Failed to mark workout as completed. Please try again.",
                autoDismiss: true
            )

            isFinishing = false
            return false
        }

        // Get the workout start time from storage
        var startDate: Date?
        do {
            if let workout = try storageService.getWorkout(id: workoutId) {
                startDate = workout.timestamp
            }
        } catch {
            print("Error retrieving workout start time: \(error.localizedDescription)")
            // Continue anyway, as we can still end the workout without the start time
        }

        // End the HealthKit workout session
        let healthKitResult = await healthKitService.endWorkout()
        if !healthKitResult {
            print("Warning: Failed to end HealthKit workout session")
            // Continue anyway, as this is not critical
        }

        // Save the workout data to HealthKit if we have a start date
        if let startDate = startDate {
            let saveResult = await healthKitService.saveWorkoutData(
                startDate: startDate,
                endDate: timestamp,
                workoutName: workoutName
            )

            if !saveResult {
                print("Warning: Failed to save workout data to HealthKit")
                // Continue anyway, as this is not critical
            }
        }

        // Sync the workout completion with the server
        do {
            let saveModel = SaveWorkoutModel(workoutId: workoutId, timestamp: timestamp)
            let result = try await apiClient.saveWorkoutV3(model: saveModel)

            if !result.result {
                print("Warning: Server returned false for workout completion")
                // Continue anyway, as the data is saved locally and will be synced later

                // Show a non-blocking info message
                ErrorHandlingService.shared.showError(
                    message: "Workout saved locally. Will sync when online.",
                    autoDismiss: true
                )
            }
        } catch {
            print("Error syncing workout completion: \(error.localizedDescription)")
            // Continue anyway, as the data is saved locally and will be synced later

            // Show a non-blocking info message
            ErrorHandlingService.shared.showError(
                message: "Workout saved locally. Will sync when online.",
                autoDismiss: true
            )
        }

        isFinishing = false
        return true
    }
}
