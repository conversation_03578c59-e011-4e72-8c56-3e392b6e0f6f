import SwiftUI

/// AppTheme defines the visual styling for the Dr. Muscle Watch app
struct AppTheme {
    // MARK: - Colors
    
    // Base colors
    static let backgroundColor = Color.black
    static let primaryTextColor = Color.white
    static let secondaryTextColor = Color(UIColor.lightGray)
    static let accentBlueColor = Color(hex: "195377")
    static let greenColor = Color(hex: "5CD196")
    static let redColor = Color(hex: "BA1C31")
    
    // Yellow CTA button colors (gradient)
    static let yellowGradientStart = Color(hex: "DFE769") // Color(red: 223/255, green: 255/255, blue: 105/255)
    static let yellowGradientEnd = Color(hex: "E9FF97") // Color(red: 233/255, green: 255/255, blue: 151/255)
    
    // MARK: - Text Styles
    
    static let titleStyle = Font.system(size: 20, weight: .semibold)
    static let largeValueStyle = Font.system(size: 36, weight: .bold)
    static let bodyStyle = Font.system(size: 16)
    static let smallStyle = Font.system(size: 14)
    static let ctaButtonTextStyle = Font.system(size: 20, weight: .bold)
    
    // MARK: - Button Styles
    
    /// Primary yellow CTA button style
    static func primaryButtonStyle() -> some ButtonStyle {
        return YellowCTAButtonStyle()
    }
    
    /// Secondary button style (blue)
    static func secondaryButtonStyle() -> some ButtonStyle {
        return CustomButtonStyle(backgroundColor: accentBlueColor, foregroundColor: .white)
    }
    
    /// Tertiary button style (transparent with white text)
    static func tertiaryButtonStyle() -> some ButtonStyle {
        return CustomButtonStyle(backgroundColor: Color.clear, foregroundColor: .white)
    }
    
    // MARK: - Dimensions
    
    static let standardPadding: CGFloat = 8
    static let buttonHeight: CGFloat = 44
    static let cornerRadius: CGFloat = 30
}

// MARK: - Helper Extensions

/// Extension to create Color from hex string
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

// MARK: - Custom Button Styles

/// Standard button style with customizable colors
struct CustomButtonStyle: ButtonStyle {
    var backgroundColor: Color
    var foregroundColor: Color
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding()
            .background(backgroundColor)
            .foregroundColor(foregroundColor)
            .cornerRadius(AppTheme.cornerRadius)
            .scaleEffect(configuration.isPressed ? 0.95 : 1)
    }
}

/// Yellow CTA button style with gradient background
struct YellowCTAButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding()
            .foregroundColor(.black)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        AppTheme.yellowGradientStart,
                        AppTheme.yellowGradientEnd
                    ]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(AppTheme.cornerRadius)
            .scaleEffect(configuration.isPressed ? 0.95 : 1)
    }
}
