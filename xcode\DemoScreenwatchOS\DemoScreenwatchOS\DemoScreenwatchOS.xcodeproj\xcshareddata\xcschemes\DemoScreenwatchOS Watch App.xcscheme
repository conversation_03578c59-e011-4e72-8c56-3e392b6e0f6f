<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1400"
   version = "1.3">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "6EAD2CF82D2FC700005C2800"
               BuildableName = "DemoScreenwatchOS Watch App.app"
               BlueprintName = "DemoScreenwatchOS Watch App"
               ReferencedContainer = "container:DemoScreenwatchOS.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "6EAD2CF22D2FC700005C2800"
               BuildableName = "DemoScreenwatchOS.app"
               BlueprintName = "DemoScreenwatchOS"
               ReferencedContainer = "container:DemoScreenwatchOS.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "NO"
            buildForProfiling = "NO"
            buildForArchiving = "NO"
            buildForAnalyzing = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "6EAD2D0A2D2FC703005C2800"
               BuildableName = "DemoScreenwatchOS Watch AppTests.xctest"
               BlueprintName = "DemoScreenwatchOS Watch AppTests"
               ReferencedContainer = "container:DemoScreenwatchOS.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES">
      <Testables>
         <TestableReference
            skipped = "NO"
            parallelizable = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "6EAD2D0A2D2FC703005C2800"
               BuildableName = "DemoScreenwatchOS Watch AppTests.xctest"
               BlueprintName = "DemoScreenwatchOS Watch AppTests"
               ReferencedContainer = "container:DemoScreenwatchOS.xcodeproj">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "6EAD2D142D2FC703005C2800"
               BuildableName = "DemoScreenwatchOS Watch AppUITests.xctest"
               BlueprintName = "DemoScreenwatchOS Watch AppUITests"
               ReferencedContainer = "container:DemoScreenwatchOS.xcodeproj">
            </BuildableReference>
         </TestableReference>
      </Testables>
   </TestAction>
   <LaunchAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "6EAD2CF82D2FC700005C2800"
            BuildableName = "DemoScreenwatchOS Watch App.app"
            BlueprintName = "DemoScreenwatchOS Watch App"
            ReferencedContainer = "container:DemoScreenwatchOS.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Release"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "6EAD2CF82D2FC700005C2800"
            BuildableName = "DemoScreenwatchOS Watch App.app"
            BlueprintName = "DemoScreenwatchOS Watch App"
            ReferencedContainer = "container:DemoScreenwatchOS.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      customArchiveName = "DemoScreenwatchOS Watch App"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>
