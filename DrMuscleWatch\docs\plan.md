# Dr. Muscle Apple Watch App - Project Plan (V1)

## Purpose

This document outlines the high-level plan, features, and goals for the first version (V1) of the Dr. Muscle standalone Apple Watch application. The primary goal is to allow users to follow their Dr. Muscle workout plan, track progress, and receive guidance directly on their wrist, independent of their iPhone.

## Instructions

This file provides a high-level overview. Detailed specifications, user stories, and acceptance criteria (BDD Scenarios) will be elaborated within `docs/todo.md` as individual tasks are tackled. Refer to `docs/architecture.md` for technical design details and `docs/rules.md` for development workflow.

## Example Entry (Illustrative - Actual tasks in todo.md)

*   **Feature:** Workout Execution
*   **Goal:** Allow users to view and log sets for their planned workout.
*   **Key Aspects:** Display exercise name, reps/weight; allow input via tap-picker; save set data; handle rest timers.
*   **Reference:** `docs/todo.md` Task ID: FEAT-WORKOUT-EXEC

## Backlog Items (V1 Core Features - To be detailed in todo.md)

1.  **Authentication:** Secure user login via "Sign in with <PERSON>".
2.  **Workout List Display:** Fetch and display the user's workout plan for the day/upcoming.
3.  **Pre-Workout Screen:** Display selected workout name and list of exercises.
4.  **Workout Execution Core:** Navigate through exercises and sets based on the plan.
5.  **Set Screen UI:** Display Exercise Name, Target Reps, Target Weight.
6.  **Reps/Weight Input:** Allow users to modify reps/weight for a set using a tap-activated picker.
7.  **Set Logging:** Save completed set data (reps, weight).
8.  **RIR Capture:** Prompt for and save Reps In Reserve (RIR) after the first work set of each exercise using a picker.
9.  **Rest Timer:** Automatically start and display intra-set rest timers after saving a set (or RIR).
10. **Dynamic Save Button:** Display calculated performance percentage change on the "Save Set" button.
11. **Add Set Functionality:** Allow users to add an extra set after the last planned set of an exercise.
12. **Exercise Transition:** Handle moving from the last set of one exercise to the next, including the fixed 1-minute "Next Exercise" timer and checkmark animation.
13. **Workout Completion:** Allow users to formally end the workout session.
14. **Offline Capability:** Allow users to continue and complete an *already started* workout if connectivity is lost. Implement local storage for workout state and completed sets.
15. **Background Sync:** Automatically sync locally stored offline data to the backend API when connectivity is restored.
16. **HealthKit Integration:** Request authorization and track Heart Rate and Active Energy Burned during the workout session, saving data to HealthKit.
17. **API Integration:** Define and interact with backend API for all data fetching and saving operations.
18. **Basic Error Handling:** Display user-friendly messages for common errors (e.g., login failure, sync failure, API errors).