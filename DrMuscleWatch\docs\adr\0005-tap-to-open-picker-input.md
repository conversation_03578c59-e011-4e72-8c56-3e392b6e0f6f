# ADR-0005: Tap-to-Open Picker for Reps/Weight Input

*   **Status:** Accepted
*   **Date:** {{CURRENT_DATE}}
*   **Deciders:** Project Lead
*   **Consulted:** AI Assistant
*   **Informed:** Development Team

## Context and Problem Statement

Users need to be able to adjust the target repetitions and weight for a set directly on the Apple Watch before saving it. We need an input method that is reliable and usable during exercise, considering potential sweaty hands or movement.

## Decision Drivers

*   **Usability During Exercise:** Input method must be robust against accidental activation or input errors caused by movement.
*   **Clarity:** Easy for the user to understand how to change the values.
*   **Accuracy:** Allow precise selection of reps and weight.
*   **Simplicity:** Keep the V1 implementation focused.
*   **Leverage Alpha:** Utilize existing patterns if suitable.

## Considered Options

*   **Option 1: Tap-to-Open Picker:** Tapping the Reps or Weight value on the main screen presents a dedicated modal sheet with a scrollable picker (similar to alpha `ContentView.swift`).
    *   *Pros:* Explicit action required (less accidental input), provides large focused UI for selection, leverages alpha implementation pattern.
    *   *Cons:* Requires an extra tap, potentially slower for minor adjustments than the crown.
*   **Option 2: Digital Crown:** Use the Digital Crown to increment/decrement the focused value (Reps or Weight).
    *   *Pros:* Quick for small adjustments, feels native to watchOS.
    *   *Cons:* High risk of accidental input during exercise/handling equipment, less efficient for large value changes.
*   **Option 3: Stepper Buttons (+/-):** Display explicit plus/minus buttons next to the values.
    *   *Pros:* Clear interaction, less prone to accidental activation than crown.
    *   *Cons:* Consumes significant screen real estate, potentially cluttered UI.
*   **Option 4: Both Tap & Crown:** Allow both methods.
    *   *Pros:* Flexibility.
    *   *Cons:* Increased complexity, potential user confusion, harder to implement and test.

## Decision Outcome

**Chosen Option:** Tap-to-Open Picker (Option 1).

While the Digital Crown offers speed, the risk of accidental input during a workout is too high. The Tap-to-Open Picker provides a more deliberate and reliable interaction, reducing errors. It also aligns with the pattern used in the alpha version, potentially allowing some reuse of concepts or UI structure. The extra tap is an acceptable trade-off for improved reliability during exercise.

### Negative Consequences

*   Slightly slower input for users making very small, frequent adjustments compared to the Crown.

## Links

*   `docs/plan.md`
*   Reference: Alpha `ContentView.swift` picker implementation pattern.