import Foundation

/// Model for logging a completed set
struct WorkoutLogSerieModel: Codable {
    let exerciseId: Int64
    let reps: Int
    let weight: WeightModel
    let rir: Int?
    let workoutId: Int64?
    let isWarmup: Bool
    let timestamp: Date
    
    enum CodingKeys: String, CodingKey {
        case exerciseId = "ExerciseId"
        case reps = "Reps"
        case weight = "Weight"
        case rir = "RIR"
        case workoutId = "WorkoutId"
        case isWarmup = "IsWarmup"
        case timestamp = "Timestamp"
    }
}

/// Model for saving a completed workout
struct SaveWorkoutModel: Codable {
    let workoutId: Int64
    let timestamp: Date
    
    enum CodingKeys: String, CodingKey {
        case workoutId = "WorkoutId"
        case timestamp = "Timestamp"
    }
}
