import XCTest
@testable import DrMuscleWatchApp

class WorkoutCompleteViewModelTests: XCTestCase {

    // Mock API client
    class MockAPIClient: DrMuscleAPIClient {
        var saveWorkoutCalled = false
        var savedWorkoutId: Int64?
        var savedTimestamp: Date?

        override func saveWorkoutV3(model: SaveWorkoutModel) async throws -> BooleanModel {
            saveWorkoutCalled = true
            savedWorkoutId = model.workoutId
            savedTimestamp = model.timestamp
            return BooleanModel(result: true)
        }
    }

    // Mock storage service
    class MockStorageService: StorageService {
        var completeWorkoutCalled = false
        var completedWorkoutId: Int64?
        var completedTimestamp: Date?
        var getWorkoutCalled = false
        var requestedWorkoutId: Int64?
        var mockWorkout: Workout?

        override func completeWorkout(id: Int64, timestamp: Date) throws {
            completeWorkoutCalled = true
            completedWorkoutId = id
            completedTimestamp = timestamp
        }

        override func getWorkout(id: Int64) throws -> Workout? {
            getWorkoutCalled = true
            requestedWorkoutId = id
            return mockWorkout
        }
    }

    // Mock HealthKit service
    class MockHealthKitService: HealthKitServiceProtocol {
        var endWorkoutCalled = false
        var startWorkoutCalled = false
        var requestAuthorizationCalled = false
        var saveWorkoutDataCalled = false
        var savedStartDate: Date?
        var savedEndDate: Date?
        var savedWorkoutName: String?

        func requestAuthorization() async -> Bool {
            requestAuthorizationCalled = true
            return true
        }

        func startWorkout() async -> Bool {
            startWorkoutCalled = true
            return true
        }

        func endWorkout() async -> Bool {
            endWorkoutCalled = true
            return true
        }

        func saveWorkoutData(startDate: Date, endDate: Date, workoutName: String?) async -> Bool {
            saveWorkoutDataCalled = true
            savedStartDate = startDate
            savedEndDate = endDate
            savedWorkoutName = workoutName
            return true
        }
    }

    func testInitialization() {
        // Given
        let workoutId: Int64 = 123
        let workoutName = "Test Workout"

        // When
        let viewModel = WorkoutCompleteViewModel(workoutId: workoutId, workoutName: workoutName)

        // Then
        XCTAssertEqual(viewModel.workoutId, workoutId)
        XCTAssertEqual(viewModel.workoutName, workoutName)
        XCTAssertFalse(viewModel.isFinishing)
    }

    func testFinishWorkout() async {
        // Given
        let workoutId: Int64 = 123
        let workoutName = "Test Workout"
        let mockAPIClient = MockAPIClient()
        let mockStorageService = MockStorageService()
        let mockHealthKitService = MockHealthKitService()

        // Create a mock workout with a start time
        let startDate = Date().addingTimeInterval(-3600) // 1 hour ago
        let workout = Workout(context: PersistenceController.preview.container.viewContext)
        workout.id = workoutId
        workout.name = workoutName
        workout.timestamp = startDate
        mockStorageService.mockWorkout = workout

        let viewModel = WorkoutCompleteViewModel(
            workoutId: workoutId,
            workoutName: workoutName,
            apiClient: mockAPIClient,
            storageService: mockStorageService,
            healthKitService: mockHealthKitService
        )

        // When
        let result = await viewModel.finishWorkout()

        // Then
        XCTAssertTrue(result)
        XCTAssertTrue(mockStorageService.completeWorkoutCalled)
        XCTAssertEqual(mockStorageService.completedWorkoutId, workoutId)
        XCTAssertNotNil(mockStorageService.completedTimestamp)

        XCTAssertTrue(mockStorageService.getWorkoutCalled)
        XCTAssertEqual(mockStorageService.requestedWorkoutId, workoutId)

        XCTAssertTrue(mockAPIClient.saveWorkoutCalled)
        XCTAssertEqual(mockAPIClient.savedWorkoutId, workoutId)
        XCTAssertNotNil(mockAPIClient.savedTimestamp)

        XCTAssertTrue(mockHealthKitService.endWorkoutCalled)
        XCTAssertTrue(mockHealthKitService.saveWorkoutDataCalled)
        XCTAssertEqual(mockHealthKitService.savedStartDate, startDate)
        XCTAssertEqual(mockHealthKitService.savedEndDate, mockStorageService.completedTimestamp)
        XCTAssertEqual(mockHealthKitService.savedWorkoutName, workoutName)
    }

    func testFinishWorkoutHandlesStorageError() async {
        // Given
        let workoutId: Int64 = 123
        let workoutName = "Test Workout"
        let mockAPIClient = MockAPIClient()

        // Create a storage service that throws an error
        class ErrorStorageService: StorageService {
            override func completeWorkout(id: Int64, timestamp: Date) throws {
                throw NSError(domain: "TestError", code: 123, userInfo: nil)
            }
        }

        let mockHealthKitService = MockHealthKitService()

        let viewModel = WorkoutCompleteViewModel(
            workoutId: workoutId,
            workoutName: workoutName,
            apiClient: mockAPIClient,
            storageService: ErrorStorageService(),
            healthKitService: mockHealthKitService
        )

        // When
        let result = await viewModel.finishWorkout()

        // Then
        XCTAssertFalse(result)
        XCTAssertFalse(mockAPIClient.saveWorkoutCalled)
        XCTAssertFalse(mockHealthKitService.endWorkoutCalled)
    }

    func testFinishWorkoutHandlesAPIError() async {
        // Given
        let workoutId: Int64 = 123
        let workoutName = "Test Workout"

        // Create an API client that throws an error
        class ErrorAPIClient: DrMuscleAPIClient {
            override func saveWorkoutV3(model: SaveWorkoutModel) async throws -> BooleanModel {
                throw NSError(domain: "TestError", code: 123, userInfo: nil)
            }
        }

        let mockStorageService = MockStorageService()
        let mockHealthKitService = MockHealthKitService()

        // Create a mock workout with a start time
        let startDate = Date().addingTimeInterval(-3600) // 1 hour ago
        let workout = Workout(context: PersistenceController.preview.container.viewContext)
        workout.id = workoutId
        workout.name = workoutName
        workout.timestamp = startDate
        mockStorageService.mockWorkout = workout

        let viewModel = WorkoutCompleteViewModel(
            workoutId: workoutId,
            workoutName: workoutName,
            apiClient: ErrorAPIClient(),
            storageService: mockStorageService,
            healthKitService: mockHealthKitService
        )

        // When
        let result = await viewModel.finishWorkout()

        // Then
        XCTAssertTrue(result) // Should still return true since API errors are non-critical
        XCTAssertTrue(mockStorageService.completeWorkoutCalled)
        XCTAssertTrue(mockHealthKitService.endWorkoutCalled)
        XCTAssertTrue(mockHealthKitService.saveWorkoutDataCalled)
        XCTAssertEqual(mockHealthKitService.savedStartDate, startDate)
    }

    func testFinishWorkoutWithoutStartDate() async {
        // Given
        let workoutId: Int64 = 123
        let workoutName = "Test Workout"
        let mockAPIClient = MockAPIClient()
        let mockStorageService = MockStorageService()
        let mockHealthKitService = MockHealthKitService()

        // No mock workout, so getWorkout will return nil

        let viewModel = WorkoutCompleteViewModel(
            workoutId: workoutId,
            workoutName: workoutName,
            apiClient: mockAPIClient,
            storageService: mockStorageService,
            healthKitService: mockHealthKitService
        )

        // When
        let result = await viewModel.finishWorkout()

        // Then
        XCTAssertTrue(result)
        XCTAssertTrue(mockStorageService.completeWorkoutCalled)
        XCTAssertTrue(mockStorageService.getWorkoutCalled)
        XCTAssertTrue(mockHealthKitService.endWorkoutCalled)
        XCTAssertFalse(mockHealthKitService.saveWorkoutDataCalled, "Should not save workout data without a start date")
    }
}
