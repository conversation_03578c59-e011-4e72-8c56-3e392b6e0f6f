<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "78C31A58-6B7B-4447-B61F-EA93AE93F589"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "777EE4DA-AA42-4F4E-8771-2A8F91892EC3"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "DemoScreenwatchOS Watch App/DatabaseManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "71"
            endingLineNumber = "71"
            landmarkName = "saveExerciseSet(repsValue:weightValue:unitValue:nameValue:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "20C5E361-E1D0-446B-964B-0A53A22F906C"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "DemoScreenwatchOS Watch App/DatabaseManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "90"
            endingLineNumber = "90"
            landmarkName = "fetchExerciseSets()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "AE0B0F54-71E8-487F-B827-93888576DA57"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "DemoScreenwatchOS Watch App/DatabaseManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "72"
            endingLineNumber = "72"
            landmarkName = "saveExerciseSet(repsValue:weightValue:unitValue:nameValue:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "516BD5AA-2489-4086-93C6-23643397A357"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "DemoScreenwatchOS Watch App/ContentView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "204"
            endingLineNumber = "204"
            landmarkName = "saveExerciseSet()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "2AA89231-2A7E-4116-B9A9-89C0470650AE"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "DemoScreenwatchOS Watch App/DatabaseManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "53"
            endingLineNumber = "53"
            landmarkName = "deleteAllExerciseSets()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "551BCFFF-EE72-491D-91F4-0B872890102B"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "DemoScreenwatchOS Watch App/DatabaseManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "52"
            endingLineNumber = "52"
            landmarkName = "deleteAllExerciseSets()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
