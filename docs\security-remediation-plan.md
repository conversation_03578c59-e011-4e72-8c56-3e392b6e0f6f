# Security Remediation Plan for Dr. Muscle Repository

## Critical Security Issues Identified

During a security audit of the Dr. Muscle repository, the following critical security issues were identified:

1. **Exposed iOS Certificates and Provisioning Profiles**:
   - iOS certificates (`.p12`, `.cer`) and provisioning profiles (`.mobileprovision`) are stored directly in the repository
   - These files contain sensitive cryptographic material that should never be committed to a repository

2. **Insecure Handling of Secrets in GitHub Actions Workflows**:
   - Secrets are being printed to logs (even with character spacing)
   - Secrets are being decoded and saved to disk without proper cleanup
   - Command-line arguments contain sensitive information that could be visible in logs

3. **Hardcoded Credentials**:
   - Keystore passwords and other credentials are passed directly as command-line arguments
   - Multiple copies of the same secrets are created during workflow execution

## Immediate Action Items

### 1. Remove Sensitive Files from Repository (URGENT)

- [ ] **Remove iOS Certificate Files**:
  ```bash
  git rm --cached iOS_Certificates/DrCertificates.p12
  git rm --cached iOS_Certificates/ios_development.cer
  git rm --cached iOS_Certificates/2024_06_10_Jignesh.mobileprovision
  ```

- [ ] **Clean Git History**:
  ```bash
  # Use BFG Repo-Cleaner to remove sensitive files from history
  java -jar bfg.jar --delete-files "*.p12" --delete-files "*.cer" --delete-files "*.mobileprovision" /path/to/repo
  git reflog expire --expire=now --all
  git gc --prune=now --aggressive
  ```

- [ ] **Revoke Compromised Certificates**:
  - Log in to Apple Developer Portal
  - Revoke the exposed certificates
  - Generate new certificates and provisioning profiles

### 2. Update GitHub Secrets Management

- [ ] **Add Missing Secrets to GitHub**:
  - Add all required secrets to GitHub repository settings
  - Ensure no secrets are hardcoded in workflow files

- [ ] **Update Workflow Files**:
  - Remove any code that prints secrets to logs
  - Use environment files instead of command-line arguments for tools that require credentials
  - Add cleanup steps to securely delete any sensitive files created during workflow execution

### 3. Implement Preventive Measures

- [ ] **Update .gitignore**:
  ```bash
  # Add to .gitignore
  *.p12
  *.mobileprovision
  *.cer
  *.key
  *.keystore
  *.jks
  google-services.json
  service-account.json
  ```

- [ ] **Set Up Git Hooks**:
  - Create pre-commit hooks to prevent committing sensitive files
  - Example hook to detect sensitive files:
    ```bash
    #!/bin/bash
    
    SENSITIVE_PATTERNS=("*.p12" "*.mobileprovision" "*.cer" "*.key" "*.keystore" "password" "secret")
    
    for pattern in "${SENSITIVE_PATTERNS[@]}"; do
      if git diff --cached --name-only | grep -E "$pattern"; then
        echo "ERROR: Potential sensitive data detected in commit."
        echo "Please remove sensitive files before committing."
        exit 1
      fi
    done
    ```

- [ ] **Implement Repository Security Scanning**:
  - Set up automated scanning for secrets in code
  - Consider tools like GitGuardian, Gitleaks, or GitHub Advanced Security

## Detailed Remediation Steps

### 1. Fix GitHub Workflow Security Issues

#### 1.1 Update `.github/workflows/dotnet.yml`

- [ ] **Remove Secret Echoing**:
  - Remove lines that echo `SENTRY_AUTH_TOKEN` or other secrets
  - Replace with safer verification methods

- [ ] **Secure Certificate Handling**:
  - Use ephemeral storage for certificates
  - Add cleanup steps to remove sensitive files
  - Consider using GitHub's built-in mechanisms for handling certificates

- [ ] **Secure Keystore Handling**:
  - Use environment files for keystore passwords
  - Avoid passing secrets as command-line arguments

#### 1.2 Implement Secure Provisioning Profile Management

- [ ] **Use GitHub Secrets for Provisioning Profiles**:
  - Store provisioning profiles as base64-encoded secrets
  - Decode only when needed and in memory-based filesystems when possible
  - Clean up after use

- [ ] **Update Apple Provisioning Profile Action**:
  ```yaml
  - name: Setup Provisioning Profile
    run: |
      echo "${{ secrets.PROVISIONING_PROFILE_BASE64 }}" | base64 --decode > /tmp/profile.mobileprovision
      mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles/
      cp /tmp/profile.mobileprovision ~/Library/MobileDevice/Provisioning\ Profiles/
      rm /tmp/profile.mobileprovision
  ```

### 2. Implement Secure Certificate Management

- [ ] **Create New Certificates**:
  - Generate new development and distribution certificates
  - Create new provisioning profiles
  - Update all affected devices and development environments

- [ ] **Document Secure Certificate Handling**:
  - Create documentation for team on how to securely handle certificates
  - Establish procedures for certificate rotation

### 3. Secure Android Build Process

- [ ] **Update Android Keystore Management**:
  - Store keystore as a GitHub secret
  - Implement secure keystore handling in workflows

- [ ] **Secure Google Play Deployment**:
  - Store Google service account JSON as a GitHub secret
  - Implement secure handling of service account credentials

## Long-term Security Improvements

### 1. Implement Security Training

- [ ] **Developer Security Training**:
  - Conduct training sessions on secure handling of certificates and credentials
  - Create security guidelines for the development team

### 2. Implement Continuous Security Monitoring

- [ ] **Set Up Automated Security Scanning**:
  - Implement regular security scans of the codebase
  - Set up alerts for potential security issues

### 3. Implement Secure CI/CD Practices

- [ ] **Use OIDC for GitHub Actions**:
  - Implement OpenID Connect for GitHub Actions to avoid long-lived credentials
  - Configure cloud providers to trust GitHub Actions OIDC tokens

- [ ] **Implement Environment Protection Rules**:
  - Set up environment protection rules for production deployments
  - Require approvals for sensitive workflows

## Verification Checklist

After implementing the remediation steps, verify the following:

- [ ] No sensitive files in the repository or its history
- [ ] All secrets properly stored in GitHub Secrets
- [ ] Workflow files updated to handle secrets securely
- [ ] New certificates and provisioning profiles created and working
- [ ] .gitignore updated to prevent future issues
- [ ] Git hooks implemented to catch sensitive files
- [ ] Security scanning tools implemented
- [ ] Team trained on secure handling of certificates and credentials

## Timeline

- **Immediate (24 hours)**:
  - Remove sensitive files from repository
  - Clean Git history
  - Revoke compromised certificates

- **Short-term (1 week)**:
  - Update GitHub Secrets
  - Update workflow files
  - Implement preventive measures

- **Medium-term (1 month)**:
  - Implement security training
  - Set up automated security scanning

- **Long-term (ongoing)**:
  - Regular security audits
  - Continuous improvement of security practices

## Resources

- [GitHub Secrets Documentation](https://docs.github.com/en/actions/security-guides/encrypted-secrets)
- [Apple Developer Certificate Management](https://developer.apple.com/support/certificates/)
- [BFG Repo-Cleaner](https://rtyley.github.io/bfg-repo-cleaner/)
- [Git-filter-repo](https://github.com/newren/git-filter-repo)
- [GitHub Security Best Practices](https://docs.github.com/en/code-security/getting-started/github-security-best-practices)
