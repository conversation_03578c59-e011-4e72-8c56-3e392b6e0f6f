# ADR-{NUMBER}: {TITLE}

*   **Status:** {Proposed | Accepted | Deprecated | Superseded} <!-- Choose one -->
*   **Date:** {YYYY-MM-DD}
*   **Deciders:** {List of names}
*   **Consulted:** {List of names}
*   **Informed:** {List of names}

## Context and Problem Statement

{Describe the context and problem that necessitates this decision. What is the issue we're seeing that needs addressing? What are the constraints? What assumptions are we making?}

## Decision Drivers

{List the forces driving this decision. Examples: performance, cost, usability, maintainability, security, specific user requirement, technical limitation.}

*   Driver 1
*   Driver 2

## Considered Options

{List the options that were considered.}

*   **Option 1:** {Description}
    *   *Pros:* {Advantages}
    *   *Cons:* {Disadvantages}
*   **Option 2:** {Description}
    *   *Pros:* {Advantages}
    *   *Cons:* {Disadvantages}
*   **Option X:** ...

## Decision Outcome

**Chosen Option:** {Option Name}, because {justification}.

{Elaborate on the chosen option, why it was selected over others, and any positive consequences anticipated.}

### Negative Consequences

{Describe any known negative consequences or trade-offs associated with the chosen option.}

## Implementation Plan (Optional)

{High-level steps or considerations for implementing this decision.}

*   Step 1
*   Step 2

## Validation (Optional)

{How will we know if this decision was successful?}

## Links (Optional)

*   {Link to related documents, issues, discussions}