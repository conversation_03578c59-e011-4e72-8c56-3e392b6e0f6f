import Foundation
import SQLite

// MARK: - Database Manager Class

/// `DatabaseManager` is responsible for handling SQLite database operations
/// related to managing exercise sets in the app.
class DatabaseManager {
    // MARK: - Private Properties
    
    private var db: Connection!              // Database connection object
    private var exerciseSets: Table!         // Table object for "exerciseSets"
    
    // Define columns for the table
    private var id: Expression<Int64>!       // Column for unique ID (Primary Key)
    private var reps: Expression<Int>!       // Column for number of reps in an exercise set
    private var weight: Expression<Int>!     // Column for weight used in the exercise set
    private var unit: Expression<String>!    // Column for unit of weight (e.g., kg, lbs)
    private var name: Expression<String>!    // Column for exercise name
    private var setNumber: Expression<Int>!  // Column for set number (e.g., Set 1, Set 2)


    // MARK: - Initialization
    
    /// Initializes the `DatabaseManager` and sets up the SQLite database.
    /// It also ensures that the required table `exerciseSets` exists.
    init() {
        do {
            // Retrieve the file URL for the app's document directory to store the database file
            let dbFileUrl = try FileManager.default.url(for: .documentDirectory, in: .userDomainMask, appropriateFor: nil, create: false).appendingPathComponent("exercise.db")
            
            // Establish a connection to the SQLite database. The file will be created if it doesn't exist.
            db = try Connection(dbFileUrl.path)
            
            // Define the table schema
            exerciseSets = Table("exerciseSets")
            id = Expression<Int64>("id")
            name = Expression<String>("name")
            reps = Expression<Int>("reps")
            weight = Expression<Int>("weight")
            unit = Expression<String>("unit")

            // Try to create the table if it doesn't already exist
            try db.run(exerciseSets.create(ifNotExists: true) { t in
                t.column(id, primaryKey: .autoincrement)  // Primary key
                t.column(name)                            // Exercise name
                t.column(reps)                            // Reps column
                t.column(weight)                          // Weight column
                t.column(unit)                            // Unit column
            })
            
            // The table is now created, including the setNumber column if it was missing.
        } catch {
            print("Database setup error: \(error)")
        }
    }

    // MARK: - CRUD Operations

    // MARK: - Delete All Exercise Sets

    /// Deletes all exercise sets from the database.
    func deleteAllExerciseSets() {
        do {
            let deleteQuery = exerciseSets.delete() // Delete all records from the table
            try db.run(deleteQuery)  // Execute the delete query
            print("All exercise sets deleted successfully.")
        } catch {
            print("Failed to delete exercise sets: \(error)")
        }
    }


    
    // MARK: - Save Exercise Set

      /// Saves a new exercise set to the database.
      /// - Parameters:
      ///   - repsValue: The number of repetitions.
      ///   - weightValue: The weight used in the exercise.
      ///   - unitValue: The unit of the weight (e.g., kg, lbs).
      ///   - nameValue: The name of the exercise.
    func saveExerciseSet(repsValue: Int, weightValue: Int, unitValue: String, nameValue: String) {
        do {
            let insert = exerciseSets.insert(
                reps <- repsValue,   //Binding OperatorIn SQLite.swift, the <- operator is used to                    bind values to the columns in the database table in a type-safe manner.
                weight <- weightValue,
                unit <- unitValue,
                name <- nameValue
            )
            try db.run(insert)
            print("Exercise set saved successfully.")
        } catch {
            print("Failed to insert exercise set: \(error)")
        }
    }

    
    // MARK: - Fetch Exercise Sets

      /// Fetches all exercise sets stored in the database.
      /// - Returns: An array of `ExerciseSet` objects representing all the data in the table.
    func fetchExerciseSets() -> [ExerciseSet] {
        var sets = [ExerciseSet]()
        
        do {
                    for set in try db.prepare(exerciseSets) {
                        let exerciseSet = ExerciseSet(
                            id: set[id],              // Fetching ID
                            reps: set[reps],          // Fetching reps value
                            weight: set[weight],      // Fetching weight value
                            unit: set[unit],          // Fetching unit value
                            name: set[name],          // Fetching exercise name
                            setNumber: set[setNumber] // Fetching set number
                        
                        )
                        sets.append(exerciseSet)  // Append to the list of sets
                    }
        } catch {
            print("Failed to fetch exercise sets: \(error)")
        }
        
        return sets
    }
}
