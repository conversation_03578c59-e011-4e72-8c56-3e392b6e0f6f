import Foundation

/// Model for historical workout data
struct HistoryModel: Codable {
    let id: Int64
    let workoutDate: Date
    let exercises: [HistoryExerciseModel]
    
    enum CodingKeys: String, CodingKey {
        case id = "Id"
        case workoutDate = "WorkoutDate"
        case exercises = "Exercises"
    }
}

/// Model for historical exercise data
struct HistoryExerciseModel: Codable {
    let exercise: ExerciseModel
    let sets: [WorkoutLogSerieModel]
    let series: Int
    let reps: Int
    let bestSerie1RM: WeightModel?
    
    enum CodingKeys: String, CodingKey {
        case exercise = "Exercise"
        case sets = "Sets"
        case series = "Series"
        case reps = "Reps"
        case bestSerie1RM = "BestSerie1RM"
    }
}

/// Model for requesting historical data for an exercise
struct GetUserWorkoutLogAverageForExerciseRequest: Codable {
    let exerciseId: Int64?
    
    enum CodingKeys: String, CodingKey {
        case exerciseId = "ExerciseId"
    }
}
