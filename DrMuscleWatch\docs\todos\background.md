# Dr. Muscle Watch App - Background & Integration Tasks (V1)

## Purpose

This file lists the development tasks related to background processes, external integrations (HealthKit), offline handling, and error display for the V1 Dr. Muscle Apple Watch app.

## Instructions

*   Mark tasks as "Pending", "In Progress", or "Completed".
*   Reference related ADRs or specific requirements where applicable.
*   Follow the TDD cycle (Red, Green, Refactor, Documentation) for each scenario within a task, as defined in `docs/rules.md`.
*   For API-related tasks, refer to `docs/api-reference.md` for detailed endpoint specifications, data models, and implementation patterns.
*   For UI-related tasks, refer to `docs/ui-components.md` for detailed component documentation, styling guidelines, and implementation patterns. The actual UI components can be found in `v1/DrMuscleWatchApp/Components/` and views in `v1/DrMuscleWatchApp/Views/`.

## Background & Integration Tasks

*   **Task ID:** OFFLINE-01
*   **Status:** Completed
*   **Task:** Ensure workout can continue if connection lost after starting (rely on local storage).
    *   **Reference:** See `docs/api-reference.md` for offline sync implementation details.
    *   **Acceptance Criteria:**
        *   **Scenario:** Start Workout Requires Connection
            *   Given the user is not currently in an active workout session
            *   And the watch has no network connectivity
            *   When the user attempts to start a workout from the Pre-Workout screen
            *   Then the app displays an error message (e.g., "Network connection required to start workout.").
            *   And the workout does not start.
            *   And the workout data is not downloaded or stored locally yet.
        *   **Scenario:** Lose Connection Mid-Workout (Set Screen)
            *   Given the user has successfully started a workout with network connectivity
            *   And the workout plan is stored locally
            *   And the user is currently on the Set Screen for an exercise
            *   And the watch loses network connectivity
            *   When the user modifies reps or weight using the pickers
            *   Then the UI updates correctly based on local data.
            *   When the user taps "Save Set"
            *   Then the set data (reps, weight, RIR if applicable) is saved successfully to *local* storage (Core Data).
            *   And the data is flagged as `needsSync = true`.
            *   And the app proceeds to the next state (RIR prompt or Rest Timer) based on local data.
        *   **Scenario:** Lose Connection Mid-Workout (Rest Timer)
            *   Given the user is in an active workout stored locally
            *   And the user is currently viewing the Rest Timer countdown
            *   And the watch loses network connectivity
            *   When the timer completes or is skipped
            *   Then the app transitions correctly to the next Set Screen based on the locally stored workout plan.
        *   **Scenario:** Lose Connection Mid-Workout (Add Set Choice)
            *   Given the user is in an active workout stored locally
            *   And the user is viewing the "Add Set" / "Next Exercise" choice screen
            *   And the watch loses network connectivity
            *   When the user taps "Add Set"
            *   Then the app correctly transitions back to the Set Screen based on local state.
            *   When the user taps "Next Exercise" (or lets timer expire and taps)
            *   Then the app correctly transitions to the next exercise or Workout Complete screen based on the locally stored plan.
        *   **Scenario:** Complete Workout Offline
            *   Given the user is in an active workout stored locally
            *   And the watch has no network connectivity
            *   When the user completes the final exercise and navigates to the Workout Complete screen
            *   Then the Workout Complete screen is displayed.
            *   When the user taps "Finish" / "Done"
            *   Then the workout is marked as complete in *local* storage.
            *   And the HealthKit session is ended and saved locally (Task HK-03).
            *   And the completion status is flagged as `needsSync = true`.
            *   And the user is navigated back to the main view.
    *   **Depends On:** STORAGE-01, WKOUT-DETAIL-01 (for local storage of plan), LOGGING-01, RIR-02, TIMER-01, ADDSET-01, TRANSITION-01, WKOUT-COMP-01

*   **Task ID:** SYNC-01
*   **Status:** Completed
*   **Task:** Implement background sync service to push locally saved data to API when online.
    *   **Reference:** See `docs/api-reference.md` for offline sync implementation details and the `SyncService` structure.
    *   **Acceptance Criteria:**
        *   **Scenario:** Sync Triggered on Regaining Connection
            *   Given the app has locally stored data flagged as `needsSync = true` (e.g., completed sets, workout completion status)
            *   And the watch was previously offline
            *   When the watch regains network connectivity
            *   Then the sync service detects connectivity.
            *   And the sync service attempts to send the unsynced data to the appropriate backend API endpoints.
        *   **Scenario:** Sync Triggered on App Launch
            *   Given the app has locally stored data flagged as `needsSync = true`
            *   When the app is launched
            *   And the watch has network connectivity
            *   Then the sync service attempts to send the unsynced data to the backend API.
        *   **Scenario:** Successful Sync Updates Local Flag
            *   Given the sync service is sending unsynced data (e.g., a SetLog) to the API
            *   When the API endpoint returns a successful response for that data item
            *   Then the corresponding local record's `needsSync` flag is updated to `false`.
            *   And the local record is *not* deleted (it remains for history/cache unless explicitly pruned later).
        *   **Scenario:** Sync Handles API Errors Gracefully
            *   Given the sync service is sending unsynced data to the API
            *   When the API endpoint returns an error (e.g., server error, validation error)
            *   Then the sync service logs the error.
            *   And the corresponding local record's `needsSync` flag remains `true`.
            *   And the sync service will retry sending this data later (e.g., on next connection or app launch).
            *   And multiple failures might trigger a user notification (Optional - see ERROR-01).
        *   **Scenario:** Sync Sends Data in Order (If Necessary)
            *   Given multiple sets and a workout completion status are queued for sync
            *   When the sync service sends the data
            *   Then it sends the data in a logical order (e.g., sets first, then workout completion) if required by the backend API design. *(Dependency: Backend API requirements)*.
    *   **Depends On:** API-01, STORAGE-01, OFFLINE-01

*   **Task ID:** HK-01
*   **Status:** Completed
*   **Task:** Implement HealthKit authorization request flow.
*   **Completion Date:** May 15, 2024
*   **Implementation Notes:** Made HealthKitService conform to HealthKitServiceProtocol for better testability. Updated HealthKitService to handle authorization requests properly. Created HealthKitServiceTests to test the authorization flow. Updated WorkoutDetailViewModel and WorkoutCompleteViewModel to use the HealthKitServiceProtocol. Enhanced error handling in WorkoutDetailViewModel to continue even if HealthKit authorization is denied.
    *   **Acceptance Criteria:**
        *   **Scenario:** Request Authorization on First Workout Start
            *   Given the user has *not* previously granted HealthKit permissions to the app
            *   When the user taps "Start Workout" for the first time
            *   Then the app requests authorization from HealthKit for `HKObjectType.workoutType()` and data types for Heart Rate and Active Energy Burned.
            *   And the native HealthKit authorization sheet is displayed.
        *   **Scenario:** Authorization Granted
            *   Given the HealthKit authorization sheet is displayed
            *   When the user grants permission for the requested types
            *   Then the authorization status is recorded by the app.
            *   And the workout session proceeds to start (Task HK-02).
        *   **Scenario:** Authorization Denied
            *   Given the HealthKit authorization sheet is displayed
            *   When the user denies permission for the requested types
            *   Then the authorization status is recorded by the app.
            *   And the workout session *still* proceeds to start, but *without* HealthKit tracking enabled.
            *   And subsequent attempts to start workouts do *not* re-prompt for authorization unless permissions are manually changed in Settings.
        *   **Scenario:** Authorization Already Determined
            *   Given the user *has* previously granted or denied HealthKit permissions
            *   When the user taps "Start Workout"
            *   Then the app checks the current authorization status.
            *   And the HealthKit authorization sheet is *not* displayed again.
            *   And the workout session proceeds (with or without HealthKit tracking based on the determined status).
    *   **Depends On:** WKOUT-DETAIL-01
    *   **ADR:** `docs/adr/0004-healthkit-integration-v1.md`

*   **Task ID:** HK-02
*   **Status:** Completed
*   **Task:** Implement HKWorkoutSession management (start, stop).
*   **Completion Date:** May 15, 2024
*   **Implementation Notes:** Created comprehensive tests for HKWorkoutSession management. Extended MockHKHealthStore to support workout session testing. Added MockHKWorkoutSession and MockHKWorkoutBuilder classes for testing. Updated HealthKitService to work with mock objects. Implemented proper workout session lifecycle management (start, stop). Added error handling for session creation, collection, and finishing. Ensured proper cleanup of session resources even when errors occur. Refactored the implementation to improve maintainability by breaking down the startWorkout and endWorkout methods into smaller, more focused helper methods. Added detailed documentation for each method to explain its purpose and behavior.
    *   **Acceptance Criteria:**
        *   **Scenario:** Start Workout Session on Workout Start
            *   Given the user taps "Start Workout"
            *   And HealthKit authorization has been granted (or denied, status known)
            *   When the workout begins in the app
            *   Then an `HKWorkoutSession` is created with the appropriate activity type (e.g., `.traditionalStrengthTraining`).
            *   And the session is started.
            *   And if permissions were granted, associated queries for Heart Rate and Active Energy are also started.
        *   **Scenario:** End Workout Session on Workout Finish
            *   Given an `HKWorkoutSession` is active for the current workout
            *   When the user taps "Finish" / "Done" on the Workout Complete screen
            *   Then the `HKWorkoutSession` is ended.
            *   And associated HealthKit queries are stopped.
            *   And the session data is prepared for saving (Task HK-03).
        *   **Scenario:** Handle App Termination During Workout (Graceful Failure)
            *   Given an `HKWorkoutSession` is active
            *   When the app terminates unexpectedly (e.g., crashes, system kills it)
            *   Then the `HKWorkoutSession` might be left dangling by the system. *(Recovery/cleanup might be complex and potentially out of scope for V1, but acknowledge the possibility)*. The primary goal is saving data on *normal* completion.
    *   **Depends On:** HK-01, WKOUT-DETAIL-01, WKOUT-COMP-01

*   **Task ID:** HK-03
*   **Status:** Completed
*   **Task:** Save workout data (Energy Burned, Heart Rate) to HealthKit upon workout completion.
*   **Completion Date:** May 16, 2024
*   **Implementation Notes:** Updated HealthKitServiceProtocol to include saveWorkoutData method. Implemented saveWorkoutData in HealthKitService with energy burned calculation based on workout duration. Added metadata with workout name and brand. Updated WorkoutCompleteViewModel to retrieve workout start time and save workout data to HealthKit. Added comprehensive error handling and tests for various scenarios.
    *   **Acceptance Criteria:**
        *   **Scenario:** Save Workout to HealthKit with Data (Permission Granted)
            *   Given an `HKWorkoutSession` has been successfully started and ended
            *   And HealthKit permissions for HR and Energy were granted
            *   And associated queries collected HR samples and calculated total energy burned
            *   When the session ends (triggered by workout completion in app)
            *   Then the app saves the `HKWorkout` record to the HealthKit store.
            *   And the record includes the correct start time, end time, duration, total energy burned, and activity type.
            *   And associated `HKQuantitySample`s for heart rate are saved and linked to the workout.
        *   **Scenario:** Save Workout to HealthKit without Data (Permission Denied)
            *   Given an `HKWorkoutSession` has been successfully started and ended
            *   And HealthKit permissions for HR and Energy were *denied*
            *   When the session ends
            *   Then the app saves the `HKWorkout` record to the HealthKit store.
            *   And the record includes the correct start time, end time, duration, and activity type.
            *   And the total energy burned field might be nil or zero.
            *   And no heart rate samples are saved.
        *   **Scenario:** Handle HealthKit Save Errors
            *   Given the app attempts to save the `HKWorkout` record and associated samples
            *   When an error occurs during the save operation (e.g., HealthKit unavailable, validation error)
            *   Then the error is logged by the app.
            *   And the user might be informed via a non-critical error message (Optional - see ERROR-01).
            *   *(Retrying HealthKit saves automatically can be complex; V1 might accept that a save failure means the data doesn't get into HealthKit for that session)*.
    *   **Depends On:** HK-02

*   **Task ID:** ERROR-01
*   **Status:** Completed
*   **Task:** Implement basic UI feedback for common errors (API, Sync, Login).
*   **Completion Date:** May 17, 2024
*   **Implementation Notes:** Created ErrorHandlingService with methods for showing, dismissing, and handling different types of errors. Implemented ErrorBannerView component with retry action and auto-dismiss functionality. Added ErrorBannerModifier and View extension for easy integration with any view. Updated all view models to use the error handling service. Added comprehensive tests for the error handling components.
    *   **Acceptance Criteria:**
        *   **Scenario:** Display Login Error
            *   Given the user encounters an error during the "Sign in with Apple" flow (Apple or Backend error)
            *   When the error occurs
            *   Then a user-friendly, non-technical error message is displayed clearly on the login screen (e.g., using a temporary banner or alert).
        *   **Scenario:** Display Workout List Load Error
            *   Given the app encounters an error fetching the workout list from the API
            *   When the error occurs
            *   Then a user-friendly error message is displayed on the Workout List screen.
        *   **Scenario:** Display Workout Detail Load Error
            *   Given the app encounters an error fetching workout details from the API
            *   When the error occurs
            *   Then a user-friendly error message is displayed on the Pre-Workout screen.
        *   **Scenario:** Indicate Sync Issues (Subtle)
            *   Given the background sync service fails to sync data after multiple retries
            *   When such a persistent failure occurs
            *   Then a subtle indicator might appear in the UI (e.g., a small icon, a message in settings) indicating unsynced data. *(Avoid disruptive alerts for background sync failures unless critical)*.
        *   **Scenario:** Indicate HealthKit Save Error (Subtle)
            *   Given the app fails to save workout data to HealthKit
            *   When the error occurs
            *   Then a subtle, non-blocking notification or log entry occurs. *(Avoid alarming the user unless data loss is significant and unrecoverable)*.
    *   **Depends On:** AUTH-01, API-01, SYNC-01, HK-03