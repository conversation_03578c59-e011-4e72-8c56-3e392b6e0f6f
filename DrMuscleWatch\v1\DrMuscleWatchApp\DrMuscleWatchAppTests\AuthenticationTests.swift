import XCTest
import AuthenticationServices
@testable import DrMuscleWatchApp

final class AuthenticationTests: XCTestCase {
    
    // Test that the login view displays the Sign in with Apple button
    func testLoginViewDisplaysSignInWithAppleButton() {
        // Given the user is not authenticated
        let loginView = LoginView()
        
        // When the view is loaded
        let viewHierarchy = loginView.body
        
        // Then the Sign in with Apple button should be displayed
        // Note: This is a basic test to ensure the view compiles and contains a button
        // In a real test environment, we would use ViewInspector or similar to verify the button exists
        XCTAssertNotNil(viewHierarchy)
    }
    
    // Test that tapping the Sign in with Apple button initiates the sign-in process
    func testTappingSignInWithAppleButtonInitiatesSignIn() {
        // Given the user is on the login screen
        let authManager = AuthenticationManager.shared
        let loginViewModel = LoginViewModel(authManager: authManager)
        
        // When the user taps the Sign in with Apple button
        // (Simulated by directly calling the signInWithApple method)
        loginViewModel.signInWithApple()
        
        // Then the sign-in process should be initiated
        // (Indicated by the isAuthenticating flag being set to true)
        XCTAssertTrue(loginViewModel.isAuthenticating)
    }
    
    // Test that the authentication manager can handle a successful sign-in
    func testAuthenticationManagerHandlesSuccessfulSignIn() {
        // Given an authentication manager and a mock Apple ID credential
        let authManager = AuthenticationManager.shared
        let mockCredential = MockAppleIDCredential(
            user: "test_user_id",
            identityToken: "test_identity_token".data(using: .utf8)!
        )
        
        // Create an expectation for the async operation
        let expectation = XCTestExpectation(description: "Handle successful sign-in")
        
        // When the authentication manager handles a successful sign-in
        authManager.handleSignInWithAppleCompletion(
            credential: mockCredential,
            error: nil
        ) { result in
            // Then the result should be success
            switch result {
            case .success:
                expectation.fulfill()
            case .failure:
                XCTFail("Sign-in should succeed with valid credential")
            }
        }
        
        wait(for: [expectation], timeout: 1.0)
    }
    
    // Test that the authentication manager can handle a failed sign-in
    func testAuthenticationManagerHandlesFailedSignIn() {
        // Given an authentication manager and an error
        let authManager = AuthenticationManager.shared
        let mockError = NSError(domain: "com.drmuscle.watch", code: 1, userInfo: [NSLocalizedDescriptionKey: "Sign-in failed"])
        
        // Create an expectation for the async operation
        let expectation = XCTestExpectation(description: "Handle failed sign-in")
        
        // When the authentication manager handles a failed sign-in
        authManager.handleSignInWithAppleCompletion(
            credential: nil,
            error: mockError
        ) { result in
            // Then the result should be failure
            switch result {
            case .success:
                XCTFail("Sign-in should fail with error")
            case .failure(let error):
                XCTAssertEqual(error.localizedDescription, "Sign-in failed")
                expectation.fulfill()
            }
        }
        
        wait(for: [expectation], timeout: 1.0)
    }
    
    // Test that the authentication manager can store and retrieve the authentication state
    func testAuthenticationManagerStoresAndRetrievesAuthState() {
        // Given an authentication manager and a user info model
        let authManager = AuthenticationManager.shared
        let userInfo = UserInfosModel(
            id: "test_user_id",
            email: "<EMAIL>",
            token: "test_token",
            firstName: "Test",
            lastName: "User"
        )
        
        // When the authentication manager stores the authentication state
        authManager.storeAuthState(userInfo: userInfo)
        
        // Then the authentication state should be retrievable
        XCTAssertTrue(authManager.isAuthenticated)
        XCTAssertEqual(authManager.currentUser?.id, "test_user_id")
        XCTAssertEqual(authManager.currentUser?.email, "<EMAIL>")
        
        // Clean up
        authManager.clearAuthState()
        XCTAssertFalse(authManager.isAuthenticated)
        XCTAssertNil(authManager.currentUser)
    }
}

// Mock Apple ID credential for testing
class MockAppleIDCredential: ASAuthorizationAppleIDCredential {
    private let mockUser: String
    private let mockIdentityToken: Data
    
    init(user: String, identityToken: Data) {
        self.mockUser = user
        self.mockIdentityToken = identityToken
        super.init()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override var user: String {
        return mockUser
    }
    
    override var identityToken: Data? {
        return mockIdentityToken
    }
}
