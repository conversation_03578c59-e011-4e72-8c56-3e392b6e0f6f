import Foundation
import SwiftUI
import WatchKit

// Define view states for better state management
enum SetViewState {
    case loading
    case loaded(RecommendationModel)
    case error(String)
}

// Define a struct to hold the current set data
struct CurrentSetData {
    var reps: Int
    var weight: Double
    var weightUnit: String
    var isFirstWorkSet: Bool
    var isLastPlannedSet: Bool
    var restDurationSeconds: Int

    init(set: SetRecommendationModel) {
        self.reps = set.reps
        self.weight = set.weight.value
        self.weightUnit = set.weight.unit == .kg ? "kg" : "lbs"
        self.isFirstWorkSet = set.isFirstWorkSet
        self.isLastPlannedSet = set.isLastPlannedSet
        self.restDurationSeconds = set.restDurationSeconds
    }

    init() {
        self.reps = 0
        self.weight = 0
        self.weightUnit = "lbs"
        self.isFirstWorkSet = false
        self.isLastPlannedSet = false
        self.restDurationSeconds = 60
    }
}

class SetViewModel: ObservableObject {
    // MARK: - Published Properties

    @Published private(set) var state: SetViewState = .loading
    @Published private(set) var exerciseName: String = ""
    @Published private(set) var currentReps: Int = 0
    @Published private(set) var currentWeight: Double = 0
    @Published private(set) var weightUnit: String = "lbs"
    @Published private(set) var currentSetIndex: Int = 0
    @Published private(set) var isFirstWorkSet: Bool = false
    @Published private(set) var isLastPlannedSet: Bool = false
    @Published private(set) var restDurationSeconds: Int = 0

    // Choice screen state
    @Published private(set) var showingChoiceScreen: Bool = false

    // Transition state
    @Published private(set) var showingCheckmarkAnimation: Bool = false
    @Published private(set) var shouldNavigateToNextExercise: Bool = false
    @Published private(set) var shouldNavigateToWorkoutComplete: Bool = false

    // Timer properties
    @Published var isTimerActive: Bool = false
    @Published var secondsRemaining: Int = 0
    private var timer: Timer?

    // MARK: - Computed Properties

    var isLoading: Bool {
        if case .loading = state { return true }
        return false
    }

    var hasError: Bool {
        if case .error(_) = state { return true }
        return false
    }

    var errorMessage: String? {
        if case .error(let message) = state { return message }
        return nil
    }

    var totalSets: Int {
        recommendation?.sets.count ?? 0
    }

    var currentSetNumber: Int {
        currentSetIndex + 1
    }

    var isWarmupSet: Bool {
        guard let recommendation = recommendation, currentSetIndex < recommendation.sets.count else {
            return false
        }
        return recommendation.sets[currentSetIndex].isWarmup
    }

    // MARK: - Private Properties

    private let apiClient: DrMuscleAPIClient
    private let storageService: StorageService
    private let exerciseId: Int64

    // These properties are exposed for navigation to the workout complete view
    let workoutId: Int64
    var workoutName: String {
        return recommendation?.workoutName ?? "Workout"
    }

    private var recommendation: RecommendationModel?
    private var currentSetData = CurrentSetData()
    private var historicalData: [OneRMModel]? = nil
    private var lastPerformanceWeight: Double? = nil
    private var lastPerformanceReps: Int? = nil

    // MARK: - Initialization

    init(exerciseId: Int64, workoutId: Int64, apiClient: DrMuscleAPIClient = DrMuscleAPIClient.shared, storageService: StorageService = StorageService.shared) {
        self.exerciseId = exerciseId
        self.workoutId = workoutId
        self.apiClient = apiClient
        self.storageService = storageService
    }

    // MARK: - Public Methods

    @MainActor
    func fetchExerciseDetails() async {
        state = .loading

        do {
            // Fetch exercise recommendation
            let requestModel = RecommendationRequestModel(exerciseId: exerciseId, workoutId: workoutId)

            do {
                // Try to fetch from API first
                let recommendation = try await apiClient.getRecommendationForExercise(model: requestModel)
                self.recommendation = recommendation

                // Update exercise details
                exerciseName = recommendation.exerciseName

                // Update current set details
                if !recommendation.sets.isEmpty {
                    updateCurrentSetDetails(recommendation.sets[currentSetIndex])
                }

                // Fetch historical data for performance calculation
                await fetchHistoricalData()

                state = .loaded(recommendation)
            } catch {
                // If API fetch fails, try to load from local storage
                print("Failed to fetch exercise details from API: \(error). Trying local storage...")

                // Try to get exercise from local storage
                if let exercise = try? storageService.getExercise(id: exerciseId) {
                    // We found the exercise in local storage
                    exerciseName = exercise.name ?? "Exercise"

                    // Create a basic recommendation with default values
                    // In a real implementation, you would load the full exercise details from local storage
                    let defaultSet = SetRecommendationModel(
                        reps: 8,
                        weight: WeightModel(value: 100, unit: .lbs),
                        isWarmup: false,
                        isFirstWorkSet: false,
                        isLastPlannedSet: false,
                        restDurationSeconds: 60
                    )

                    let offlineRecommendation = RecommendationModel(
                        exerciseName: exerciseName,
                        sets: [defaultSet]
                    )

                    self.recommendation = offlineRecommendation

                    // Update current set details
                    updateCurrentSetDetails(defaultSet)

                    // Try to load previous set logs for this exercise to get better defaults
                    if let setLogs = try? storageService.getSetLogsForExercise(exerciseID: exerciseId) {
                        if let lastSetLog = setLogs.last {
                            // Use the last set log as a reference for the current set
                            currentReps = Int(lastSetLog.reps)
                            currentWeight = lastSetLog.weight
                            weightUnit = lastSetLog.weightUnit ?? "lbs"
                        }
                    }

                    state = .loaded(offlineRecommendation)
                } else {
                    // We couldn't find the exercise in local storage either
                    let errorMessage = "Failed to load exercise details. Please check your connection."
                    state = .error(errorMessage)

                    // Use the error handling service to show a banner
                    ErrorHandlingService.shared.showError(message: errorMessage)
                }
            }
        } catch {
            let errorMessage = "Failed to load exercise details. Please check your connection."
            state = .error(errorMessage)

            // Use the error handling service to show a banner
            if let apiError = error as? APIError {
                ErrorHandlingService.shared.handleAPIError(apiError)
            } else {
                ErrorHandlingService.shared.handleError(error)
            }
        }
    }

    /// Fetches historical performance data for the current exercise
    @MainActor
    func fetchHistoricalData() async {
        do {
            // Try to fetch one-rep max data from the API
            let oneRMRequest = GetOneRMforExerciseModel(exerciseId: exerciseId)

            do {
                let oneRMData = try await apiClient.getOneRMForExercise(model: oneRMRequest)
                self.historicalData = oneRMData

                // If we have historical data, extract the most recent performance
                if let mostRecentPerformance = oneRMData.first {
                    self.lastPerformanceWeight = mostRecentPerformance.weight.value
                    self.lastPerformanceReps = mostRecentPerformance.reps
                }
            } catch {
                print("Failed to fetch historical data from API: \(error). Trying local storage...")

                // If API fetch fails, try to use local set logs as historical data
                if let setLogs = try? storageService.getSetLogsForExercise(exerciseID: exerciseId) {
                    if let lastSetLog = setLogs.last {
                        // Use the last set log as historical data
                        self.lastPerformanceWeight = lastSetLog.weight
                        self.lastPerformanceReps = Int(lastSetLog.reps)

                        // Create a synthetic OneRMModel from the set log
                        let syntheticOneRM = OneRMModel(
                            weight: WeightModel(value: lastSetLog.weight, unit: lastSetLog.weightUnit == "kg" ? .kg : .lbs),
                            reps: Int(lastSetLog.reps),
                            date: lastSetLog.timestamp ?? Date()
                        )

                        self.historicalData = [syntheticOneRM]
                    }
                }
            }

            // Recalculate performance percentage with the new historical data
            recalculatePerformancePercentage()
        } catch {
            print("Failed to fetch historical data: \(error)")
            // We don't want to fail the entire exercise loading if historical data fails
            // Just continue without historical data
        }
    }

    func moveToNextSet() -> Bool {
        guard let recommendation = recommendation else {
            return false
        }

        let nextIndex = currentSetIndex + 1
        if nextIndex < recommendation.sets.count {
            currentSetIndex = nextIndex
            updateCurrentSetDetails(recommendation.sets[nextIndex])
            return true
        }

        return false
    }

    func updateReps(_ reps: Int) {
        currentReps = reps
        currentSetData.reps = reps
        // Recalculate performance percentage when reps change
        recalculatePerformancePercentage()
    }

    func updateWeight(_ weight: Double) {
        currentWeight = weight
        currentSetData.weight = weight
        // Recalculate performance percentage when weight changes
        recalculatePerformancePercentage()
    }

    /// Calculate performance percentage based on current reps/weight vs historical data
    /// - Returns: The performance percentage change
    func getPerformancePercentage() -> Double {
        // If we don't have historical data, compare to the original set values
        if lastPerformanceWeight == nil || lastPerformanceReps == nil {
            guard let recommendation = recommendation, currentSetIndex < recommendation.sets.count else {
                return 0.0
            }

            let originalSet = recommendation.sets[currentSetIndex]
            let originalReps = originalSet.reps
            let originalWeight = originalSet.weight.value

            // Calculate 1RM using the Epley formula: weight * (1 + 0.0333 * reps)
            let original1RM = calculateOneRM(weight: originalWeight, reps: originalReps)
            let current1RM = calculateOneRM(weight: currentWeight, reps: currentReps)

            // Calculate percentage change
            return ((current1RM / original1RM) - 1) * 100
        } else {
            // We have historical data, so compare to that
            let historical1RM = calculateOneRM(weight: lastPerformanceWeight!, reps: lastPerformanceReps!)
            let current1RM = calculateOneRM(weight: currentWeight, reps: currentReps)

            // Calculate percentage change
            return ((current1RM / historical1RM) - 1) * 100
        }
    }

    /// Calculate one-rep max using the Epley formula
    /// - Parameters:
    ///   - weight: The weight lifted
    ///   - reps: The number of reps performed
    /// - Returns: The estimated one-rep max
    private func calculateOneRM(weight: Double, reps: Int) -> Double {
        // Epley formula: weight * (1 + 0.0333 * reps)
        return weight * (1 + 0.0333 * Double(reps))
    }

    /// Recalculate the performance percentage and trigger UI updates
    private func recalculatePerformancePercentage() {
        // This is called when reps or weight changes
        // The UI will automatically update because the view calls getPerformancePercentage()
        // No need to do anything here, but we keep the method for clarity
        objectWillChange.send()
    }

    func getCurrentSetData() -> CurrentSetData {
        return currentSetData
    }

    /// Determines whether the RIR picker should be shown for the current set
    /// - Returns: True if the RIR picker should be shown, false otherwise
    func shouldShowRIRPicker() -> Bool {
        // Show RIR picker only for the first work set and not for warmup sets
        return isFirstWorkSet && !isWarmupSet
    }

    /// Determines whether the choice screen should be shown
    /// - Returns: True if the choice screen should be shown, false otherwise
    func shouldShowChoiceScreen() -> Bool {
        return showingChoiceScreen
    }

    /// Determines whether the checkmark animation should be shown
    /// - Returns: True if the checkmark animation should be shown, false otherwise
    func shouldShowCheckmarkAnimation() -> Bool {
        return showingCheckmarkAnimation
    }

    /// Adds a set after the last planned set
    /// This resets the choice screen and timer state while preserving the current reps and weight
    func addSet() {
        // Hide the choice screen
        showingChoiceScreen = false

        // Stop the timer if it's running
        if isTimerActive {
            skipTimer()
        }

        // Mark this set as no longer the last planned set
        // This is important so that when the user saves this set, they don't get the choice screen again
        isLastPlannedSet = false

        // The current reps and weight values are already preserved in the currentReps and currentWeight properties
        // We don't need to do anything special to keep them, as they're not reset when hiding the choice screen

        // Note: We're intentionally not updating currentSetData with a new SetRecommendationModel
        // because we want to keep using the same values from the set that was just completed
    }

    /// Proceeds to the next exercise
    /// - Returns: True if there is a next exercise, false otherwise
    func nextExercise() -> Bool {
        // Hide the choice screen
        showingChoiceScreen = false

        // Stop the timer if it's running
        if isTimerActive {
            skipTimer()
        }

        // Show the checkmark animation
        showingCheckmarkAnimation = true

        // Reset the navigation flags
        shouldNavigateToNextExercise = false
        shouldNavigateToWorkoutComplete = false

        // Check if there is a next exercise
        // For this implementation, we'll simulate checking if this is the last exercise
        // In a real implementation, this would check the workout data
        let hasNextExercise = hasNextExercise()

        return hasNextExercise
    }

    /// Called when the checkmark animation completes
    func onCheckmarkAnimationComplete() {
        // Hide the checkmark animation
        showingCheckmarkAnimation = false

        // Check if there is a next exercise
        if hasNextExercise() {
            // Set the flag to navigate to the next exercise
            shouldNavigateToNextExercise = true
        } else {
            // Set the flag to navigate to the workout complete screen
            shouldNavigateToWorkoutComplete = true
        }
    }

    /// Checks if there is a next exercise
    /// - Returns: True if there is a next exercise, false otherwise
    private func hasNextExercise() -> Bool {
        // In a real implementation, this would check the workout data
        // For now, we'll simulate this by checking if there's a next exercise in the recommendation
        guard let recommendation = recommendation else {
            return false
        }

        // This is a simplified check - in a real implementation, you would check
        // if there are more exercises in the workout
        return false // For testing, always return false to navigate to workout complete
    }

    /// Saves the current set data to local storage
    /// - Parameter rir: Optional RIR value to save with the set
    /// - Returns: True if the set was saved successfully, false otherwise
    @MainActor
    func saveCurrentSet(rir: Int? = nil) async -> Bool {
        do {
            // Convert Int to Int16 for Core Data
            let repsInt16 = Int16(currentReps)
            let rirInt16: Int16? = rir != nil ? Int16(rir!) : nil

            // Save the set log to local storage
            _ = try storageService.saveSetLog(
                exerciseID: exerciseId,
                workoutID: workoutId,
                reps: repsInt16,
                weight: currentWeight,
                weightUnit: weightUnit,
                rir: rirInt16,
                timestamp: Date(),
                isWarmup: isWarmupSet,
                needsSync: true
            )

            // If this is the last planned set, show the choice screen
            if isLastPlannedSet {
                showingChoiceScreen = true
            }

            return true
        } catch {
            print("Error saving set log: \(error)")
            return false
        }
    }

    // MARK: - Timer Methods

    /// Starts the rest timer with the current rest duration
    func startTimer() {
        // Cancel any existing timer
        stopTimerWithoutStateChange()

        // Set initial timer values
        secondsRemaining = max(restDurationSeconds, 1) // Ensure at least 1 second
        isTimerActive = true

        // Create a new timer that fires every second
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.timerTick()
        }

        // Add the timer to the common run loop mode to ensure it runs even during scrolling
        RunLoop.current.add(timer!, forMode: .common)
    }

    /// Called every second by the timer to update the countdown
    func timerTick() {
        guard isTimerActive, secondsRemaining > 0 else {
            // Timer has reached zero or is no longer active, stop it
            timerComplete()
            return
        }

        // Decrement the seconds remaining
        secondsRemaining -= 1

        // If we've reached zero, stop the timer
        if secondsRemaining == 0 {
            timerComplete()
        }
    }

    /// Called when the timer reaches zero
    func timerComplete() {
        // Stop the timer
        stopTimerWithoutStateChange()

        // Update state
        isTimerActive = false

        // Trigger haptic feedback
        WKInterfaceDevice.current().play(.notification)

        // Note: We don't hide the choice screen when the timer completes
        // The user still needs to make a choice between "Add Set" and "Next Exercise"
        // The button text will change from "Next Exercise 0:00" to just "Next Exercise"
    }

    /// Allows the user to skip the rest timer
    func skipTimer() {
        // Stop the timer
        stopTimerWithoutStateChange()

        // Update state
        isTimerActive = false
    }

    /// Helper method to stop the timer without changing the isTimerActive state
    private func stopTimerWithoutStateChange() {
        timer?.invalidate()
        timer = nil
    }

    /// Clean up resources when the view model is deallocated
    deinit {
        stopTimerWithoutStateChange()
    }

    // MARK: - Private Methods

    /// Updates the current set details with the provided set recommendation
    /// - Parameter set: The set recommendation to use for updating the current set details
    func updateCurrentSetDetails(_ set: SetRecommendationModel) {
        currentSetData = CurrentSetData(set: set)

        // Update published properties
        currentReps = currentSetData.reps
        currentWeight = currentSetData.weight
        weightUnit = currentSetData.weightUnit
        isFirstWorkSet = currentSetData.isFirstWorkSet
        isLastPlannedSet = currentSetData.isLastPlannedSet
        restDurationSeconds = currentSetData.restDurationSeconds
    }
}
