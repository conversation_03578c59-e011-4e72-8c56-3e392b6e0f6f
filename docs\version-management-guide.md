@@ -0,0 +1,161 @@
# Version Management for CI/CD

This document outlines the approach for managing version numbers in the Dr. Muscle app's CI/CD pipeline and provides implementation instructions.

## Overview

Version management is a critical aspect of mobile app development, especially when deploying to app stores. Each build submitted to the App Store or Google Play must have a unique and higher version number than the previous submission.

## Current Challenges

- **Multiple Developers**: With 5 developers on the team, manually managing version numbers in source files can lead to conflicts
- **Consistency**: Ensuring consistent version numbering across different platforms (iOS and Android)
- **Traceability**: Maintaining a clear relationship between builds and version numbers
- **Reproducibility**: Being able to reproduce a specific build if needed

## Solution: Environment Variable-Based Version Management

Instead of modifying version numbers in source files, we use environment variables during the build process to set the version number dynamically.

### How It Works

1. **Base Version**: The major and minor version numbers (e.g., 2.9) are maintained in the source files
2. **Build Number**: The build number is derived from the GitHub Actions run number
3. **Complete Version**: The complete version is constructed as `{major}.{minor}.{build_number}`

### Benefits

1. **No File Modifications**: Source files remain unchanged during the build process
2. **No Conflicts**: Developers don't need to worry about version number conflicts
3. **Automatic Incrementing**: Each build automatically gets a higher version number
4. **Traceability**: Version numbers are directly tied to GitHub Actions run numbers
5. **Consistency**: The same version number is used across all platforms

## Implementation Guide

### Steps to Update the Workflow File

1. Open the workflow file: `.github/workflows/dotnet.yml`

2. Add the version environment variable setup step before the build step:

```yaml
- name: Set version environment variables
  run: |
    # Extract base version from Info.plist (major.minor)
    BASE_VERSION=$(grep -A 1 "CFBundleShortVersionString" DrMaxMuscle/Platforms/iOS/Info.plist | grep -o '>.*<' | sed 's/[<>]//g' | cut -d. -f1,2)
    # Use GitHub Actions run number as build number
    BUILD_NUMBER=${{ github.run_number }}
    # Create full version string
    APP_VERSION="$BASE_VERSION.$BUILD_NUMBER"
    echo "Using version: $APP_VERSION"
    # Set as environment variable for subsequent steps
    echo "APP_VERSION=$APP_VERSION" >> $GITHUB_ENV
```

3. Update the iOS build step to use the environment variable:

```yaml
- name: Build iOS
  env:
    APP_VERSION: ${{ env.APP_VERSION }}
  run: dotnet publish DrMaxMuscle/DrMaxMuscle.csproj -f net8.0-ios -c Release -p:BuildPlatform=ios -p:GenerateIpa=true -p:RuntimeIdentifier=ios-arm64 -p:ApplicationVersion=$APP_VERSION -p:ApplicationDisplayVersion=$APP_VERSION
```

4. Update the Android build step similarly:

```yaml
- name: Build Android
  env:
    APP_VERSION: ${{ env.APP_VERSION }}
  run: dotnet publish DrMaxMuscle/DrMaxMuscle.csproj -f net8.0-android -c Release -p:BuildPlatform=android -p:ApplicationVersion=$APP_VERSION -p:ApplicationDisplayVersion=$APP_VERSION
```

### Example of Complete Workflow Section

Here's an example of how the relevant section of the workflow file should look after the changes:

```yaml
jobs:
  build-ios:
    name: iOS Build (macOS)
    if: github.ref == 'refs/heads/Firebase_Common_Auth_Google_Code'
    runs-on: macos-15

    steps:
    - uses: actions/checkout@v4

    # Other setup steps...

    - name: Set version environment variables
      run: |
        # Extract base version from Info.plist (major.minor)
        BASE_VERSION=$(grep -A 1 "CFBundleShortVersionString" DrMaxMuscle/Platforms/iOS/Info.plist | grep -o '>.*<' | sed 's/[<>]//g' | cut -d. -f1,2)
        # Use GitHub Actions run number as build number
        BUILD_NUMBER=${{ github.run_number }}
        # Create full version string
        APP_VERSION="$BASE_VERSION.$BUILD_NUMBER"
        echo "Using version: $APP_VERSION"
        # Set as environment variable for subsequent steps
        echo "APP_VERSION=$APP_VERSION" >> $GITHUB_ENV

    # Code signing steps...

    - name: Build iOS
      env:
        APP_VERSION: ${{ env.APP_VERSION }}
      run: dotnet publish DrMaxMuscle/DrMaxMuscle.csproj -f net8.0-ios -c Release -p:BuildPlatform=ios -p:GenerateIpa=true -p:RuntimeIdentifier=ios-arm64 -p:ApplicationVersion=$APP_VERSION -p:ApplicationDisplayVersion=$APP_VERSION

    # Upload steps...
```

## Version Number Format

- **iOS**: `{major}.{minor}.{build_number}` (e.g., 2.9.123)
- **Android**: `{major}.{minor}.{build_number}` (e.g., 2.9.123)

## Updating the Base Version

When a significant update is ready for release, the base version (major.minor) should be updated in:

1. `DrMaxMuscle/Platforms/iOS/Info.plist`
2. `DrMaxMuscle/DrMaxMuscle.csproj`

This should be done as part of the release process and committed to the repository.

## Testing the Changes

After implementing these changes:

1. Commit the changes to a feature branch
2. Push the branch to GitHub
3. Manually trigger the workflow
4. Check the workflow logs to verify that:
   - The version is correctly extracted and set
   - The build command includes the version parameters
   - The build completes successfully
   - The app is uploaded to the App Store with the correct version

## Troubleshooting

### Version Number Conflicts

If you encounter version number conflicts with the App Store or Google Play:

1. Check the last successful build number
2. Ensure the GitHub Actions workflow is using the correct base version
3. If necessary, manually increment the base version in the source files

### Reproducing a Specific Build

To reproduce a build with a specific version number:

1. Identify the GitHub Actions run number that corresponds to the version
2. Use the same source code commit that was used for that run
3. Manually set the version number during the build process if needed

### Common Issues

1. **Version extraction fails**: Check the format of the Info.plist file and adjust the grep/sed commands accordingly
2. **Build fails with version parameter**: Verify that the parameter names are correct for your project
3. **App Store upload fails**: Check the logs for specific error messages related to versioning