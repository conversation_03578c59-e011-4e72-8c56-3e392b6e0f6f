﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <!-- <TargetFrameworks>net8.0-android;net8.0-ios</TargetFrameworks> -->
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>DrMuscleWebApiSharedModel</RootNamespace>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <CreatePackage>false</CreatePackage>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <CreatePackage>false</CreatePackage>
    <DebugSymbols>true</DebugSymbols>
    <WarningLevel>4</WarningLevel>
    <AssemblyName>DrMuscleWebApiSharedModel</AssemblyName>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

</Project>
