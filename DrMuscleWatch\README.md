# Dr. Muscle Apple Watch App (Standalone V1)

This repository contains the source code for the standalone Dr. Muscle watchOS application (Version 1). This app allows users to track their Dr. Muscle workouts directly on their Apple Watch, fetching data from and syncing progress to the Dr. Muscle backend API, independent of the iPhone app.

## Prerequisites

Before you begin, ensure you have the following installed:

*   **Xcode:** Version 15.0 or later (or the version specified by the project configuration)
*   **Swift:** Version 5.9 or later (typically included with Xcode)
*   **SwiftLint:** (Optional but recommended for code style consistency)
    *   Install via Homebrew: `brew install swiftlint`

## Getting Started

1.  **Clone the repository:**
    ```bash
    git clone <repository-url>
    cd <repository-directory>
    ```
2.  **Open the project:**
    *   If the project uses an `.xcodeproj` file:
        ```bash
        open DrMuscleWatch.xcodeproj
        ```
    *   If the project uses an `.xcworkspace` file (e.g., if using CocoaPods or Swift Package Manager dependencies that require it):
        ```bash
        open DrMuscleWatch.xcworkspace
        ```

## Building and Running

1.  Open the project in Xcode.
2.  Select the **`DrMuscleWatch Watch App`** target (or the appropriately named watch app target) from the scheme selector at the top of the Xcode window.
3.  Choose a target device:
    *   **Simulator:** Select an appropriate Apple Watch simulator (e.g., "Apple Watch Series 9 (45mm)") from the device list.
    *   **Physical Device:** Connect your paired Apple Watch via your iPhone and select it from the device list.
4.  Click the **Run** button (or press `Cmd + R`). Xcode will build the app and install/run it on the selected simulator or device.

## Running Tests

You can run the automated tests (XCTest) using either Xcode or the command line:

*   **Xcode:**
    1.  Select the **`DrMuscleWatch Watch App`** scheme (or the scheme configured for testing).
    2.  Go to the menu **Product > Test** (or press `Cmd + U`).
*   **Command Line (Example):**
    *   You may need to adjust the scheme name and destination based on your project setup. Find available simulators using `xcrun simctl list devices`.
    ```bash
    xcodebuild test \
      -scheme "DrMuscleWatch Watch App" \
      -destination 'platform=watchOS Simulator,name=Apple Watch Series 9 (45mm),OS=latest' \
      # Use -workspace DrMuscleWatch.xcworkspace if applicable, otherwise -project DrMuscleWatch.xcodeproj
      # -project DrMuscleWatch.xcodeproj
    ```

## Running Linter (SwiftLint)

If you have SwiftLint installed, you can run it from the project's root directory:

```bash
swiftlint
swiftlint --fix
```

##  Documentation
Detailed project documentation can be found in the /docs directory:
/docs/rules.md: Development workflow, coding standards, and operational rules. MUST READ.
/docs/architecture.md: System architecture, technical design patterns, and dependencies.
/docs/plan.md: High-level project plan and V1 feature overview.
/docs/todo.md: Detailed task list with BDD Acceptance Criteria (User Stories). This drives development.
/docs/adr/: Architecture Decision Records explaining key technical choices.
/docs/status.md: Current project status and progress tracking.
/docs/testing.md: List of implemented automated tests.
