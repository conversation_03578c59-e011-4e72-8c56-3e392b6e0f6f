import SwiftUI

/// A component for displaying a value with a label (e.g., reps or weight)
struct ValueDisplay: View {
    let value: String
    let label: String
    let action: () -> Void

    var body: some View {
        VStack(spacing: 0) {
            Text(value)
                .font(AppTheme.largeValueStyle)
                .foregroundColor(AppTheme.primaryTextColor)
                .padding(8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.gray.opacity(0.2))
                )
                .overlay(
                    Image(systemName: "chevron.up.chevron.down")
                        .font(.system(size: 12))
                        .foregroundColor(AppTheme.secondaryTextColor)
                        .offset(x: 30, y: 0)
                )
                .onTapGesture(perform: action)

            Text(label)
                .font(AppTheme.smallStyle)
                .foregroundColor(AppTheme.secondaryTextColor)
                .padding(.top, 4)
        }
    }
}
