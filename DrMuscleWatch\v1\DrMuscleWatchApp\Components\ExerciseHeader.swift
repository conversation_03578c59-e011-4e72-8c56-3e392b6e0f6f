import SwiftUI

/// A component for displaying the exercise name with proper styling
struct ExerciseHeader: View {
    let exerciseName: String
    
    var body: some View {
        HStack {
            ScrollView(.horizontal, showsIndicators: false) {
                Text(exerciseName)
                    .font(AppTheme.titleStyle)
                    .foregroundColor(AppTheme.primaryTextColor)
                    .lineLimit(1)
                    .truncationMode(.tail)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [.clear, .black]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .opacity(1.0)
                    .padding(.top, 0.0)
            }
            .mask(LinearGradient(
                gradient: Gradient(stops: [
                    .init(color: .black, location: 0.75),
                    .init(color: .clear, location: 0.85)
                ]),
                startPoint: .leading,
                endPoint: .trailing
            ))
            .frame(maxWidth: 150, alignment: .leading)
            .padding(.top, -28.0)
            .padding(.leading, -20.0)
        }
    }
}
