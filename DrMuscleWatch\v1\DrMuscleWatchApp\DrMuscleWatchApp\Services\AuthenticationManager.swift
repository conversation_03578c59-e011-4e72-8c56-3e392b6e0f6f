import Foundation
import AuthenticationServices
import Security

/// Manages authentication state and operations
class AuthenticationManager: NSObject, ObservableObject {
    /// Shared instance for the entire application
    static let shared = AuthenticationManager()

    /// The API client for making network requests
    private let apiClient = DrMuscleAPIClient.shared

    /// Published property indicating whether the user is authenticated
    @Published var isAuthenticated: Bool = false

    /// The current authenticated user
    @Published var currentUser: UserInfosModel?

    /// Error message if authentication fails
    @Published var authError: String?

    /// Private initializer to enforce singleton pattern
    private override init() {
        super.init()
        loadAuthState()
    }

    /// Loads the authentication state from secure storage
    private func loadAuthState() {
        // Check if we have a stored token
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: "DrMuscleAuthToken",
            kSecReturnData as String: true
        ]

        var item: CFTypeRef?
        let status = SecItemCopyMatching(query as CFDictionary, &item)

        guard status == errSecSuccess,
              let tokenData = item as? Data,
              let token = String(data: tokenData, encoding: .utf8) else {
            isAuthenticated = false
            currentUser = nil
            return
        }

        // Set the token in the API client
        apiClient.token = token

        // Load user info from UserDefaults
        if let userData = UserDefaults.standard.data(forKey: "DrMuscleUserInfo"),
           let userInfo = try? JSONDecoder().decode(UserInfosModel.self, from: userData) {
            currentUser = userInfo
            isAuthenticated = true
        } else {
            isAuthenticated = false
            currentUser = nil
        }
    }

    /// Stores the authentication state in secure storage
    func storeAuthState(userInfo: UserInfosModel) {
        // Store the token in the keychain
        let tokenData = userInfo.token.data(using: .utf8)!

        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: "DrMuscleAuthToken",
            kSecValueData as String: tokenData
        ]

        // First try to update the existing item
        var status = SecItemUpdate(query as CFDictionary, [kSecValueData as String: tokenData] as CFDictionary)

        // If the item doesn't exist, add it
        if status == errSecItemNotFound {
            status = SecItemAdd(query as CFDictionary, nil)
        }

        // Set the token in the API client
        apiClient.token = userInfo.token

        // Store user info in UserDefaults
        if let userData = try? JSONEncoder().encode(userInfo) {
            UserDefaults.standard.set(userData, forKey: "DrMuscleUserInfo")
        }

        // Update the published properties
        currentUser = userInfo
        isAuthenticated = true
        authError = nil
    }

    /// Clears the authentication state
    func clearAuthState() {
        // Remove the token from the keychain
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: "DrMuscleAuthToken"
        ]

        SecItemDelete(query as CFDictionary)

        // Clear the token in the API client
        apiClient.token = nil

        // Remove user info from UserDefaults
        UserDefaults.standard.removeObject(forKey: "DrMuscleUserInfo")

        // Update the published properties
        currentUser = nil
        isAuthenticated = false
    }

    /// Authentication error types
    enum AuthError: Error, LocalizedError {
        case invalidCredential
        case missingToken
        case apiError(Error)

        var errorDescription: String? {
            switch self {
            case .invalidCredential:
                return "Invalid Apple ID credential"
            case .missingToken:
                return "Missing identity token"
            case .apiError(let error):
                return "API error: \(error.localizedDescription)"
            }
        }
    }

    /// Handles the completion of the Sign in with Apple flow
    func handleSignInWithAppleCompletion(
        credential: ASAuthorizationAppleIDCredential?,
        error: Error?,
        completion: @escaping (Result<UserInfosModel, Error>) -> Void
    ) {
        // Check for error
        if let error = error {
            self.authError = error.localizedDescription
            completion(.failure(error))
            return
        }

        // Check for credential
        guard let credential = credential else {
            let error = AuthError.invalidCredential
            self.authError = error.localizedDescription
            completion(.failure(error))
            return
        }

        // Check for identity token
        guard let identityToken = credential.identityToken,
              let idTokenString = String(data: identityToken, encoding: .utf8) else {
            let error = AuthError.missingToken
            self.authError = error.localizedDescription
            completion(.failure(error))
            return
        }

        // Call the API to sign in with Apple
        Task {
            do {
                let userInfo = try await apiClient.signInWithApple(idToken: idTokenString)

                // Store the authentication state
                self.storeAuthState(userInfo: userInfo)

                // Complete with success
                DispatchQueue.main.async {
                    completion(.success(userInfo))
                }
            } catch {
                // Handle API error
                let authError = AuthError.apiError(error)
                DispatchQueue.main.async {
                    self.authError = authError.localizedDescription
                    completion(.failure(authError))
                }
            }
        }
    }
}
