import Foundation

/// Request model for getting exercise recommendations
struct RecommendationRequestModel: Codable {
    let exerciseId: Int64
    let workoutId: Int64?

    enum CodingKeys: String, CodingKey {
        case exerciseId = "ExerciseId"
        case workoutId = "WorkoutId"
    }
}

/// Model for weight units
enum WeightUnit: String, Codable {
    case kg
    case lb
}

/// Model for a weight value with unit
struct WeightModel: Codable {
    let value: Double
    let unit: WeightUnit

    enum CodingKeys: String, CodingKey {
        case value = "Value"
        case unit = "Unit"
    }
}

/// Model for exercise recommendations
struct RecommendationModel: Codable {
    let sets: [SetRecommendationModel]
    let exerciseId: Int64
    let exerciseName: String
    let isBodyweight: Bool
    let isWeighted: Bool
    let isAssisted: Bool
    let isTimeBased: Bool
    let isUnilateral: Bool
    let workoutName: String

    enum CodingKeys: String, CodingKey {
        case sets = "Sets"
        case exerciseId = "ExerciseId"
        case exerciseName = "ExerciseName"
        case isBodyweight = "IsBodyweight"
        case isWeighted = "IsWeighted"
        case isAssisted = "IsAssisted"
        case isTimeBased = "IsTimeBased"
        case isUnilateral = "IsUnilateral"
        case workoutName = "WorkoutName"
    }
}

/// Model for a set recommendation
struct SetRecommendationModel: Codable {
    let reps: Int
    let weight: WeightModel
    let isWarmup: Bool
    let isFirstWorkSet: Bool
    let isLastPlannedSet: Bool
    let restDurationSeconds: Int

    enum CodingKeys: String, CodingKey {
        case reps = "Reps"
        case weight = "Weight"
        case isWarmup = "IsWarmup"
        case isFirstWorkSet = "IsFirstWorkSet"
        case isLastPlannedSet = "IsLastPlannedSet"
        case restDurationSeconds = "RestDurationSeconds"
    }
}
