import XCTest
@testable import DrMuscleWatchApp

class TransitionTests: XCTestCase {

    func testNextExerciseActionShowsCheckmarkAnimation() {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockStorageService = MockStorageService()

        let viewModel = SetViewModel(
            exerciseId: 123,
            workoutId: 456,
            apiClient: mockAPIClient,
            storageService: mockStorageService
        )
        viewModel.showingChoiceScreen = true

        // When
        let result = viewModel.nextExercise()

        // Then
        XCTAssertTrue(result)
        XCTAssertFalse(viewModel.shouldShowChoiceScreen())
        XCTAssertTrue(viewModel.shouldShowCheckmarkAnimation())
    }

    func testCheckmarkAnimationCompletesAndNavigatesToNextExercise() {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockStorageService = MockStorageService()

        let viewModel = SetViewModel(
            exerciseId: 123,
            workoutId: 456,
            apiClient: mockAPIClient,
            storageService: mockStorageService
        )
        viewModel.showingChoiceScreen = true

        // When
        _ = viewModel.nextExercise()
        viewModel.onCheckmarkAnimationComplete()

        // Then
        XCTAssertFalse(viewModel.shouldShowChoiceScreen())
        XCTAssertFalse(viewModel.shouldShowCheckmarkAnimation())
        XCTAssertTrue(viewModel.shouldNavigateToNextExercise)
    }

    func testSkippingInterExerciseRest() {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockStorageService = MockStorageService()

        let viewModel = SetViewModel(
            exerciseId: 123,
            workoutId: 456,
            apiClient: mockAPIClient,
            storageService: mockStorageService
        )
        viewModel.showingChoiceScreen = true
        viewModel.isTimerActive = true
        viewModel.secondsRemaining = 30

        // When
        _ = viewModel.nextExercise()

        // Then
        XCTAssertFalse(viewModel.isTimerActive)
        XCTAssertTrue(viewModel.shouldShowCheckmarkAnimation())
    }

    func testInterExerciseRestTimerCompletes() {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockStorageService = MockStorageService()

        let viewModel = SetViewModel(
            exerciseId: 123,
            workoutId: 456,
            apiClient: mockAPIClient,
            storageService: mockStorageService
        )
        viewModel.showingChoiceScreen = true
        viewModel.isTimerActive = true
        viewModel.secondsRemaining = 1

        // When
        viewModel.timerTick() // This should trigger timer completion

        // Then
        XCTAssertFalse(viewModel.isTimerActive)
        XCTAssertTrue(viewModel.shouldShowChoiceScreen()) // Choice screen should still be visible
    }

    func testProceedingAfterTimerExpiry() {
        // Given
        let mockAPIClient = MockDrMuscleAPIClient()
        let mockStorageService = MockStorageService()

        let viewModel = SetViewModel(
            exerciseId: 123,
            workoutId: 456,
            apiClient: mockAPIClient,
            storageService: mockStorageService
        )
        viewModel.showingChoiceScreen = true
        viewModel.isTimerActive = false // Timer has expired

        // When
        _ = viewModel.nextExercise()

        // Then
        XCTAssertTrue(viewModel.shouldShowCheckmarkAnimation())
    }
}
