# Remote Development Setup for DrMuscle Watch App

This document outlines the setup and workflow for developing the DrMuscle Watch app using a remote macOS environment on Scaleway.

## Environment Setup

### Mac Mini on Scaleway

The development environment uses a Mac mini hosted on Scaleway's Apple Silicon as a Service (ASaaS) with the following configuration:

- **macOS Version**: Sequoia 15.2
- **Server IP**: *************
- **VNC Port**: 59010
- **Username**: m1
- **SSH Access**: Enabled

### Development Tools

- **Xcode**: Pre-installed on the Mac mini
- **Git**: Installed via Homebrew
- **SSH**: For command-line operations
- **VNC**: For visual access to the GUI environment

## Development Workflow

The workflow uses a hybrid approach:

1. **Code Editing**: Done locally on Windows
2. **Version Control**: Git operations performed locally
3. **Building & Testing**: Done on the remote Mac mini
4. **Visualization**: Using VNC to view the Mac's GUI

### Step-by-Step Workflow

#### Initial Setup

1. **Generate SSH Key**:
   ```bash
   ssh-keygen -t ed25519 -C "drmuscle-watch-app"
   ```

2. **Connect to Mac Mini via SSH**:
   ```bash
   ssh m1@*************
   ```

3. **Clone Repository**:
   ```bash
   cd ~
   git clone https://github.com/dr-muscle/DrMuscle.git
   cd DrMuscle
   git checkout Development_Watch_Carl
   ```

4. **Connect via VNC**:
   - Use RealVNC Viewer or similar client
   - Connect to: *************:59010
   - Use the Mac mini password for authentication

#### Daily Development

1. **Edit Code Locally** on Windows machine

2. **Commit and Push Changes**:
   ```bash
   git add .
   git commit -m "Your descriptive commit message"
   git push origin Development_Watch_Carl
   ```

3. **Pull Changes on Mac** via SSH:
   ```bash
   ssh m1@************* "cd ~/DrMuscle && git pull origin Development_Watch_Carl"
   ```

4. **Open Project in Xcode** via SSH:
   ```bash
   ssh m1@************* "open -a Xcode ~/DrMuscle/DrMuscleWatch/DrMuscleWatchApp.xcodeproj"
   ```
   (Adjust the path as needed for your project location)

5. **View Xcode and Simulator** through VNC viewer

6. **Run Terminal Commands** via SSH as needed

## Tips and Best Practices

1. **Keep SSH and VNC Sessions Open** in separate windows for efficiency

2. **Use SSH for All Command-Line Operations**:
   - Git operations
   - File management
   - Running scripts

3. **Use VNC Only for Visual Tasks**:
   - Viewing Xcode interface
   - Interacting with the simulator
   - Debugging visual elements

4. **Commit Frequently** to ensure changes are synchronized

5. **Consider Creating Aliases** for common commands:
   ```bash
   # Add to ~/.zshrc on the Mac
   alias update="cd ~/DrMuscle && git pull origin Development_Watch_Carl"
   alias runwatch="open -a Xcode ~/DrMuscle/DrMuscleWatch/DrMuscleWatchApp.xcodeproj"
   ```

## Troubleshooting

### SSH Connection Issues

If you encounter SSH connection problems:
- Verify the server is running
- Check your SSH key permissions
- Ensure the correct username and IP are used

### Git Authentication

If GitHub authentication fails:
- Use a personal access token instead of password
- Configure credential storage:
  ```bash
  git config --global credential.helper store
  ```

### VNC Connection Issues

If VNC connection fails:
- Verify the correct port is being used (59010)
- Check if the Mac mini is running
- Ensure VNC service is enabled on the Mac

## Resources

- [Scaleway Mac Mini Documentation](https://www.scaleway.com/en/docs/compute/apple-silicon/how-to/create-a-mac-mini/)
- [Apple Developer Documentation](https://developer.apple.com/documentation/)
- [watchOS Development Resources](https://developer.apple.com/watchos/)
