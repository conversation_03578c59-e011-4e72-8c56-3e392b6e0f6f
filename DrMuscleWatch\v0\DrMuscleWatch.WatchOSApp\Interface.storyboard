<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder.WatchKit.Storyboard" version="3.0" toolsVersion="16097" targetRuntime="watchKit" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="AgC-eL-Hgc">
    <device id="watch38"/>
    <dependencies>
        <deployment identifier="watchOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16087"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBWatchKitPlugin" version="16012"/>
    </dependencies>
    <scenes>
        <!--Interface Controller-->
        <scene sceneID="aou-V4-d1y">
            <objects>
                <controller spacing="0.0" id="AgC-eL-Hgc" customClass="InterfaceController">
                    <items>
                        <group width="1" alignment="left" verticalAlignment="center" layout="overlap" radius="0.0" id="qhQ-7Z-cjS">
                            <items>
                                <group width="1" alignment="left" layout="vertical" radius="0.0" spacing="0.0" id="LnJ-B9-DGi">
                                    <items>
                                        <label alignment="center" textAlignment="center" id="zMQ-iu-jci"/>
                                        <group width="1" alignment="left" layout="vertical" spacing="0.0" id="Ocz-UM-DMi">
                                            <items>
                                                <label width="44" height="18" alignment="center" text="Reps" textAlignment="center" id="CyQ-c2-5Nk" userLabel="LblReps">
                                                    <fontDescription key="font" style="UICTFontTextStyleCaption2"/>
                                                </label>
                                                <group width="1" alignment="left" id="wYD-MR-xoo">
                                                    <items>
                                                        <button width="30" alignment="left" title="-" id="N3A-W1-AOh">
                                                            <fontDescription key="font" type="system" pointSize="22"/>
                                                            <connections>
                                                                <action selector="BtnRepsLess" destination="AgC-eL-Hgc" id="WuG-iE-nTH"/>
                                                            </connections>
                                                        </button>
                                                        <label height="34" alignment="center" text="Reps" id="4d1-Zt-8Vm" userLabel="LblRepsValue">
                                                            <fontDescription key="font" style="UICTFontTextStyleHeadline"/>
                                                        </label>
                                                        <button width="30" alignment="right" title="+" id="8Hh-XQ-N72">
                                                            <fontDescription key="font" type="system" pointSize="22"/>
                                                            <connections>
                                                                <action selector="BtnRepsMore" destination="AgC-eL-Hgc" id="LiI-g4-iNy"/>
                                                            </connections>
                                                        </button>
                                                    </items>
                                                </group>
                                            </items>
                                        </group>
                                        <group width="1" alignment="left" layout="vertical" spacing="0.0" id="uZA-QO-qpS">
                                            <items>
                                                <label width="59" height="18" alignment="center" text="Weight" textAlignment="center" id="5pv-DX-FUB" userLabel="LblWeight">
                                                    <fontDescription key="font" style="UICTFontTextStyleCaption2"/>
                                                </label>
                                                <group width="1" alignment="left" id="yne-aI-VM3">
                                                    <items>
                                                        <button width="30" alignment="left" title="-" id="ZH6-7z-hmU">
                                                            <fontDescription key="font" type="system" pointSize="22"/>
                                                            <connections>
                                                                <action selector="BtnWeightLess" destination="AgC-eL-Hgc" id="qCp-CT-P6F"/>
                                                            </connections>
                                                        </button>
                                                        <label height="45" alignment="center" text="Weight" id="0hj-wj-Jib" userLabel="LblWeightValue">
                                                            <fontDescription key="font" style="UICTFontTextStyleHeadline"/>
                                                        </label>
                                                        <button width="30" alignment="right" title="+" id="m1C-nx-DRd">
                                                            <fontDescription key="font" type="system" pointSize="22"/>
                                                            <connections>
                                                                <action selector="BtnWeightMore" destination="AgC-eL-Hgc" id="RAN-4q-v3R"/>
                                                            </connections>
                                                        </button>
                                                    </items>
                                                </group>
                                            </items>
                                        </group>
                                        <group width="1" alignment="left" radius="0.0" id="va4-mf-6gx">
                                            <items>
                                                <button width="1" alignment="left" title="Save set" id="gUg-jT-2nB" userLabel="btnSaveset">
                                                    <connections>
                                                        <action selector="BtnSavesetclicked" destination="AgC-eL-Hgc" id="RwA-un-IY9"/>
                                                    </connections>
                                                </button>
                                            </items>
                                        </group>
                                    </items>
                                </group>
                                <group width="1" height="1" alignment="center" verticalAlignment="center" animationDuration="1" layout="vertical" id="YMt-f3-aCe">
                                    <items>
                                        <group width="1" height="1" alignment="center" verticalAlignment="center" layout="vertical" id="QmG-Kc-9nz">
                                            <items>
                                                <label alignment="center" verticalAlignment="center" text="Rest" textAlignment="center" id="Gok-De-fw6"/>
                                                <timer alignment="center" verticalAlignment="center" textAlignment="center" id="f62-NI-wSO">
                                                    <calendarUnit key="units" minute="YES" second="YES"/>
                                                </timer>
                                                <button width="1" alignment="center" verticalAlignment="center" title="Hide" id="Ti7-Sn-APL">
                                                    <connections>
                                                        <action selector="BtnHideTimerClicked" destination="AgC-eL-Hgc" id="iCo-LI-e6W"/>
                                                    </connections>
                                                </button>
                                            </items>
                                        </group>
                                    </items>
                                    <gestureRecognizers>
                                        <tapGestureRecognizer id="6X5-5G-5Yx">
                                            <connections>
                                                <action selector="TimerGestureTapped:" destination="AgC-eL-Hgc" id="pjT-89-rSa"/>
                                            </connections>
                                        </tapGestureRecognizer>
                                    </gestureRecognizers>
                                </group>
                                <button width="1" alignment="center" verticalAlignment="center" title="Finish exercise" id="xXR-bZ-uJP">
                                    <connections>
                                        <action selector="BtnFinishExerciseClicked" destination="AgC-eL-Hgc" id="bRb-ZH-UZa"/>
                                    </connections>
                                </button>
                                <button width="1" alignment="center" verticalAlignment="center" title="Next exercise" id="WOw-ov-KhS">
                                    <connections>
                                        <action selector="BtnNextExerrciseClicked" destination="AgC-eL-Hgc" id="TyO-F6-qpU"/>
                                    </connections>
                                </button>
                                <button width="1" alignment="center" verticalAlignment="center" title="Finish and save workout" id="ndD-2A-d9P">
                                    <connections>
                                        <action selector="BtnFinishandSaveClicked" destination="AgC-eL-Hgc" id="Fcj-LO-3al"/>
                                    </connections>
                                </button>
                                <group width="1" height="1" alignment="center" verticalAlignment="bottom" layout="vertical" spacing="0.0" id="btS-fx-LQq">
                                    <items>
                                        <picker height="125" alignment="center" verticalAlignment="bottom" focusStyle="sequence" indicatorMode="shownWhileFocused" id="jAa-1c-kLi">
                                            <connections>
                                                <action selector="RIRPickerSelected:" destination="AgC-eL-Hgc" id="PuY-un-WCP"/>
                                            </connections>
                                        </picker>
                                        <button width="1" alignment="center" verticalAlignment="bottom" title="Save" id="i2i-Gf-47X">
                                            <connections>
                                                <action selector="BtnHidePickerTapped" destination="AgC-eL-Hgc" id="IYA-ld-ZZV"/>
                                            </connections>
                                        </button>
                                    </items>
                                </group>
                            </items>
                        </group>
                        <label width="1" height="1" alignment="center" verticalAlignment="center" hidden="YES" text="Open an exercise to begin..." textAlignment="center" numberOfLines="0" id="i03-5r-NRV"/>
                        <label alignment="center" verticalAlignment="center" text="Loading..." id="yoj-5W-wGS"/>
                        <button width="1" alignment="center" verticalAlignment="center" title="Open workout" id="kKi-2g-Vei">
                            <connections>
                                <action selector="BtnOpenWorkoutClicked" destination="AgC-eL-Hgc" id="P3n-Yx-Cbx"/>
                            </connections>
                        </button>
                    </items>
                    <edgeInsets key="margins" left="5" right="5" top="2" bottom="2"/>
                    <connections>
                        <outlet property="BtnFinishAndSave" destination="ndD-2A-d9P" id="1v6-Iv-1Uk"/>
                        <outlet property="BtnFinishExercise" destination="xXR-bZ-uJP" id="4TM-Hk-fyc"/>
                        <outlet property="BtnHidePicker" destination="i2i-Gf-47X" id="brE-Vo-6T3"/>
                        <outlet property="BtnHideTimer" destination="Ti7-Sn-APL" id="mYb-n2-UM8"/>
                        <outlet property="BtnNextExercise" destination="WOw-ov-KhS" id="zY9-e7-BDJ"/>
                        <outlet property="BtnOpenWorkout" destination="kKi-2g-Vei" id="pTh-i9-g5m"/>
                        <outlet property="BtnSaveSet" destination="gUg-jT-2nB" id="5re-Yv-a10"/>
                        <outlet property="GroupPicker" destination="btS-fx-LQq" id="T02-6R-6a2"/>
                        <outlet property="GroupReps" destination="Ocz-UM-DMi" id="FDB-uB-BaX"/>
                        <outlet property="GroupTimer" destination="YMt-f3-aCe" id="4b3-Np-A7J"/>
                        <outlet property="GroupWeight" destination="uZA-QO-qpS" id="O30-EO-s3H"/>
                        <outlet property="LblExerciseName" destination="zMQ-iu-jci" id="Lc3-WY-Hmp"/>
                        <outlet property="LblLoading" destination="yoj-5W-wGS" id="gX2-oT-ZWP"/>
                        <outlet property="LblReps" destination="CyQ-c2-5Nk" id="EWr-tg-Zgj"/>
                        <outlet property="LblRepsValue" destination="4d1-Zt-8Vm" id="K7B-bs-7Gp"/>
                        <outlet property="LblWeightValue" destination="0hj-wj-Jib" id="VDI-FU-pCI"/>
                        <outlet property="LblWeighting" destination="i03-5r-NRV" id="0JV-2E-7Md"/>
                        <outlet property="PickerRIR" destination="jAa-1c-kLi" id="19s-Dd-8yt"/>
                        <outlet property="timer" destination="f62-NI-wSO" id="bhc-Ky-LTq"/>
                    </connections>
                </controller>
            </objects>
            <point key="canvasLocation" x="-91" y="-5"/>
        </scene>
        <!--Static Notification Interface Controller-->
        <scene sceneID="AEw-b0-oYE">
            <objects>
                <notificationController id="YCC-NB-fut">
                    <items>
                        <label alignment="left" text="Alert Label" id="XkS-y5-khE"/>
                    </items>
                    <notificationCategory key="notificationCategory" id="JfB-70-Muf"/>
                    <connections>
                        <outlet property="notificationAlertLabel" destination="XkS-y5-khE" id="49B-RR-99y"/>
                        <segue destination="gdX-wl-uQE" kind="relationship" relationship="dynamicNotificationInterface" id="fKh-qV-3T2"/>
                    </connections>
                </notificationController>
            </objects>
            <point key="canvasLocation" x="231" y="0.0"/>
        </scene>
        <!--Notification Controller-->
        <scene sceneID="KIl-fV-djm">
            <objects>
                <controller id="gdX-wl-uQE" customClass="NotificationController"/>
            </objects>
            <point key="canvasLocation" x="462" y="0.0"/>
        </scene>
    </scenes>
</document>
