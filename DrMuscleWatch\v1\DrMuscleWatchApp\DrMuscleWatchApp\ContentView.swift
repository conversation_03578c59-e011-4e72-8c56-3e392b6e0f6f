//
//  ContentView.swift
//  DrMuscleWatchApp
//
//  Created on May 1, 2024
//

import SwiftUI
import CoreData

struct ContentView: View {
    // Access the managed object context
    @Environment(\.managedObjectContext) private var viewContext

    // Access the authentication manager
    @StateObject private var authManager = AuthenticationManager.shared

    // Fetch request for workouts
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Workout.timestamp, ascending: false)],
        animation: .default)
    private var workouts: FetchedResults<Workout>

    var body: some View {
        Group {
            if authManager.isAuthenticated {
                // Main app content when authenticated
                HomeView()
            } else {
                // Login view when not authenticated
                LoginView()
                    .environmentObject(authManager)
            }
        }
    }
}

/// Home view for the authenticated user
struct HomeView: View {
    // Access the managed object context
    @Environment(\.managedObjectContext) private var viewContext

    // Access the authentication manager
    @EnvironmentObject private var authManager: AuthenticationManager

    // Fetch request for workouts
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Workout.timestamp, ascending: false)],
        animation: .default)
    private var workouts: FetchedResults<Workout>

    var body: some View {
        VStack {
            Text("Dr. Muscle")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.white)

            Spacer().frame(height: 20)

            Text("Watch App")
                .font(.headline)
                .foregroundColor(.gray)

            // Display the number of workouts in Core Data (for testing)
            Text("\(workouts.count) workouts stored")
                .font(.caption)
                .foregroundColor(.gray)
                .padding(.top, 10)

            // Display the user's name if available
            if let firstName = authManager.currentUser?.firstName {
                Text("Welcome, \(firstName)!")
                    .font(.body)
                    .foregroundColor(.white)
                    .padding(.top, 10)
            }

            // Sign out button
            Button("Sign Out") {
                authManager.clearAuthState()
            }
            .padding(.top, 20)
        }
        .padding()
    }
}

#Preview {
    ContentView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
        .environmentObject(AuthenticationManager.shared)
}
