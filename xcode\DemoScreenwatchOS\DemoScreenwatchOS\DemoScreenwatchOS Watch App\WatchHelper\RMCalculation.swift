//
//  RMCalculation.swift
//  DemoScreenwatchOS Watch App
//
//  Created by Dotnet on 16/01/25.
//

import Foundation
import SQLite
import SwiftUI

// MARK: - Workout Log Serie Model Reference

/// Struct that holds data related to workout series.
struct WorkoutLogSerieModelRef {
    var lastTimeSet: String = ""
    var setTitle: String = ""
    var isFirstWorkSet: Bool = false
    var headerTitle: String = ""
    var isMaxChallenge: Bool = false
}

// MARK: - Helper Functions

/// Coefficient used for the 1RM (One Repetition Maximum) calculation.
func coefficient() -> Double {
    return 0.03921568
}

/// Computes the One Rep Max (1RM) based on the weight lifted and number of reps.
func computeOneRM(weight: Double, reps: Int) -> Double {
    guard reps > 0 else { return 0.0 } // Ensure reps are greater than zero to avoid invalid calculations
    return (coefficient() * Double(reps)) * weight + weight
}

// MARK: - Main Calculation Function

/// Calculates the percentage difference between current and previous One Rep Max (1RM).
/// This is used to track progression in the workout.
func calculateSetTitle(
    models: inout WorkoutLogSerieModelRef, // Reference to the workout log model
    currentReps: Int,                     // Current number of repetitions
    currentWeight: Double,                // Current weight lifted
    lastReps: Int,                        // Last number of repetitions
    lastWeight: Double,                   // Last weight lifted
    isKg: Bool,                           // Flag indicating if the weight is in kilograms
    userBodyWeight: Double,               // User's body weight for adjusted calculations
    isWeighted: Bool,                     // Flag indicating if the exercise is weighted
    isBodyweight: Bool,                   // Flag indicating if the exercise is bodyweight-based
    isAssisted: Bool                      // Flag indicating if the exercise is assisted
) -> Double {
    
    // MARK: - Variables
    var currentRM = 0.0  // Current One Rep Max (1RM)
    var lastOneRM = 0.0  // Last One Rep Max (1RM)
    var percentage: Double = 0.0 // Percentage change in 1RM between the sets

    // MARK: - Calculate Current 1RM

    // Adjust current weight by adding user body weight if the exercise is weighted
    let adjustedCurrentWeight = currentWeight + (isWeighted ? userBodyWeight : 0.0)
    currentRM = computeOneRM(weight: adjustedCurrentWeight, reps: currentReps)

    // MARK: - Calculate Last 1RM

    // Adjust last weight by adding user body weight if the exercise was weighted
    let adjustedLastWeight = lastWeight + (isWeighted ? userBodyWeight : 0.0)
    lastOneRM = computeOneRM(weight: adjustedLastWeight, reps: lastReps)

    // MARK: - Calculate Percentage Difference

    if lastOneRM > 0 {
        // Calculate the percentage difference between the current and last 1RM
        percentage = ((currentRM - lastOneRM) * 100) / lastOneRM
    }

    // Update the set title with the calculated percentage change
    models.setTitle = "Today: \(percentage >= 0 ? "+" : "")\(String(format: "%.2f", percentage))%"
    
    // Return the calculated percentage for further use
    return percentage
}
