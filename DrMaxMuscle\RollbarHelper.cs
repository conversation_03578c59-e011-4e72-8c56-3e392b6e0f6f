﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Resx;
using Rollbar;
using Rollbar.DTOs;
using Exception = System.Exception;

namespace DrMaxMuscle
{
    /// <summary>
    /// Class RollbarHelper.
    /// A utility class aiding in Rollbar SDK usage.
    /// </summary>
    public static class RollbarHelper
    {
        public static readonly TimeSpan RollbarTimeout = TimeSpan.FromSeconds(10);

        /// <summary>
        /// Registers for global exception handling.
        /// </summary>
        public static void RegisterForGlobalExceptionHandling()
        {
            #if !DEBUG
            AppDomain.CurrentDomain.UnhandledException += (sender, args) =>
            {
                // Check if the exception message contains "You are not connected to the Google Play App store"
                if (args.ExceptionObject is Exception exception && exception.Message.Contains("You are not connected to the Google Play App store"))
                {
                    // Ignore this specific exception
                    Console.WriteLine("Ignored exception: " + exception.Message);
                    return;
                }

                var newExc = new System.Exception(
                  "CurrentDomainOnUnhandledException",
                  args.ExceptionObject as System.Exception
                );

                // Capture the exception with Rollbar and log telemetry:
                RollbarInfrastructure.Instance.TelemetryCollector.Capture(
                    new Telemetry(
                        TelemetrySource.Client,
                        TelemetryLevel.Error,
                        new ErrorTelemetry(newExc)
                    )
                );

                RollbarLocator
                  .RollbarInstance
                  .AsBlockingLogger(RollbarTimeout)
                  .Critical(newExc);
            };
            #endif
            TaskScheduler.UnobservedTaskException += (sender, args) =>
            {
                //Uncomment it after unhandled exception
                //if (args.Exception.Message.Contains("You are not connected to the Google Play App store"))
                //{
                //    // Ignore this specific exception
                //    Console.WriteLine("Ignored exception: " + args.Exception.Message);
                //    return;
                //}
                //var newExc = new ApplicationException(
                //  "TaskSchedulerOnUnobservedTaskException",
                //  args.Exception
                //);
                //RollbarInfrastructure.Instance.TelemetryCollector.Capture(
                //    new Telemetry(
                //        TelemetrySource.Client,
                //        TelemetryLevel.Error,
                //        new ErrorTelemetry(newExc)
                //    )
                //);

                //RollbarLocator
                //  .RollbarInstance
                //  .AsBlockingLogger(RollbarTimeout)
                //  .Critical(newExc);
            };
        }

        /// <summary>
        /// Configures the Rollbar singleton-like infrastructure.
        /// </summary>
        public static void ConfigureRollbar()
        {
            // Create Rollbar configuration:
            RollbarInfrastructureConfig rollbarInfrastructureConfig =
              new RollbarInfrastructureConfig(
                "d8bb0e7151dd424bb9da5fbfeddaa73f",
                "Production"
              );

            // Set data security options:
            // Create Data Security Options
            RollbarDataSecurityOptions dataSecurityOptions = new RollbarDataSecurityOptions();
            dataSecurityOptions.ScrubFields = new string[]
            {
    "d8bb0e7151dd424bb9da5fbfeddaa73f",
    "DrMuscle",
            };

            // Reconfigure Data Security Options
            rollbarInfrastructureConfig
                .RollbarLoggerConfig
                .RollbarDataSecurityOptions
                .Reconfigure(dataSecurityOptions);

            // Set Payload Addition Options
            RollbarPayloadAdditionOptions payloadAdditionOptions = new RollbarPayloadAdditionOptions();
            payloadAdditionOptions.Person = new Person("007")
            {
                Email = LocalDBManager.Instance.GetDBSetting("email")?.Value,
                UserName = LocalDBManager.Instance.GetDBSetting("email")?.Value
            };
            try
            {
                var _version = (IVersionInfoService)MauiProgram.ServiceProvider.GetService(typeof(IVersionInfoService));
                payloadAdditionOptions.CodeVersion = _version.GetVersionInfo().ToString();

            }
            catch (Exception ex)
            {

            }


            // Create and set up Telemetry Options
            RollbarTelemetryOptions telemetryOptions = new RollbarTelemetryOptions(true, 60)
            {
                TelemetryEnabled = true,
                TelemetryAutoCollectionInterval = TimeSpan.FromSeconds(1),
                TelemetryAutoCollectionTypes = TelemetryType.Network | TelemetryType.Error | TelemetryType.Navigation | TelemetryType.Dom | TelemetryType.Manual,
                TelemetryQueueDepth = 100
            };
            rollbarInfrastructureConfig.RollbarTelemetryOptions
                .Reconfigure(telemetryOptions);



            // Reconfigure Payload Addition Options in Rollbar Logger Config
            rollbarInfrastructureConfig
                .RollbarLoggerConfig
                .RollbarPayloadAdditionOptions
                .Reconfigure(payloadAdditionOptions);

            // Initialize Rollbar
            RollbarInfrastructure.Instance.Init(rollbarInfrastructureConfig);
        }

        /// <summary>
        /// Configures Rollbar telemetry settings.
        /// </summary>
      


        /// <summary>
        /// Called when Rollbar internal event is detected.
        /// </summary>
        private static void OnRollbarInternalEvent(object sender, RollbarEventArgs e)
        {
            Console.WriteLine(e.TraceAsString());

            // Capture telemetry when internal Rollbar events occur:
            RollbarInfrastructure.Instance.TelemetryCollector.Capture(
                new Telemetry(
                    TelemetrySource.Client,
                    TelemetryLevel.Info,
                    new ManualTelemetry(new Dictionary<string, object> { { "event", e.TraceAsString() } })
                )
            );

            RollbarApiErrorEventArgs apiErrorEvent = e as RollbarApiErrorEventArgs;
            if (apiErrorEvent != null)
            {
                // Handle Rollbar API communication error...
                return;
            }

            CommunicationEventArgs commEvent = e as CommunicationEventArgs;
            if (commEvent != null)
            {
                // Handle Rollbar API communication event...
                return;
            }

            CommunicationErrorEventArgs commErrorEvent = e as CommunicationErrorEventArgs;
            if (commErrorEvent != null)
            {
                // Handle communication error...
                return;
            }

            InternalErrorEventArgs internalErrorEvent = e as InternalErrorEventArgs;
            if (internalErrorEvent != null)
            {
                // Handle internal error...
                return;
            }
        }
    }
}
 
