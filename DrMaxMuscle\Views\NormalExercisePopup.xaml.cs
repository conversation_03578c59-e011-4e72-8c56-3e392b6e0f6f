﻿using CommunityToolkit.Maui.Views;
using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Acr.UserDialogs;
using DrMaxMuscle.Layout;
using System.Linq;
using DrMaxMuscle.Message;
using DrMaxMuscle.Screens.Workouts;
using DrMaxMuscle.Constants;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using RGPopup.Maui.Pages;
using RGPopup.Maui.Services;
using Microsoft.Maui.Controls;
using DrMaxMuscle.Utility;
using CommunityToolkit.Maui.Core;

namespace DrMaxMuscle.Views;

public partial class NormalExercisePopup : Popup
{
    bool _isbodyweight = false;
    public delegate void TaskCompletedCallBack(string taskResult);
    TaskCompletedCallBack _taskCompletedCallBack;
    public Page _kenkoPage { get; set; }
    public List<BandsModel> _availabelBands { get; set; }
    ExerciseWorkSetsModel _exercise;
    public NormalExercisePopup(string videoUrl, string titleText, string popupText, string placeholder, ExerciseWorkSetsModel m, bool isBodyweight)
    {
        InitializeComponent();

        var screenWidth = DeviceDisplay.MainDisplayInfo.Width / DeviceDisplay.MainDisplayInfo.Density;
        var screenheight = DeviceDisplay.MainDisplayInfo.Height / DeviceDisplay.MainDisplayInfo.Density;

        //this.Size = new Size(screenWidth * 0.99);

        //_taskCompletedCallBack = taskCompletedCallBack;

        if (!string.IsNullOrEmpty(videoUrl))
            {
                videoPlayer.Source = videoUrl;
            }
            else
            {
                videoPlayer.IsVisible = false;
            }
            _exercise = m;
            LblTitle.Text = titleText;
            LblDesc.Text = popupText;
            EntryWeight.Placeholder = placeholder;
            if (isBodyweight)
                EntryWeight.TextChanged += RepsPopup_OnTextChanged;
            else
                EntryWeight.TextChanged += BodyweightPopup_OnTextChanged;
            _isbodyweight = isBodyweight;
            if (m.EquipmentId == 7)
            {
                _availabelBands = Utility.HelperClass.GetAvailableBands(LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg");
                if (_availabelBands.Count > 0)
                {
                    PickerCM.IsVisible = true;
                    EntryWeight.IsVisible = false;
                    PickerCM.SelectedIndex = 0;
                    LblDesc.Text = "Which can you do easily 5 times?";
                    PickerCM.ItemsSource = _availabelBands.Select(x => x.BandColor);

                    if (Device.RuntimePlatform.Equals(Device.Android))
                    {
                        PickerCM.HeightRequest = 150;
                        PickerCM.Margin = new Thickness(15, 0);
                    }
                    else
                    {
                        //PickerStack.Margin = new Thickness(7, 0);
                    }
                }
        }
        Microsoft.Maui.Handlers.EntryHandler.Mapper.AppendToMapping(nameof(Entry), (handler, view) =>
        {
#if __IOS__                        
            if (view == EntryWeight )
            {

                try 
	            {	   
                    if(handler.PlatformView != null)
                    {
		                var toolbar = new UIKit.UIToolbar(new System.Drawing.RectangleF(0.0f, 0.0f, 50.0f, 44.0f));
                        toolbar.BackgroundColor = UIKit.UIColor.LightGray; // Set the color you prefer
                        var doneButton = new UIKit.UIBarButtonItem(UIKit.UIBarButtonSystemItem.Done, delegate
                        {
                            handler.PlatformView.ResignFirstResponder();
                            BtnDoneClicked(EntryWeight, EventArgs.Empty);
                        });

                        toolbar.Items = new UIKit.UIBarButtonItem[] {
                                new UIKit.UIBarButtonItem (UIKit.UIBarButtonSystemItem.FlexibleSpace),
                                    doneButton
                                };

                        handler.PlatformView.InputAccessoryView = toolbar;
                    }
	            }
	            catch (global::System.Exception ex)
	            {

	            }

            }
#endif
        });
        this.Opened += Popup_Opened;
    }

    private async void Popup_Opened(object sender, PopupOpenedEventArgs e)
    {
        //base.OnAppearing();
        try
        {
            await Task.Delay(500); // Short delay

            Dispatcher.Dispatch(() =>
            {
                if (EntryWeight.IsVisible && EntryWeight.Handler != null)
                {
                    EntryWeight.Focus();
                }
            });
        }
        catch (Exception ex)
        {
            // Optional: log or debug here
        }
    }

    async void BtnDoneClicked(System.Object sender, System.EventArgs e)
    {
        try
        {
            if (PickerCM.IsVisible && _availabelBands?.Count > 0)
            {
                if (PickerCM.SelectedIndex > -1)
                {
                    var bands = _availabelBands[PickerCM.SelectedIndex].Weight;
                    EntryWeight.Text = Convert.ToString(bands).ReplaceWithDot();
                }
            }
        }
        catch (Exception ex)
        {

        }
        if (string.IsNullOrEmpty(EntryWeight.Text) || string.IsNullOrWhiteSpace(EntryWeight.Text))
            return;
        try
        {
            var weight = int.Parse(EntryWeight.Text);
            if (weight < 1)
            {


                if (_kenkoPage is KenkoSingleExercisePage)
                {
                    await HelperClass.DisplayCustomPopupForResult("Error",
                        _isbodyweight ? "Please enter valid reps" : "Please enter valid weight","Ok","");
                    // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                    // {
                    //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //     Message = _isbodyweight ? "Please enter valid reps" : "Please enter valid weight",
                    //     Title = "Error"
                    // });
                }
                if (_kenkoPage is KenkoChooseYourWorkoutExercisePage)
                {
                    await this.CloseAsync();
                    //await MauiProgram.SafeDismissTopPopup();
                    //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
                    //    await PopupNavigation.Instance.PopAsync();
                    ((KenkoChooseYourWorkoutExercisePage)(_kenkoPage)).AskForSwap(_exercise);
                }

                return;
            }
        }
        catch (Exception ex)
        {

        }

        var bodyweight = EntryWeight.Text;
        await this.CloseAsync();
        //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
        //    await PopupNavigation.Instance.PopAsync() ;


        if (_kenkoPage is KenkoSingleExercisePage)
            ((KenkoSingleExercisePage)(_kenkoPage)).FinishSetup(_exercise, bodyweight, _isbodyweight);
        if (_kenkoPage is KenkoChooseYourWorkoutExercisePage)
            ((KenkoChooseYourWorkoutExercisePage)(_kenkoPage)).FinishSetup(_exercise, bodyweight, _isbodyweight);

    }

    async void BtnCancelClicked(System.Object sender, System.EventArgs e)
    {
        if (_kenkoPage is KenkoSingleExercisePage)
            ((KenkoSingleExercisePage)(_kenkoPage)).CancelClick(_exercise);
        if (_kenkoPage is KenkoChooseYourWorkoutExercisePage)
            ((KenkoChooseYourWorkoutExercisePage)(_kenkoPage)).CancelClick(_exercise);
        //await MauiProgram.SafeDismissTopPopup();

        await this.CloseAsync();
        //if (PopupNavigation.Instance.PopupStack?.Count() > 0) 
        //    PopupNavigation.Instance.PopAsync() ;

    }

    protected void BodyweightPopup_OnTextChanged(object obj, TextChangedEventArgs args)
    {
        try
        {

            Entry entry = (Entry)obj;
            const string textRegex = @"^\d+(?:[\.,]\d{0,5})?$";
            var text = entry.Text.Replace(",", ".");
            bool IsValid = Regex.IsMatch(text, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
            if (IsValid == false && !string.IsNullOrEmpty(entry.Text))
            {
                double result;
                entry.Text = entry.Text.Substring(0, entry.Text.Length - 1);
                double.TryParse(entry.Text, out result);
                entry.Text = result.ToString();
            }

        }
        catch (Exception ex)
        {

        }
    }

    protected void RepsPopup_OnTextChanged(object obj, TextChangedEventArgs args)
    {
        try
        {

            Entry entry = (Entry)obj;
            const string textRegex = @"^\d+(?:)?$";
            var text = entry.Text.Replace(",", ".");
            bool IsValid = Regex.IsMatch(text, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
            if (IsValid == false && !string.IsNullOrEmpty(entry.Text))
            {
                double result;
                entry.Text = entry.Text.Substring(0, entry.Text.Length - 1);
                double.TryParse(entry.Text, out result);
                entry.Text = result.ToString();
            }

        }
        catch (Exception ex)
        {

        }
    }

    void EntryWeight_Completed(System.Object sender, System.EventArgs e)
    {
        BtnDoneClicked(sender, e);
    }
}
