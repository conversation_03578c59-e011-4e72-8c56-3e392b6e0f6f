﻿<?xml version="1.0" encoding="utf-8" ?>
<toolkit:Popup xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
               xmlns:t="clr-namespace:DrMaxMuscle.Layout"
               CanBeDismissedByTappingOutsideOfPopup="True" Color="Transparent"
               xmlns:app="clr-namespace:DrMaxMuscle.Constants"
                xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
             x:Class="DrMaxMuscle.Views.CustomPopup" HorizontalOptions="Center" VerticalOptions="Center">

        <Frame
            IsVisible="false"
            x:Name="IOSFrame"
            IsClippedToBounds="true"
            CornerRadius="14"
            VerticalOptions="FillAndExpand"
            HorizontalOptions="FillAndExpand"
            BorderColor="Transparent"
            Margin="0"
            Padding="0"
            Style="{StaticResource GradientFrameStyleBlue}" >
           
            <StackLayout HorizontalOptions="FillAndExpand"
                VerticalOptions="FillAndExpand"
                Orientation="Vertical"  Spacing="0"
            Padding="0"
            Margin="0">

                <StackLayout
                x:Name="labelView"
                HorizontalOptions="FillAndExpand"
                VerticalOptions="FillAndExpand"
                Padding="0,15,0,0"
                Spacing="0">

                <Grid RowSpacing="2" Padding="{OnPlatform Android='8,0,8,8',iOS='17,8,17,4'}">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                
                <Label
                    Padding="0" Grid.Row="0"
                    Margin="20,0"
                    x:Name="titleLabelIOS"
                    Text=""
                    VerticalOptions="StartAndExpand"
                    FontSize="20"
                    FontAttributes="Bold"
                    VerticalTextAlignment="Center"
                    TextColor="White"
                    HorizontalTextAlignment="Center"
                    HorizontalOptions="FillAndExpand"
                     />
                <Label
                    Padding="0" Grid.Row="1"
                    Margin="20,15,20,0"
                    VerticalOptions="StartAndExpand"
                    x:Name="bodyLabelIOS"
                    Text=""
                    FontSize="18"
                    VerticalTextAlignment="Center"
                    TextColor="White"
                    HorizontalTextAlignment="Center"
                    HorizontalOptions="FillAndExpand"/>
                        </Grid>
     
            </StackLayout>

                <StackLayout
                    x:Name="horizontalButtonsView"
                    BackgroundColor="Transparent"
                    Padding="0,1,0,0"
                    Margin="-5,15,-5,-5"
                    VerticalOptions="End"
                    HorizontalOptions="Fill"
                    Orientation="Horizontal"
                    Spacing="0">

                     <t:DrMuscleButton
                        HeightRequest="60"
                        Text="Ok"
                        Padding="10, 0"
                        CornerRadius="0"
                        x:Name="cancelBtnIOS"
                        FontSize="18"
                        LineBreakMode="TailTruncation"
                        TextTransform="None"
                        HorizontalOptions="FillAndExpand"
                        Clicked="Cancel_Btn_ClickedIOS"
                        VerticalOptions="CenterAndExpand"
                        BorderColor="Gray"
                        BorderWidth="1"
                        BackgroundColor="Transparent"
                        TextColor="White" />

                    <t:DrMuscleButton
                        HeightRequest="60"
                        Text=""
                        CornerRadius="0"
                        x:Name="okBtnIOS"
                        Padding="10, 0"
                        FontSize="18"
                        FontAttributes="Bold"
                        LineBreakMode="TailTruncation"
                        TextTransform="None"
                        HorizontalOptions="FillAndExpand"
                        BorderColor="Gray"
                        BorderWidth="1"
                        Clicked="OK_Btn_ClickedIOS"
                        VerticalOptions="CenterAndExpand"
                        BackgroundColor="Transparent"
                        TextColor="White" />
                   
                </StackLayout>

                <StackLayout
                    x:Name="verticalButtonsView"
                    BackgroundColor="Transparent"
                    VerticalOptions="FillAndExpand"
                    HorizontalOptions="FillAndExpand"
                    Orientation="Vertical"
                    Padding="0"
                    Margin="0"
                    IsVisible="False"
                    Spacing="{OnPlatform Android='0',iOS='2'}">

                      <t:DrMuscleButton
                        HeightRequest="45"
                        Text=""
                        CornerRadius="0"
                        x:Name="okBtnVerticalIOS"
                        Padding="0"
                        FontSize="18"
                        HorizontalOptions="FillAndExpand"
                        BorderColor="Transparent"
                        LineBreakMode="WordWrap"
                        Clicked="OK_Btn_ClickedIOS"
                        VerticalOptions="CenterAndExpand"
                        BackgroundColor="Transparent"
                        TextColor="White" />

                     <t:DrMuscleButton
                        HeightRequest="45"
                        Text=""
                        Padding="0"
                        CornerRadius="0"
                        x:Name="cancelBtnVerticalIOS"
                        FontSize="18"
                        HorizontalOptions="FillAndExpand"
                        LineBreakMode="WordWrap"
                        Clicked="Cancel_Btn_ClickedIOS"
                        VerticalOptions="CenterAndExpand"
                        BorderColor="Transparent"
                        BackgroundColor="Transparent"
                        TextColor="White" />                 
                   
                </StackLayout>

            </StackLayout>

        </Frame>

</toolkit:Popup>

