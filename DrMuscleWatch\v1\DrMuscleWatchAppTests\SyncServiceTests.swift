import XCTest
@testable import DrMuscleWatchApp

class SyncServiceTests: XCTestCase {

    // Mock API client for testing
    class MockAPIClient: DrMuscleAPIClient {
        var addWorkoutLogSerieCalled = false
        var addWorkoutLogSerieModel: WorkoutLogSerieModel?
        var shouldThrowError = false
        var errorToThrow: Error?

        override func addWorkoutLogSerie(model: WorkoutLogSerieModel) async throws -> BooleanModel {
            addWorkoutLogSerieCalled = true
            addWorkoutLogSerieModel = model

            if shouldThrowError {
                if let error = errorToThrow {
                    throw error
                } else {
                    throw NSError(domain: "MockError", code: 0, userInfo: [NSLocalizedDescriptionKey: "Mock API error"])
                }
            }

            return BooleanModel(result: true)
        }

        override func saveWorkoutV3(model: SaveWorkoutModel) async throws -> BooleanModel {
            if shouldThrowError {
                if let error = errorToThrow {
                    throw error
                } else {
                    throw NSError(domain: "MockError", code: 0, userInfo: [NSLocalizedDescriptionKey: "Mock API error"])
                }
            }

            return BooleanModel(result: true)
        }
    }

    // Mock storage service for testing
    class MockStorageService: StorageService {
        var mockSetLogs: [SetLog] = []
        var markSetLogAsSyncedCalled = false
        var markedSetLog: SetLog?
        var shouldThrowError = false

        override func getSetLogsNeedingSync() throws -> [SetLog] {
            if shouldThrowError {
                throw NSError(domain: "MockError", code: 0, userInfo: [NSLocalizedDescriptionKey: "Mock storage error"])
            }

            return mockSetLogs
        }

        override func markSetLogAsSynced(_ setLog: SetLog) throws {
            if shouldThrowError {
                throw NSError(domain: "MockError", code: 0, userInfo: [NSLocalizedDescriptionKey: "Mock storage error"])
            }

            markSetLogAsSyncedCalled = true
            markedSetLog = setLog
        }
    }

    // Test that sync is triggered when connectivity is restored
    func testSyncTriggeredOnRegainingConnection() async {
        // Create mock dependencies
        let mockAPIClient = MockAPIClient()
        let mockStorageService = MockStorageService()

        // Create a mock set log
        let mockSetLog = SetLog(context: mockStorageService.viewContext)
        mockSetLog.exerciseID = 123
        mockSetLog.workoutID = 456
        mockSetLog.reps = 10
        mockSetLog.weight = 100.0
        mockSetLog.weightUnit = "kg"
        mockSetLog.timestamp = Date()
        mockSetLog.needsSync = true

        mockStorageService.mockSetLogs = [mockSetLog]

        // Create the sync service with mock dependencies
        let syncService = SyncService(apiClient: mockAPIClient, storageService: mockStorageService)

        // Simulate connectivity change
        await syncService.networkStatusChanged(isOnline: true)

        // Verify that the API client was called to sync the set log
        XCTAssertTrue(mockAPIClient.addWorkoutLogSerieCalled, "API client should be called to sync set log")
        XCTAssertNotNil(mockAPIClient.addWorkoutLogSerieModel, "API client should receive a model")
        XCTAssertEqual(mockAPIClient.addWorkoutLogSerieModel?.reps, 10, "API client should receive the correct reps")
        XCTAssertEqual(mockAPIClient.addWorkoutLogSerieModel?.weight.value, 100.0, "API client should receive the correct weight")

        // Verify that the set log was marked as synced
        XCTAssertTrue(mockStorageService.markSetLogAsSyncedCalled, "Set log should be marked as synced")
        XCTAssertEqual(mockStorageService.markedSetLog, mockSetLog, "The correct set log should be marked as synced")
    }

    // Test that sync is triggered on app launch
    func testSyncTriggeredOnAppLaunch() async {
        // Create mock dependencies
        let mockAPIClient = MockAPIClient()
        let mockStorageService = MockStorageService()

        // Create a mock set log
        let mockSetLog = SetLog(context: mockStorageService.viewContext)
        mockSetLog.exerciseID = 123
        mockSetLog.workoutID = 456
        mockSetLog.reps = 10
        mockSetLog.weight = 100.0
        mockSetLog.weightUnit = "kg"
        mockSetLog.timestamp = Date()
        mockSetLog.needsSync = true

        mockStorageService.mockSetLogs = [mockSetLog]

        // Create the sync service with mock dependencies
        let syncService = SyncService(apiClient: mockAPIClient, storageService: mockStorageService)

        // Simulate app launch with connectivity
        syncService.isOnline = true
        await syncService.attemptSync()

        // Verify that the API client was called to sync the set log
        XCTAssertTrue(mockAPIClient.addWorkoutLogSerieCalled, "API client should be called to sync set log")
        XCTAssertNotNil(mockAPIClient.addWorkoutLogSerieModel, "API client should receive a model")
        XCTAssertEqual(mockAPIClient.addWorkoutLogSerieModel?.reps, 10, "API client should receive the correct reps")
        XCTAssertEqual(mockAPIClient.addWorkoutLogSerieModel?.weight.value, 100.0, "API client should receive the correct weight")

        // Verify that the set log was marked as synced
        XCTAssertTrue(mockStorageService.markSetLogAsSyncedCalled, "Set log should be marked as synced")
        XCTAssertEqual(mockStorageService.markedSetLog, mockSetLog, "The correct set log should be marked as synced")
    }

    // Test successful sync of set logs
    func testSuccessfulSyncUpdatesLocalFlag() async {
        // Create mock dependencies
        let mockAPIClient = MockAPIClient()
        let mockStorageService = MockStorageService()

        // Create a mock set log
        let mockSetLog = SetLog(context: mockStorageService.viewContext)
        mockSetLog.exerciseID = 123
        mockSetLog.workoutID = 456
        mockSetLog.reps = 10
        mockSetLog.weight = 100.0
        mockSetLog.weightUnit = "kg"
        mockSetLog.timestamp = Date()
        mockSetLog.needsSync = true

        mockStorageService.mockSetLogs = [mockSetLog]

        // Create the sync service with mock dependencies
        let syncService = SyncService(apiClient: mockAPIClient, storageService: mockStorageService)

        // Simulate sync
        await syncService.syncSetLog(mockSetLog)

        // Verify that the API client was called to sync the set log
        XCTAssertTrue(mockAPIClient.addWorkoutLogSerieCalled, "API client should be called to sync set log")

        // Verify that the set log was marked as synced
        XCTAssertTrue(mockStorageService.markSetLogAsSyncedCalled, "Set log should be marked as synced")
        XCTAssertEqual(mockStorageService.markedSetLog, mockSetLog, "The correct set log should be marked as synced")
    }

    // Test handling of API errors during sync
    func testSyncHandlesAPIErrorsGracefully() async {
        // Create mock dependencies
        let mockAPIClient = MockAPIClient()
        mockAPIClient.shouldThrowError = true
        mockAPIClient.errorToThrow = NSError(domain: NSURLErrorDomain, code: NSURLErrorNotConnectedToInternet, userInfo: [NSLocalizedDescriptionKey: "The Internet connection appears to be offline."])

        let mockStorageService = MockStorageService()

        // Create a mock set log
        let mockSetLog = SetLog(context: mockStorageService.viewContext)
        mockSetLog.exerciseID = 123
        mockSetLog.workoutID = 456
        mockSetLog.reps = 10
        mockSetLog.weight = 100.0
        mockSetLog.weightUnit = "kg"
        mockSetLog.timestamp = Date()
        mockSetLog.needsSync = true

        mockStorageService.mockSetLogs = [mockSetLog]

        // Create the sync service with mock dependencies
        let syncService = SyncService(apiClient: mockAPIClient, storageService: mockStorageService)

        // Simulate sync with API error
        await syncService.syncSetLog(mockSetLog)

        // Verify that the API client was called to sync the set log
        XCTAssertTrue(mockAPIClient.addWorkoutLogSerieCalled, "API client should be called to sync set log")

        // Verify that the set log was NOT marked as synced (since the API call failed)
        XCTAssertFalse(mockStorageService.markSetLogAsSyncedCalled, "Set log should NOT be marked as synced when API call fails")
    }

    // Test that sync sends data in the correct order
    func testSyncSendsDataInOrder() async {
        // Create mock dependencies
        let mockAPIClient = MockAPIClient()
        let mockStorageService = MockStorageService()

        // Create multiple mock set logs with different timestamps
        let mockSetLog1 = SetLog(context: mockStorageService.viewContext)
        mockSetLog1.exerciseID = 123
        mockSetLog1.workoutID = 456
        mockSetLog1.reps = 10
        mockSetLog1.weight = 100.0
        mockSetLog1.weightUnit = "kg"
        mockSetLog1.timestamp = Date().addingTimeInterval(-3600) // 1 hour ago
        mockSetLog1.needsSync = true

        let mockSetLog2 = SetLog(context: mockStorageService.viewContext)
        mockSetLog2.exerciseID = 123
        mockSetLog2.workoutID = 456
        mockSetLog2.reps = 8
        mockSetLog2.weight = 110.0
        mockSetLog2.weightUnit = "kg"
        mockSetLog2.timestamp = Date() // Now
        mockSetLog2.needsSync = true

        mockStorageService.mockSetLogs = [mockSetLog2, mockSetLog1] // Intentionally out of order

        // Create the sync service with mock dependencies
        let syncService = SyncService(apiClient: mockAPIClient, storageService: mockStorageService)

        // Simulate sync
        await syncService.attemptSync()

        // Verify that the API client was called to sync the set logs in the correct order
        // This will be verified by checking the order in which markSetLogAsSynced was called
        // We expect mockSetLog1 (older) to be synced first, then mockSetLog2 (newer)
        XCTAssertTrue(mockStorageService.markSetLogAsSyncedCalled, "Set logs should be marked as synced")
        XCTAssertEqual(mockStorageService.markedSetLog, mockSetLog1, "The older set log should be synced first")
    }
}
