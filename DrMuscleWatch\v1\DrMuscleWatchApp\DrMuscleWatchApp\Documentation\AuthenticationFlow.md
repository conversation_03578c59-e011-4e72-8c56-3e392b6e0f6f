# Authentication Flow

This document describes the authentication flow for the Dr. Muscle Watch app.

## Overview

The Dr. Muscle Watch app uses "Sign in with <PERSON>" for authentication. This provides a secure and user-friendly way for users to authenticate without having to enter their credentials on the small watch screen.

## Components

### AuthenticationManager

The `AuthenticationManager` class is responsible for managing the authentication state and handling the Sign in with Apple flow. It provides methods for:

- Handling the completion of the Sign in with Apple flow
- Storing and retrieving the authentication state
- Clearing the authentication state (signing out)

### LoginViewModel

The `LoginViewModel` class is responsible for managing the login screen's state and initiating the Sign in with Apple flow. It implements the `ASAuthorizationControllerDelegate` and `ASAuthorizationControllerPresentationContextProviding` protocols to handle the Sign in with Apple flow.

### LoginView

The `LoginView` struct is the UI component that displays the login screen, including the Sign in with Apple button. It uses the `LoginViewModel` to manage its state and initiate the Sign in with Apple flow.

### SignInWithAppleButton

The `SignInWithAppleButton` struct is a custom button that mimics the appearance of the standard Sign in with Apple button. It's used in the `LoginView` to provide a consistent and recognizable button for users to tap.

## Flow

1. When the app starts, the `AuthenticationManager` checks if there's a stored authentication token.
2. If there's a token, the user is considered authenticated and is shown the main app content.
3. If there's no token, the user is shown the login screen.
4. When the user taps the Sign in with Apple button, the `LoginViewModel` initiates the Sign in with Apple flow.
5. The system presents the Sign in with Apple dialog, which is handled by the `ASAuthorizationController`.
6. When the user completes the Sign in with Apple flow, the `LoginViewModel` receives a callback with the result.
7. If the flow is successful, the `AuthenticationManager` stores the authentication state and updates the UI to show the main app content.
8. If the flow fails, the `LoginViewModel` displays an error message.

## Error Handling

The authentication flow includes comprehensive error handling to provide a good user experience:

- If the Sign in with Apple flow fails, the user is shown an error message.
- If the API call to sign in with Apple fails, the user is shown an error message.
- If the user cancels the Sign in with Apple flow, the user is returned to the login screen.

## Security

The authentication token is stored securely in the keychain, which provides encryption and protection against unauthorized access. The user's information is stored in UserDefaults, which is less secure but only contains non-sensitive information like the user's name and email.

## Testing

The authentication flow is tested with unit tests that verify:

- The login view displays the Sign in with Apple button
- Tapping the Sign in with Apple button initiates the sign-in process
- The authentication manager can handle a successful sign-in
- The authentication manager can handle a failed sign-in
- The authentication manager can store and retrieve the authentication state

## Future Improvements

- Add support for refreshing the authentication token when it expires
- Add support for handling Apple ID credential revocation
- Add support for handling Apple ID credential changes (e.g., email address changes)
- Add support for handling Apple ID credential upgrades (e.g., from private email relay to real email)
