import SwiftUI

/// A button that can display either a normal CTA button or a timer countdown
struct TimerButton: View {
    // MARK: - Properties

    let title: String
    let percentage: Double
    let showPercentage: Bool
    let action: () -> Void

    @Binding var isTimerActive: Bool
    @Binding var secondsRemaining: Int

    // MARK: - Initialization

    /// Initialize with constant values (for previews and testing)
    init(
        title: String,
        percentage: Double = 0.0,
        showPercentage: Bool = false,
        isTimerActive: Bool,
        secondsRemaining: Int,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.percentage = percentage
        self.showPercentage = showPercentage
        self.action = action
        self._isTimerActive = .constant(isTimerActive)
        self._secondsRemaining = .constant(secondsRemaining)
    }

    /// Initialize with bindings (for actual use in views)
    init(
        title: String,
        percentage: Double = 0.0,
        showPercentage: Bool = false,
        isTimerActive: Binding<Bool>,
        secondsRemaining: Binding<Int>,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.percentage = percentage
        self.showPercentage = showPercentage
        self.action = action
        self._isTimerActive = isTimerActive
        self._secondsRemaining = secondsRemaining
    }

    // MARK: - Computed Properties

    /// Format the time as "Rest M:SS"
    var formattedTime: String {
        let minutes = secondsRemaining / 60
        let remainingSeconds = secondsRemaining % 60
        return "Rest \(minutes):\(String(format: "%02d", remainingSeconds))"
    }

    /// Get the appropriate button text based on state
    private var buttonText: String {
        if isTimerActive {
            return formattedTime
        } else if showPercentage {
            return "\(title) \(percentage >= 0 ? "+" : "")\(String(format: "%.2f", percentage))%"
        } else {
            return title
        }
    }

    // MARK: - Body

    var body: some View {
        Button(action: action) {
            Text(buttonText)
                .font(AppTheme.ctaButtonTextStyle)
                .foregroundColor(.black)
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            AppTheme.yellowGradientStart,
                            AppTheme.yellowGradientEnd
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                    .cornerRadius(AppTheme.cornerRadius)
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}
