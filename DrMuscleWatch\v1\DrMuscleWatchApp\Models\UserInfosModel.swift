import Foundation

/// Model for user authentication and profile information
struct UserInfosModel: Codable {
    let id: String
    let email: String
    let token: String
    let firstName: String?
    let lastName: String?
    
    enum CodingKeys: String, CodingKey {
        case id = "Id"
        case email = "Email"
        case token = "Token"
        case firstName = "FirstName"
        case lastName = "LastName"
    }
}
