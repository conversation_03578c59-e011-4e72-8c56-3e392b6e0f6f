﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using Acr.UserDialogs;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Layout;
using DrMuscleWebApiSharedModel;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Screens.Me;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Views;
using RGPopup.Maui.Services;
using DrMaxMuscle.Utility;

namespace DrMaxMuscle.Screens.Workouts;

public partial class ChooseYourWorkoutTemplateInGroup : ContentPage
{
    private List<WorkoutTemplateModel> workouts;
    public ObservableCollection<WorkoutTemplateModel> workoutItems = new ObservableCollection<WorkoutTemplateModel>();
    bool isAppliedSettings = false;

    public ChooseYourWorkoutTemplateInGroup()
    {
        InitializeComponent();

        WorkoutListView.ItemsSource = workoutItems;
        WorkoutListView.ItemTapped += WorkoutListView_ItemTapped;

        RefreshLocalized();
        MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
        {
            RefreshLocalized();
        });
        OnBeforeShow();
    }

    private void RefreshLocalized()
    {
        Title = AppResources.ChooseWorkout;

    }

    public async void OnBeforeShow()
    {
        
        DependencyService.Get<IFirebase>().SetScreenName("choose_workout");
        try
        {
            try
            {
                if (workouts != null)
                {

                    workoutItems.Clear();
                }
            }
            catch (Exception ex)
            {

            }
            await UpdateWorkoutList();

            if (LocalDBManager.Instance.GetDBSetting("Equipment") == null)
                LocalDBManager.Instance.SetDBSetting("Equipment", "false");

            if (LocalDBManager.Instance.GetDBSetting("ChinUp") == null)
                LocalDBManager.Instance.SetDBSetting("ChinUp", "true");

            if (LocalDBManager.Instance.GetDBSetting("Dumbbell") == null)
                LocalDBManager.Instance.SetDBSetting("Dumbbell", "true");

            if (LocalDBManager.Instance.GetDBSetting("Plate") == null)
                LocalDBManager.Instance.SetDBSetting("Plate", "true");

            if (LocalDBManager.Instance.GetDBSetting("Pully") == null)
                LocalDBManager.Instance.SetDBSetting("Pully", "true");

            //GetUserWorkoutTemplateResponseModel itemsSource = await DrMuscleRestClient.Instance.GetCustomizedUserWorkout(new EquipmentModel()
            //{
            //    IsEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("Equipment").Value == "true",
            //    IsChinUpBarEnabled = LocalDBManager.Instance.GetDBSetting("ChinUp").Value == "true",
            //    IsPullyEnabled = LocalDBManager.Instance.GetDBSetting("Pully").Value == "true",
            //    IsPlateEnabled = LocalDBManager.Instance.GetDBSetting("Plate").Value == "true"
            //});
            workouts = CurrentLog.Instance.CurrentWorkoutTemplateGroup.WorkoutTemplates;
            try
            {

                if (isAppliedSettings && workouts.First().Exercises.First().Timer != null)
                {
                    if (!string.IsNullOrEmpty(Convert.ToString(workouts.First().Exercises.First().Timer)))
                    {
                        LocalDBManager.Instance.SetDBSetting("timer_remaining", workouts.First().Exercises.First().Timer.ToString());
                        LocalDBManager.Instance.SetDBSetting("timer_autoset", "false");
                        //TODO: MAUI
                        //SetFeaturedTimer();
                    }

                }

            }
            catch (Exception ex)
            {

            }
            await UpdateWorkoutList();
        }
        catch (Exception e)
        {
            ConnectionErrorPopup();
        }
    }

    async Task ConnectionErrorPopup()
    {
        await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                        AppResources.PleaseCheckInternetConnection,"Try again","");
        // await UserDialogs.Instance.AlertAsync(new AlertConfig()
        // {
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     Message = AppResources.PleaseCheckInternetConnection,
        //     Title = AppResources.ConnectionError,
        //     OkText = "Try again"
        // });
    }
    void SetVisibility(IView view, bool isVisible)
    {
        if (view is View mauiView)
        {
            mauiView.IsVisible = isVisible;
        }
    }

    void OnCancelClicked(object sender, System.EventArgs e)
    {
        StackLayout s = ((StackLayout)((Button)sender).Parent);
        SetVisibility(s.Children[0], false);
        SetVisibility(s.Children[1],false);
        SetVisibility(s.Children[2], false);
        SetVisibility(s.Children[3], false);
        SetVisibility(s.Children[4], true);
    }

    void OnContextMenuClicked(object sender, System.EventArgs e)
    {
        StackLayout s = ((StackLayout)((Button)sender).Parent);
        SetVisibility(s.Children[0], true);
        if (CurrentLog.Instance.CurrentWorkoutTemplateGroup != null && (CurrentLog.Instance.CurrentWorkoutTemplateGroup.IsFeaturedProgram || CurrentLog.Instance.CurrentWorkoutTemplateGroup.IsSystemExercise))
        {
            SetVisibility(s.Children[1], false);
            SetVisibility(s.Children[2], false);
        }
        else
        {
            SetVisibility(s.Children[1], true);
            SetVisibility(s.Children[2], true);
        }
        SetVisibility(s.Children[3], true);
        SetVisibility(s.Children[4], false);
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        if (!CurrentLog.Instance.CurrentWorkoutTemplateGroup.IsFeaturedProgram)
        {
            isAppliedSettings = false;
            return;
        }
        //TODO: MAUI
        //if (Navigation.NavigationStack.First() is MePage)
        //{
        //    return;
        //}
        if (App.IsFeaturedPopup)
            return;
        isAppliedSettings = false;

        var ShowRIRPopUp = await HelperClass.DisplayCustomPopup("",$"Change exercise and timer settings for {CurrentLog.Instance.CurrentWorkoutTemplateGroup.Label}?",
        "Change settings",AppResources.Cancel);
        ShowRIRPopUp.ActionSelected += async (sender,action) => {
            if(action == PopupAction.OK){
                isAppliedSettings = true;
                    //Load Settings 
                var response = await DrMuscleRestClient.Instance.UpdateSettingsForProgram(CurrentLog.Instance.CurrentWorkoutTemplateGroup.ProgramId);
                try
                {
                    //TODO: MAUI
                    //SetFeaturedTimer();
                    

                }
                catch (Exception ex)
                {

                }
            }
        };

        App.IsFeaturedPopup = true;
        // ConfirmConfig ShowRIRPopUp = new ConfirmConfig()
        // {
        //     Title = "",
        //     Message = $"Change exercise and timer settings for {CurrentLog.Instance.CurrentWorkoutTemplateGroup.Label}?",
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     OkText = "Change settings",
        //     CancelText = AppResources.Cancel,
        //     OnAction = async (bool ok) =>
        //     {
        //         if (ok)
        //         {
        //             isAppliedSettings = true;
        //             //Load Settings 
        //             var response = await DrMuscleRestClient.Instance.UpdateSettingsForProgram(CurrentLog.Instance.CurrentWorkoutTemplateGroup.ProgramId);
        //             try
        //             {
        //                 //TODO: MAUI
        //                //SetFeaturedTimer();
                        

        //             }
        //             catch (Exception ex)
        //             {

        //             }
        //         }
        //     }
        // };
        
        // UserDialogs.Instance.Confirm(ShowRIRPopUp);
    }

    public async void OnEdit(object sender, EventArgs e)
    {
        try
        {
            var mi = ((Button)sender);
            if (mi.CommandParameter is WorkoutTemplateModel)
            {
                WorkoutTemplateModel m = (WorkoutTemplateModel)mi.CommandParameter;
                CurrentLog.Instance.CurrentWorkoutTemplate = m;
                await Navigation.PushAsync(new AddWorkoutToWorkoutOrderPage());
            }
            else
            {
                WorkoutTemplateGroupModel m = (WorkoutTemplateGroupModel)mi.CommandParameter;
                CurrentLog.Instance.CurrentWorkoutTemplateGroup = m;
                await Navigation.PushAsync(new AddWorkoutToWorkoutOrderPage());

            }
        }
        catch (Exception ex)
        {

        }
    }

    public async void OnRename(object sender, EventArgs e)
    {
        var mi = ((Button)sender);
        if (mi.CommandParameter is WorkoutTemplateModel)
        {
            WorkoutTemplateModel m = (WorkoutTemplateModel)mi.CommandParameter;

            CustomPromptConfig p = new CustomPromptConfig(string.Format("{0} \"{1}\"", AppResources.Rename, m.Label),AppResources.TapToEnterNewName,
            AppResources.Rename,AppResources.Cancel,
            "");

            p.ActionSelected += (sender,action) => {
                if(action == PopupAction.OK){
                    RenameWorkoutTemplateAction(m,p.text);
                }
            };

            await PopupNavigation.Instance.PushAsync(p);
            // PromptConfig p = new PromptConfig()
            // {
            //     InputType = InputType.Default,
            //     IsCancellable = true,
            //     Title = string.Format("{0} \"{1}\"", AppResources.Rename, m.Label),
            //     Placeholder = AppResources.TapToEnterNewName,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     OkText = AppResources.Rename,
            //     OnAction = new Action<PromptResult>((PromptResult response) =>
            //     {
            //         if (response.Ok)
            //         {
            //             RenameWorkoutTemplateAction(m, response.Text);
            //         }
            //     })
            // };

            // UserDialogs.Instance.Prompt(p);
        }
    }

    private async void RenameWorkoutTemplateAction(WorkoutTemplateModel model, string newLabel)
    {
        int itemIndex = workoutItems.IndexOf(model);
        model.Label = newLabel;
        BooleanModel result = await DrMuscleRestClient.Instance.RenameWorkoutTemplate(model);
        if (result.Result)
        {
            workoutItems.RemoveAt(itemIndex);
            workoutItems.Insert(itemIndex, model);
            //exerciseItems[itemIndex].Label = newLabel;
        }
    }
    public async void OnReset(object sender, EventArgs e)
    {
        try
        {
            var mi = ((Button)sender);
            if (mi.CommandParameter is WorkoutTemplateModel)
            {
                WorkoutTemplateModel m = (WorkoutTemplateModel)mi.CommandParameter;
                CurrentLog.Instance.WorkoutTemplateSettings = m;

            }
           await Navigation.PushAsync(new WorkoutSettingsPage());

            OnCancelClicked(sender, e);
        }
        catch (Exception ex)
        {

        }
    }
    public async void OnDelete(object sender, EventArgs e)
    {
        var mi = ((Button)sender);
        if (mi.CommandParameter is WorkoutTemplateModel)
        {
            WorkoutTemplateModel m = (WorkoutTemplateModel)mi.CommandParameter;

            var p = await HelperClass.DisplayCustomPopup(AppResources.DeleteWorkout,string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
            AppResources.Delete,AppResources.Cancel);
            p.ActionSelected += (sender,action) => {
                if(action == PopupAction.OK){
                     DeleteWorkoutTemplateAction(m);
                }
            };
            // ConfirmConfig p = new ConfirmConfig()
            // {
            //     Title = AppResources.DeleteWorkout,
            //     Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
            //     OkText = AppResources.Delete,
            //     CancelText = AppResources.Cancel,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
            // };
            // p.OnAction = (obj) =>
            // {
            //     if (obj)
            //     {
            //         DeleteWorkoutTemplateAction(m);
            //     }
            // };
            // UserDialogs.Instance.Confirm(p);
        }
    }

    private async void DeleteWorkoutTemplateAction(WorkoutTemplateModel model)
    {
        int itemIndex = workoutItems.IndexOf(model);
        await WorkoutIsInProgram(model);
        BooleanModel result = await DrMuscleRestClient.Instance.DeleteWorkoutTemplateModel(model);
        if (result.Result)
        {
            workoutItems.RemoveAt(itemIndex);
        }
    }
    private async Task WorkoutIsInProgram(WorkoutTemplateModel model)
    {
        try
        {
            List<WorkoutTemplateModel> newList = new List<WorkoutTemplateModel>();
            foreach (var item in workouts)
            {
                if (item != model)
                    newList.Add(item);
            }
            var workTemplateGroup = new WorkoutTemplateGroupModel()
            {
                Id = CurrentLog.Instance.CurrentWorkoutTemplateGroup.Id,
                WorkoutTemplates = newList
            };
            BooleanModel result = await DrMuscleRestClient.Instance.CreateNewWorkoutTemplateOrder(workTemplateGroup);
        }
        catch (Exception ex)
        {

        }
    }

    private async Task UpdateWorkoutList()
    {
        if (workouts == null)
            return;
        workoutItems.Clear();

        foreach (WorkoutTemplateModel tm in workouts)
            workoutItems.Add(tm);

    }

    private async void WorkoutListView_ItemTapped(object sender, ItemTappedEventArgs e)
    {
        //if (((WorkoutTemplateModel)e.Item).Exercises.Count > 0)
        //{
        try
        {
            CurrentLog.Instance.CurrentWorkoutTemplate = (WorkoutTemplateModel)e.Item;
            CurrentLog.Instance.WorkoutStarted = true;
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                var page = new KenkoChooseYourWorkoutExercisePage();
                App.ShowTopButtonWorkout = true;
                page.OnBeforeShow();
                await Navigation.PushAsync(page);
            });
        }
        catch (Exception ex)
        {

        }

    }

}
