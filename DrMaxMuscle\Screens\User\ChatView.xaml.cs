using Acr.UserDialogs;
using DrMaxMuscle.Constants;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Entity;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Screens.Subscription;
#if ANDROID
using DrMaxMuscle.Platforms.Android.Renderers;
#elif IOS
using DrMaxMuscle.Platforms.iOS.Renderer;
#endif
using DrMuscleWebApiSharedModel;
using FFImageLoading;
using Newtonsoft.Json;
using Microsoft.Maui.Networking;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Text.RegularExpressions;
using DrMaxMuscle.Views;
using RGPopup.Maui.Services;
using DrMaxMuscle.Utility;
using System.Diagnostics;
namespace DrMaxMuscle.Screens.User;

public partial class ChatView : ContentView
{

    bool IsLoading = false;
    bool IsLoadMore = false;
    bool IsAdmin = false;
    bool IsSupportMessagesLoaded = false;
    string supportUrl = "";

    public ObservableCollection<DrMaxMuscle.Helpers.Messages> groupChannelsList { get; set; } = new ObservableCollection<DrMaxMuscle.Helpers.Messages>();
    public ObservableCollection<DrMaxMuscle.Helpers.Messages> messageList { get; set; } = new ObservableCollection<DrMaxMuscle.Helpers.Messages>();

    public ChatView()
    {
        InitializeComponent();

        TapGestureRecognizer btnSendTapGestureRecognizer = new TapGestureRecognizer();
        btnSendTapGestureRecognizer.Tapped += BtnSendTapGestureRecognizer_Tapped;
        OnAppearing();

        MessagingCenter.Subscribe<Message.LoadChatMessage>(this, "LoadChatMessage", (obj) =>
        {
            try
            {
                if (obj.IsFromLogin)
                {
                    groupChannelsList?.Clear();
                    messageList?.Clear();
                    //scrollMain.ScrollToAsync(0, 0, false);
                }
                OnAppearing();
            }
            catch (Exception ex) 
            {
                Debug.WriteLine("Error in LoadChatMessage: " + ex.Message);
            }            
        });

        MessagingCenter.Subscribe<Message.UnLoadChatMessage>(this, "UnLoadChatMessage", (obj) =>
        {
            try
            {
                IsLoading = false;
                OnDisappearing();
            }
            catch (Exception ex)
            {
                Debug.WriteLine("Error in UnLoadChatMessage: " + ex.Message);
            }
        });

        MessagingCenter.Subscribe<Message.MuteUnmuteUserMessage>(this, "MuteUnmuteUserMessage", (obj) =>
        {
            try
            {
                MuteUnmuteMessage(obj);
            }
            catch (Exception ex)
            {
                Debug.WriteLine("Error in MuteUnmuteUserMessage: " + ex.Message);
            }
        });

        MessagingCenter.Subscribe<Message.DeleteChatMessage>(this, "DeleteChatMessage", (obj) =>
        {
            try
            {
                DeleteMessage(obj);
            }
            catch (Exception ex)
            {
                Debug.WriteLine("Error in DeleteChatMessage: " + ex.Message);
            }
        });

        try
        {
            MessagingCenter.Unsubscribe<Message.HelpWithGoalChatMessage>(this, "HelpWithGoalChatMessage");
        }
        catch (Exception ex)
        {
            Debug.WriteLine("Error in Unsubscribe HelpWithGoalChatMessage: " + ex.Message);
        }

        MessagingCenter.Subscribe<Message.HelpWithGoalChatMessage>(this, "HelpWithGoalChatMessage", (obj) =>
        {
            try
            {
                if (obj.IsBodyWeightAnalysis || obj.IsVolumeAnalysis || obj.IsStrengthAnalysis || obj.IsInspireMe)
                    AIAnalysis_Clicked(obj.IsBodyWeightAnalysis, obj.IsVolumeAnalysis, obj.IsStrengthAnalysis, obj.IsInspireMe);
                else
                {
                    HelpWithGoal_Clicked();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine("Error in HelpWithGoalChatMessage: " + ex.Message);
            }
        });

        MessagingCenter.Subscribe<Message.HelpWithGoalChatMessage>(this, "FullReportChatMessage", (obj) =>
        {
            try
            {
                FullReport_Clicked();
            }
            catch (Exception ex)
            {
                Debug.WriteLine("Error in FullReportChatMessage: " + ex.Message);
            }
        });

        MessagingCenter.Subscribe<Message.HelpWithGoalChatMessage>(this, "AIChat", (obj) =>
        {
            try
            {
                AiButton.IsVisible = true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine("Error in AIChat: " + ex.Message);
            }
        });

        MessagingCenter.Subscribe<Message.HelpWithGoalChatMessage>(this, "AnalyzeMyCurrentStatsAndGiveMeYourTopRecommendation", async (obj) =>
        {
            try 
            {
                AiButton.IsVisible = false;

                var txt = "Analyze my current stats.";
                DrMuscleRestClient.Instance.SendMessage(new ChatModel()
                {
                    ReceiverId = AppThemeConstants.ChatReceiverId,
                    Message = txt
                });
                if (!App.IsFreePlan)
                {
                    SendMessagestoChatBot(txt, true, 4);
                    LoadJustSentSupportMessage();
                    LocalDBManager.Instance.SetDBSetting("IsFirstMessage", null);
                    LocalDBManager.Instance.SetDBSetting("IsSecondMessage", null);
                    LocalDBManager.Instance.SetDBSetting("IsFirstMessageSend", null);

                    messageList.Insert(0, new DrMaxMuscle.Helpers.Messages()
                    {
                        Message = txt,
                        ProfileUrl = "",
                        Nickname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value,
                        UserId = LocalDBManager.Instance.GetDBSetting("email")?.Value,
                        CreatedDate = DateTime.UtcNow,
                    });
                }
                else
                {
                    messageList.Insert(0, new DrMaxMuscle.Helpers.Messages()
                    {
                        Message = txt,
                        ProfileUrl = "",
                        Nickname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value,
                        UserId = LocalDBManager.Instance.GetDBSetting("email")?.Value,
                        CreatedDate = DateTime.UtcNow,
                    });

                    FreePlanUserMessage();
                }
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    if (messageList?.Count == 1)
                    {
                        lstChats.ItemsSource = messageList;
                        // lstChats.ScrollToFirst();
                        lstChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);
                    }
                    if (messageList.Count <= 4 && !IsAdmin && messageList.Where(x => x.UserId.Equals(LocalDBManager.Instance.GetDBSetting("email").Value)).ToList().Count == 1 && messageList.First().UserId.Equals(LocalDBManager.Instance.GetDBSetting("email").Value))
                    {

                    }
                });
                if (messageList?.Count > 1)
                    lstSupportChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);
                chatSupportInput.MessageText = "";
                if (Device.RuntimePlatform.Equals(Device.Android) && messageList.Count > 1)
                {
                    await Task.Delay(300);
                    lstSupportChats.ScrollTo(messageList[1], ScrollToPosition.End, animate: false);
                    lstSupportChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);
                }
            }
            catch (Exception ex) 
            {
                Debug.WriteLine("Error in AnalyzeMyCurrentStatsAndGiveMeYourTopRecommendation: " + ex.Message);
            }
        });

        MessagingCenter.Subscribe<string>(this, "ChatWithUsFromSatisfactionSurvey", async (obj) =>
        {
            try
            {
                AiButton.IsVisible = false;
                await Task.Delay(300);
                DrMuscleRestClient.Instance.SendMessage(new ChatModel()
                {
                    ReceiverId = AppThemeConstants.ChatReceiverId,
                    Message = obj
                });

                SendMessagestoChatBot(obj, true, 4);
                LoadJustSentSupportMessage();
                LocalDBManager.Instance.SetDBSetting("IsFirstMessage", null);
                LocalDBManager.Instance.SetDBSetting("IsSecondMessage", null);
                LocalDBManager.Instance.SetDBSetting("IsFirstMessageSend", null);

                messageList.Insert(0, new DrMaxMuscle.Helpers.Messages()
                {
                    Message = obj,
                    ProfileUrl = "",
                    Nickname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value,
                    UserId = LocalDBManager.Instance.GetDBSetting("email")?.Value,
                    CreatedDate = DateTime.UtcNow,
                });

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    if (messageList?.Count == 1)
                    {
                        lstChats.ItemsSource = messageList;
                        //lstChats.ScrollTo(0, ScrollToPosition.Start, animate: false);
                    }
                    if (messageList?.Count <= 4 && !IsAdmin && messageList.Where(x => x.UserId.Equals(LocalDBManager.Instance.GetDBSetting("email").Value)).ToList().Count == 1 && messageList.First().UserId.Equals(LocalDBManager.Instance.GetDBSetting("email").Value))
                    {

                    }
                });
                if (messageList?.Count > 1)
                    lstSupportChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);
                chatSupportInput.MessageText = "";
                if (Device.RuntimePlatform.Equals(Device.Android) && messageList.Count > 1)
                {
                    await Task.Delay(300);
                    lstSupportChats.ScrollTo(messageList[1], ScrollToPosition.End, animate: false);
                    lstSupportChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine("Error in ChatWithUsFromSatisfactionSurvey: " + ex.Message);
            }
        });

        try
        {
            timerToolbarItem = new ToolbarItem("1:1 support", "", SlideTimerAction, ToolbarItemOrder.Primary, 0);
        }
        catch (Exception ex)
        {

        }

        try
        {
            groupChatItem = new ToolbarItem("Group chat", "", GroupChatAction, ToolbarItemOrder.Primary, 0);
        }
        catch (Exception ex)
        {

        }
    }

    void NewBindingContextChanged()
    {
        //TODO: Remove below line when group chat activate
        try
        {
            if (IsAdmin)
            {
                ChatPage drMuscle = (ChatPage)this.Parent;
                if (drMuscle != null)
                {
                    drMuscle.ToolbarItems.Clear();
                    drMuscle.ToolbarItems.Add(timerToolbarItem);
                }
            }
            else
            {
                ChatPage drMuscle = (ChatPage)this.Parent;
                drMuscle?.ToolbarItems?.Clear();
            }
        }
        catch (Exception ex)
        {

        }
        try
        {
            if (Device.RuntimePlatform.Equals(Device.iOS))
            {
                ImageService.Instance.InvalidateMemoryCache();
                ImgLoader.Source = "typing_loader.gif";
            }
        }
        catch (Exception ex)
        {

        }
        return;
        /* when group chat activates
        try
        {
            if (App.IsV1User || IsAdmin || App.IsFreePlan)
            {

                Device.BeginInvokeOnMainThread(() =>
                {
                    ChatPage drMuscle = (ChatPage)this.Parent;
                    drMuscle.ToolbarItems.Clear();
                    drMuscle.ToolbarItems.Add(timerToolbarItem);
                });


            }
            else
            {

                Device.BeginInvokeOnMainThread(() =>
                {
                    ChatPage drMuscle = (ChatPage)this.Parent;
                    drMuscle.ToolbarItems.Clear();
                    drMuscle.ToolbarItems.Add(groupChatItem);
                });

            }
        }
        catch (Exception ex)
        {
            UserDialogs.Instance.HideLoading();
        }*/
    }
    public ToolbarItem timerToolbarItem;
    public ToolbarItem groupChatItem;

    public void SlideTimerAction()
    {
        Support_Tapped(null, EventArgs.Empty);
    }

    public async void GroupChatAction()
    {
        try
        {
            GroupChatPage page = new GroupChatPage();
            await Navigation.PushAsync(page);
            //await PagesFactory.PushAsync<GroupChatPage>();
        }
        catch (Exception ex)
        {

        }
    }


    async void OnAppearing()
    {
        try
        {
            groupChannelsList = new ObservableCollection<DrMaxMuscle.Helpers.Messages>();
            messageList = new ObservableCollection<DrMaxMuscle.Helpers.Messages>();

            IsSupportMessagesLoaded = false;
            bool isAnyPopup = false;
            //DependencyService.Get<IFirebase>().SetScreenName("chat_view");
            IsLoadMore = false;
            try
            {
                ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Tabs[0].BadgeCaption = 0;
            }
            catch (Exception ex)
            {

            }
            messageList.Clear();
            if (LocalDBManager.Instance.GetDBSetting("email") == null)
                return;
            IsAdmin = LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>") || LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>");

            //TODO: Remove below comments when group chat activate
            //if (App.IsV1User || IsAdmin || App.IsFreePlan)
            //{
            //    ChatMainView.IsVisible = true; 
            //    SupportMainView.IsVisible = false;
            //}
            //else
            //{
            //    ChatMainView.IsVisible = false;
            //    SupportMainView.IsVisible = true;
            //}

            //TODO: Remove below 2 line when group chat activate
            ChatMainView.IsVisible = false;
            SupportMainView.IsVisible = true;

            CheckIsV1();
            if (App.IsChatPopup)
                return;

            if (Config.ShowChatPopup == false && !App.IsChatPopup)
            {

                App.IsChatPopup = true;
                UserDialogs.Instance.HideLoading();
               var result = await HelperClass.DisplayCustomPopupForResult("AI & human chat","Instant support with AI. Human review in 1 day.",
                            AppResources.GotIt,AppResources.RemindMe);


               // await Task.Delay(100);
               

                // ConfirmConfig ShowPopUp = new ConfirmConfig()
                // {
                //     Title = "AI & human chat",
                //     Message = "Instant support with AI. Human review in 1 day.",
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     OkText = AppResources.GotIt,
                //     CancelText = AppResources.RemindMe,
                //     //OnAction = async (bool ok) =>
                //     //{
                //     //    if (ok)
                //     //    {
                //     //        Config.ShowChatPopup = true;
                //     //    }
                //     //    else
                //     //    {
                //     //        Config.ShowChatPopup = false;
                //     //    }
                //     //}
                // };
                // await Task.Delay(100);
                // var result = await UserDialogs.Instance.ConfirmAsync(ShowPopUp);
                isAnyPopup = true;
                App.IsChatPopup = false;
                if (result == PopupAction.OK)
                    Config.ShowChatPopup = true;
                else
                    Config.ShowChatPopup = false;
            }
            //TODO: Remove below comments when Group chat activate
            //if (LocalDBManager.Instance.GetDBSetting("IsSubscribedInApp")?.Value == "true")
            //{
            //    isAnyPopup = true;
            //    LocalDBManager.Instance.SetDBSetting("IsSubscribedInApp", "false");
            //    await UserDialogs.Instance.AlertAsync(new AlertConfig()
            //    {
            //        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //        Title = $"Group chat unlocked!",
            //        Message = "Chat with Dr. Juneau and other paying subscribers.",
            //        OkText = AppResources.GotIt,
            //    });

            //    await Task.Delay(100);
            //    TooltipEffect.SetPosition(BxTooltip, TooltipPosition.Bottom);
            //    TooltipEffect.SetBackgroundColor(BxTooltip, AppThemeConstants.BlueColor);
            //    TooltipEffect.SetTextColor(BxTooltip, Color.White);
            //    TooltipEffect.SetText(BxTooltip, $"1-on-1 support is here");
            //    TooltipEffect.SetHasTooltip(BxTooltip, true);
            //    TooltipEffect.SetHasShowTooltip(BxTooltip, true);
            //}
            //else if (!Config.IsExistingSubscriber && App.IsV1User)
            //{
            //    UserDialogs.Instance.HideLoading();
            //    isAnyPopup = true;
            //    Config.IsExistingSubscriber = true;
            //    var waitHandle = new EventWaitHandle(false, EventResetMode.AutoReset);
            //    var modalPage = new Views.GeneralPopup("TrueState.png", "You have unlocked a premium feature!", "Welcome to group chat (only subscribers have access). Prefer 1-on-1 support? Tap the link top-right.", "Got it");
            //    modalPage.Disappearing += (sender2, e2) =>
            //    {
            //        waitHandle.Set();
            //    };
            //    await PopupNavigation.Instance.PushAsync(modalPage);
            //    await Task.Run(() => waitHandle.WaitOne());
            //    TooltipEffect.SetPosition(BxTooltip, TooltipPosition.Bottom);
            //    TooltipEffect.SetBackgroundColor(BxTooltip, AppThemeConstants.BlueColor);
            //    TooltipEffect.SetTextColor(BxTooltip, Color.White);
            //    TooltipEffect.SetText(BxTooltip, $"1-on-1 support ");
            //    TooltipEffect.SetHasTooltip(BxTooltip, true);
            //    TooltipEffect.SetHasShowTooltip(BxTooltip, true);

            //}


            try
            {
                if (Connectivity.NetworkAccess != NetworkAccess.Internet)
                {
                    // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                    // {
                    //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //     Message = AppResources.PleaseCheckInternetConnection,
                    //     Title = AppResources.ConnectionError,
                    //     OkText = "Try again"
                    // });
                    await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                       AppResources.PleaseCheckInternetConnection,"Try again","");
                    return;
                }

                //
                Connect_Handler();
                if (IsAdmin)
                    GetMutedUserList_handler();
            }
            catch (Exception ex)
            {

            }


        }
        catch (Exception ex)
        {

        }
    }
    private async void CheckIsV1()
    {
        NotPurchased.IsVisible = !(await CanGoFurtherWithoughtLoader());
    }

    async void NotPurchased_Clicked(object sender, System.EventArgs e)
    {
        //AlertConfig alertConfig = new AlertConfig()
        //{
        //    Title = "",
        //    //Message = AppResources.GroupChatIsPayingSubscribeOnly,
        //    Message = AppResources.GroupChatIsPayingSubscribeOnly,
        //    OkText = AppResources.Ok,
        //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
        //};
        //UserDialogs.Instance.Alert(alertConfig);

        try
        {
            var supersetConfig = await HelperClass.DisplayCustomPopup("You discovered a premium feature!", "Upgrading will unlock group chat�1-on-1 support is always free.",
                          "1-on-1 support", "Upgrade");
            bool result = false;
            supersetConfig.ActionSelected += async (sender, action) =>
            {
                if (action == PopupAction.OK)
                {
                    CurrentLog.Instance.ChannelUrl = supportUrl;
                    CurrentLog.Instance.RoomId = 0;
                    SupportPage supportPage = new SupportPage();
                    supportPage.OnBeforeShow();
                    await Navigation.PushAsync(supportPage);
                    //await PagesFactory.PushAsync<SupportPage>();
                }
                else
                {
                    SubscriptionPage subscriptionPage = new SubscriptionPage();
                    subscriptionPage.OnBeforeShow();
                    await Navigation.PushAsync(subscriptionPage);
                    //await PagesFactory.PushAsync<SubscriptionPage>();
                }
            };

        }
        catch (Exception ex)
        {

        }
        // ConfirmConfig alertConfig = new ConfirmConfig()
        // {
        //     Title = "You discovered a premium feature!",
        //     Message = "Upgrading will unlock group chat�1-on-1 support is always free.",
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     OkText = "1-on-1 support",
        //     CancelText = "Upgrade",
        //     OnAction = async (bool ok) =>
        //     {
        //         if (ok)
        //         {
        //             CurrentLog.Instance.ChannelUrl = supportUrl;
        //             CurrentLog.Instance.RoomId = 0;
        //             SupportPage supportPage = new SupportPage();
        //             supportPage.OnBeforeShow();
        //             await Navigation.PushAsync(supportPage);
        //             //await PagesFactory.PushAsync<SupportPage>();
        //         }
        //         else
        //         {
        //             SubscriptionPage subscriptionPage = new SubscriptionPage();
        //             subscriptionPage.OnBeforeShow();
        //             await Navigation.PushAsync(subscriptionPage);
        //             //await PagesFactory.PushAsync<SubscriptionPage>();
        //         }
        //     }
        // };

        // UserDialogs.Instance.Confirm(alertConfig);

    }

    async void OnDisappearing()
    {
        try
        {
            CurrentLog.Instance.GroupChats = await DrMuscleRestClient.Instance.FetchGroupMessages(new GroupChatModel() { UpdatedAt = DateTime.UtcNow });
            if (!IsAdmin)
                LoadJustSentSupportMessage();
        }
        catch (Exception ex)
        {

        }
    }

    void Connect_Handler()
    {
        NewBindingContextChanged();

        if (LocalDBManager.Instance.GetDBSetting("firstname") == null)
            return;

        string profileUrl = null;
        if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("ProfilePic")?.Value))
            profileUrl = LocalDBManager.Instance.GetDBSetting("ProfilePic")?.Value;
        LoadData();

        if (string.IsNullOrEmpty(Config.RegisteredDeviceToken)) return;
        //System.Diagnostics.Debug.WriteLine($"NOTIFICATION TOKEN { SendBirdClient.GetPendingPushToken()}");
        // For Android
        try
        {

            if (Device.RuntimePlatform.Equals(Device.Android))
            {

            }
            else
            {

            }

        }
        catch (Exception ex)
        {

        }


    }

    async void LoadData()
    {
        try
        {

            if (groupChannelsList?.Count == 1)
                return;

            if (App.IsV1User || IsAdmin || App.IsFreePlan)
            {
                DrMaxMuscle.Helpers.Messages messages = new DrMaxMuscle.Helpers.Messages()
                {
                    Message = AppResources.TapHereFor11Chat,
                    Nickname = AppResources.ChatWithSupport,
                    CreatedDate = DateTime.Now,
                    ChatType = ChannelType.Group,
                    UserId = "<EMAIL>"
                };



                MainThread.BeginInvokeOnMainThread(() =>
                {
                    Color backColor = AppThemeConstants.RandomColor;

                    if (AppThemeConstants.ProfileColor.ContainsKey("<EMAIL>"))
                    {
                    }
                    else
                    {
                        AppThemeConstants.ProfileColor.Add("<EMAIL>", backColor);
                    }
                });

            }
            //TODO: Remove below comments when group chat activate
            //if (App.IsV1User || IsAdmin || App.IsFreePlan)
            //{
            //    GetMessages();
            //}
            //else
            //{
            //    GetSupportMessages();
            //}

            //TODO: Remove below line when group chat activate
            GetSupportMessages();

        }
        catch (Exception ex)
        {

        }
    }

    private async void DeleteMessage(Message.DeleteChatMessage msg)
    {
        //openChannel.DeleteMessage(msg.FullMessage.BaseMessage, (e) => {
        //    if (e == null)
        //    {
        //        Device.BeginInvokeOnMainThread(() => {
        //            messageList.Remove(msg.FullMessage);
        //        });
        //    }
        //});
        if (!IsAdmin)
            return;
        var result = DrMuscleRestClient.Instance.DeleteGroupChatMessage(new GroupChatModel() { Id = msg.FullMessage.MessageId });
        //using (HttpClient client = new HttpClient())
        //{

        //    client.BaseAddress = new Uri($"https://api-91658003-270F-446B-BD61-0043FAA8D641.sendbird.com/v3/open_channels/{openChannel.Url}/messages/{msg.FullMessage.BaseMessage.MessageId}");
        //    client.DefaultRequestHeaders.Add("Api-Token", "f72f26ba1b6b8bf6c8d37315414fbff1d915a3c8");
        //    HttpResponseMessage httpResponse = await client.DeleteAsync("");
        MainThread.BeginInvokeOnMainThread(() =>
        {
            if (messageList.Count > 0)
            { messageList.Remove(msg.FullMessage); }
        });
        //    string raw = await httpResponse.Content.ReadAsStringAsync();
        //}
    }

    private async void MuteUnmuteMessage(Message.MuteUnmuteUserMessage msg)
    {
        if (!IsAdmin)
            return;
        if (msg.IsMuted)
        {
            try
            {

                //using (HttpClient client = new HttpClient())
                //{
                //    //
                //    client.BaseAddress = new Uri($"https://api-91658003-270F-446B-BD61-0043FAA8D641.sendbird.com/v3/open_channels/{openChannel.Url}/mute/{msg.UserId}");
                //    client.DefaultRequestHeaders.Add("Api-Token", "f72f26ba1b6b8bf6c8d37315414fbff1d915a3c8");

                //    HttpResponseMessage httpResponse = await client.DeleteAsync("");

                //    string raw = await httpResponse.Content.ReadAsStringAsync();
                //    if (App.MutedUserList.Contains(msg.UserId))
                //        App.MutedUserList.Remove(msg.UserId);
                //}
                var result = await DrMuscleRestClient.Instance.UnmuteGroupChatUser(new GroupChatModel() { SenderEmail = msg.UserId });
                if (App.MutedUserList.Contains(msg.UserId))
                    App.MutedUserList.Remove(msg.UserId);

            }
            catch (Exception ex)
            {

            }
            //openChannel.UnmuteUserWithUserId(msg.UserId, UnmutedUser_Handler);

        }
        else
        {
            //openChannel.MuteUserWithUserId(msg.UserId, mutedUser_Handler);
            //using (HttpClient client = new HttpClient())
            //{
            ////
            //client.BaseAddress = new Uri($"https://api-91658003-270F-446B-BD61-0043FAA8D641.sendbird.com/v3/open_channels/{openChannel.Url}/mute");
            //client.DefaultRequestHeaders.Add("Api-Token", "f72f26ba1b6b8bf6c8d37315414fbff1d915a3c8");
            //var url = $"https://api-91658003-270F-446B-BD61-0043FAA8D641.sendbird.com/v3/open_channels/{openChannel.Url}/mute";
            //var sbModel = new SBLocalMessage()
            //{
            //    user_id = msg.UserId,
            //    message = ""
            //};
            //HttpContent content = new StringContent(JsonConvert.SerializeObject(sbModel),
            //                                    Encoding.UTF8,
            //                                    "application/json");
            //HttpResponseMessage httpResponse = await client.PostAsync("", content);

            //string raw = await httpResponse.Content.ReadAsStringAsync();
            var result = await DrMuscleRestClient.Instance.MuteGroupChatUser(new GroupChatModel() { SenderEmail = msg.UserId });

            if (!App.MutedUserList.Contains(msg.UserId))
                App.MutedUserList.Add(msg.UserId);
            //}

        }
    }



    private async void GetMutedUserList_handler()
    {
        try
        {

            var muted = await DrMuscleRestClient.Instance.GetMutedUsers();
            if (muted != null && muted.Count > 0)

                App.MutedUserList = muted;



        }
        catch (Exception ex)
        {

        }

    }



    bool isSendSecond = false;


    private async void GetSupportMessages()
    {
        try
        {
            IsSupportMessagesLoaded = true;
            ChatPage drMuscle = (ChatPage)this.Parent;
            if (drMuscle != null)
                drMuscle.Title = "Chat";
            SBLocalMessage sbModel = null, sbModel1 = null;
            if (LocalDBManager.Instance.GetDBSetting("email") == null)
            {
                messageList?.Clear();
                return;
            }

            if (IsLoading)
                return;
            IsLoading = true;
            //var ms = "";
            try
            {
                if (LocalDBManager.Instance.GetDBSetting("IsFirstMessage") != null && LocalDBManager.Instance.GetDBSetting("IsFirstMessage")?.Value == "First1" && !CurrentLog.Instance.IsSendOne)
                {
                    CurrentLog.Instance.IsSendOne = true;
                    LocalDBManager.Instance.SetDBSetting("IsFirstMessage", null);
                    LocalDBManager.Instance.SetDBSetting("IsFirstMessageSend", "First1");
                    //using (HttpClient client = new HttpClient())
                    //{
                    var goal = "";
                    if (LocalDBManager.Instance.GetDBSetting("DBFocus") != null)
                        goal = LocalDBManager.Instance.GetDBSetting("DBFocus").Value;


                    sbModel1 = new SBLocalMessage()
                    {
                        user_id = "<EMAIL>",
                        message = ""
                    };
                    var reps = "";
                    if (LocalDBManager.Instance.GetDBSetting("SmashDemo") != null && LocalDBManager.Instance.GetDBSetting("SmashDemo")?.Value != null)
                    {
                        reps = LocalDBManager.Instance.GetDBSetting("SmashDemo").Value;
                    }

                    //if (!string.IsNullOrEmpty(reps))
                    //    ms = $"Congrats on smashing the demo! {reps} crunches, that's nice :) \n\n";
                    //ms += $"So your goal is {goal}. Can you tell me more?";
                    //sbModel1.message = ms;
                    //var ms = "Welcome! I'm Dr. Muscle, your AI coach. Trained on the latest exercise science by Dr. Carl Juneau, PhD. Ask me anything for a quick reply. A human will also reply 1 business day.";
                    AiButton.IsVisible = true;

                    //var isadded = await DrMuscleRestClient.Instance.SendAdminMessage(new ChatModel()
                    //{
                    //    ReceiverId = AppThemeConstants.ChatReceiverId,
                    //    Message = ms,
                    //    IsFromAI = true
                    //});
                    //if (Device.RuntimePlatform.Equals(Device.Android))
                    //{
                    //    if (messageList.Count == 0)
                    //        messageList.Insert(0, new DrMaxMuscle.Helpers.Messages()
                    //        {
                    //            Message = ms,
                    //            ProfileUrl = "",
                    //            Nickname = "Carl Juneau",
                    //            UserId = "<EMAIL>",
                    //            CreatedDate = DateTime.UtcNow,
                    //            ChatType = ChannelType.Group,
                    //            IsFromAI = true
                    //        });
                    //}
                    //string raw = await httpResponse.Content.ReadAsStringAsync();
                    //}
                }
            }
            catch (Exception ex)
            {

            }

            try
            {
                if (LocalDBManager.Instance.GetDBSetting("IsSecondMessage") != null && LocalDBManager.Instance.GetDBSetting("IsSecondMessage")?.Value == "Second1" && !isSendSecond)
                {
                    isSendSecond = true;
                    LocalDBManager.Instance.SetDBSetting("IsFirstMessage", null);
                    LocalDBManager.Instance.SetDBSetting("IsSecondMessage", null);
                    LocalDBManager.Instance.SetDBSetting("IsFirstMessageSend", null);

                    //using (HttpClient client = new HttpClient())
                    //{
                    var goal = "";
                    if (LocalDBManager.Instance.GetDBSetting("PopupMainGoal") != null)
                        goal = LocalDBManager.Instance.GetDBSetting("PopupMainGoal").Value;
                    //client.BaseAddress = new Uri($"https://****************************************.sendbird.com/v3/group_channels/{supportUrl}/messages");
                    //client.DefaultRequestHeaders.Add("Api-Token", "f72f26ba1b6b8bf6c8d37315414fbff1d915a3c8");
                    //    client.DefaultRequestHeaders.Add("Content-Type", "application/json");

                    sbModel = new SBLocalMessage()
                    {
                        user_id = "<EMAIL>",
                        message = "Nice to see you back -- hit me up if you need anything ;)"
                    };
                    var firstTimeOpen = LocalDBManager.Instance.GetDBSetting("FirstTImeOpen")?.Value;
                    if (!string.IsNullOrEmpty(firstTimeOpen))
                    {
                        var date = new DateTime(long.Parse(firstTimeOpen));
                        if ((DateTime.Now - date).TotalDays >= 3)
                            sbModel.message = "No new workout -- anything holding you back?";
                        else
                        {
                            MainThread.BeginInvokeOnMainThread(() =>
                            {
                                DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1051);
                            });
                        }
                        //HttpContent content = new StringContent(JsonConvert.SerializeObject(sbModel),
                        //                                        Encoding.UTF8,
                        //                                        "application/json");

                        //HttpResponseMessage httpResponse = await client.PostAsync("", content);

                        //string raw = await httpResponse.Content.ReadAsStringAsync();
                        var isadded = await DrMuscleRestClient.Instance.SendAdminMessage(new ChatModel()
                        {
                            ReceiverId = AppThemeConstants.ChatReceiverId,
                            Message = sbModel.message,
                            IsFromAI = true
                        });

                        if (Device.RuntimePlatform.Equals(Device.Android))
                        {
                            messageList.Insert(0, new DrMaxMuscle.Helpers.Messages()
                            {
                                Message = sbModel.message,
                                ProfileUrl = "",
                                Nickname = "Carl Juneau",
                                UserId = "<EMAIL>",
                                CreatedDate = DateTime.UtcNow,
                                IsFromAI = true
                            });
                        }
                    }
                    
                }

            }
            catch (Exception ex)
            {

            }
            List<ChatModel> chatMessages;
            if (CurrentLog.Instance?.SupportChats == null)
                CurrentLog.Instance.SupportChats = new List<ChatModel>();

            if (messageList?.Count == 0)
                chatMessages = CurrentLog.Instance.SupportChats?.Count != 0 ? CurrentLog.Instance.SupportChats : await DrMuscleRestClient.Instance.FetchChatBox(new ChatModel() { CreatedAt = messageList?.Count == 0 ? DateTime.UtcNow : messageList.LastOrDefault().CreatedDate, ReceiverId = AppThemeConstants.ChatReceiverId });
            else
                chatMessages = await DrMuscleRestClient.Instance.FetchChatBoxWithoutLoader(new ChatModel() { CreatedAt = messageList.LastOrDefault().CreatedDate, ReceiverId = AppThemeConstants.ChatReceiverId });
            LoadJustSentSupportMessage();
            if (chatMessages == null)
            {
                UserDialogs.Instance.HideLoading();
                return;
            }

            if (Device.RuntimePlatform.Equals(Device.Android))
            {
                foreach (ChatModel msg in chatMessages)
                {
                    if (sbModel != null)
                    {
                        if (msg.Message.Equals(sbModel.message))
                            continue;
                    }
                    if (messageList?.Count <= 4)
                    {
                        try
                        {

                            if (messageList.Where(x => x.Message.Equals("Welcome! I'm Dr. Muscle, your AI coach. Trained on the latest exercise science by Dr. Carl Juneau, PhD. Ask me anything for a quick reply. A human will also reply in 1 business day.")).FirstOrDefault() != null)
                                continue;

                        }
                        catch (Exception ex)
                        {

                        }
                    }
                    try
                    {

                        messageList.Add(new DrMaxMuscle.Helpers.Messages()
                        {
                            Message = msg.Message,
                            UserId = msg.SenderEmail,
                            ProfileUrl = "",
                            Nickname = msg.SenderName,
                            CreatedDate = msg.CreatedAt,
                            ChatType = ChannelType.Group,
                            IsFromAI = msg.IsFromAI
                        });

                    }
                    catch (Exception ex)
                    {

                    }
                }


               MainThread.BeginInvokeOnMainThread(async() =>
{
                    if (messageList?.Count <= 2)
                    {
                        await Task.Delay(300);
                        lstChats.ItemsSource = messageList;
                        lstChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);
                    }
                    //LblGroup.Text = $"{AppResources.GroupChat} ({messageList.Count})";
                    //BoxBorder.IsVisible = true;
                    if (messageList != null && messageList.Count > 0)
                    {
                        lstChats.ItemsSource = messageList;
                    }
                    else
                    {
                        lstChats.ItemsSource = null; // Or handle gracefully
                    }
                    if (messageList?.Count < 11 && chatMessages.Count != 0)
                    {
                        lstChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);
                    }
                    //else
                    //  lstChats.ScrollTo(messageList[messages.Count - 2], ScrollToPosition.Start, false);

                });
            }
            else
            {
                //Device.BeginInvokeOnMainThread(() =>
                //{
                foreach (ChatModel msg in chatMessages)
                {
                    messageList.Add(new DrMaxMuscle.Helpers.Messages()
                    {
                        Message = msg.Message,
                        UserId = msg.SenderEmail,
                        ProfileUrl = "",
                        Nickname = msg.SenderName,
                        CreatedDate = msg.CreatedAt,
                        ChatType = ChannelType.Group,
                        IsFromAI = msg.IsFromAI
                    });
                }

                /*Welcome! I'm Dr. Muscle, your AI coach. I'm trained on the latest exercise science by Dr. Carl Juneau, PhD. I also have access to your stats. You can ask me anything. Someone from the team will also reply in 1 business day if needed. Not sure what to ask? Try: "How do I build muscle?"*/

                //LblGroup.Text = $"{AppResources.GroupChat} ({messageList.Count})";
                //BoxBorder.IsVisible = true;
                if (messageList != null && messageList.Count > 0)
                {
                    lstChats.ItemsSource = messageList;
                }
                else
                {
                    lstChats.ItemsSource = null; // Or handle gracefully
                }
                if (messageList?.Count < 11 && chatMessages.Count != 0)
                {
                    lstChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);
                }
                //else
                //  lstChats.ScrollTo(messageList[messages.Count - 2], ScrollToPosition.Start, false);

                //});
            }


            IsLoading = false;




            if (chatMessages?.Count == 0)
            {
                if (messageList?.Count > 0)
                {
                    if (string.IsNullOrEmpty(messageList.LastOrDefault().UserId))
                        return;
                }
                if (messageList?.Count <= 3 && !IsAdmin && messageList?.Where(x => x.UserId.Equals(LocalDBManager.Instance.GetDBSetting("email").Value)).ToList().Count == 1 && messageList.First().UserId.Equals(LocalDBManager.Instance.GetDBSetting("email").Value))
                {
                    var msg = "";
                    if (messageList.Any(x => x.UserId.ToLower().Equals(LocalDBManager.Instance.GetDBSetting("email").Value)))
                    {
                        if (DateTime.Now.DayOfWeek == DayOfWeek.Saturday || DateTime.Now.DayOfWeek == DayOfWeek.Sunday)
                            msg = $"Thanks {LocalDBManager.Instance.GetDBSetting("firstname").Value}. I'm not online at the moment, but you can expect a response by Monday evening.";
                        else
                            msg = $"Thanks {LocalDBManager.Instance.GetDBSetting("firstname").Value}. I'm not online at the moment, but you can expect a response by {AppThemeConstants.ChatTimeAgoFromDate(messageList.First().CreatedDate)} tomorrow.";
                        if (!"<EMAIL>".Equals(LocalDBManager.Instance.GetDBSetting("email").Value))
                            messageList.Insert(0, new DrMaxMuscle.Helpers.Messages()
                            {
                                Message = msg,
                                ProfileUrl = messageList.First().ProfileUrl,
                                Nickname = "Carl Juneau",
                                UserId = "<EMAIL>",
                                CreatedDate = messageList.First().CreatedDate,
                                ChatType = ChannelType.Group
                            });
                        if (messageList != null && messageList?.Count > 0)
                        {
                            lstSupportChats.ItemsSource = messageList;
                        }
                        else
                        {
                            lstSupportChats.ItemsSource = null; // Or handle gracefully
                        }
                    }
                }
                if (messageList.Contains(messageList.Where(x => x.UserId == "").FirstOrDefault()))
                    messageList.Remove(messageList.Where(x => x.UserId == "").FirstOrDefault());
                messageList.Add(new DrMaxMuscle.Helpers.Messages()
                {
                    UserId = ""
                });

            }
            MainThread.BeginInvokeOnMainThread(() =>
            {
                if (messageList != null && messageList?.Count > 0)
                {
                    lstSupportChats.ItemsSource = messageList;
                }
                else
                {
                    lstSupportChats.ItemsSource = null; // Or handle gracefully
                }
                if (messageList?.Count < 26 && chatMessages?.Count != 0)
                    lstSupportChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);

            });
            IsLoading = false;
            IsLoadMore = false;
            UserDialogs.Instance.HideLoading();

        }
        catch (Exception ex)
        {

        }
    }

    async void Support_Tapped(object sender, System.EventArgs e)
    {
        try
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                if (!IsAdmin)
                {

                    CurrentLog.Instance.ChannelUrl = supportUrl;
                    CurrentLog.Instance.RoomId = 0;
                    SupportPage supportPage = new SupportPage();
                    supportPage.OnBeforeShow();
                    await Navigation.PushAsync(supportPage);
                    //await PagesFactory.PushAsync<SupportPage>();

                }
                else
                {
                    //CurrentLog.Instance.ChannelUrl = ((DrMaxMuscle.Helpers.Messages)e.Item).SupportChannelUrl;
                    InboxPage page = new InboxPage();
                    await Navigation.PushAsync(page);
                    //await PagesFactory.PushAsync<InboxPage>();

                }
            });
        }
        catch (Exception ex)
        {

        }
    }

    async void LstView_ItemTapped(object sender, ItemTappedEventArgs e)
    {
        try
        {
            if (!IsAdmin)
            {
                if (((DrMaxMuscle.Helpers.Messages)e.Item).ChatType == ChannelType.Group)
                {
                    CurrentLog.Instance.ChannelUrl = supportUrl;
                    CurrentLog.Instance.RoomId = 0;
                    SupportPage supportPage = new SupportPage();
                    supportPage.OnBeforeShow();
                    await Navigation.PushAsync(supportPage);
                }
            }
            else
            {
                if (((DrMaxMuscle.Helpers.Messages)e.Item).ChatType == ChannelType.Group)
                {
                    //CurrentLog.Instance.ChannelUrl = ((DrMaxMuscle.Helpers.Messages)e.Item).SupportChannelUrl;
                    InboxPage page = new InboxPage();
                    await Navigation.PushAsync(page);
                    //await PagesFactory.PushAsync<InboxPage>();
                }
            }
        }
        catch (Exception ex)
        {

        }
    }


    private async void GetMessages()
    {
        try
        {
            ChatPage drMuscle = null;
            try
            {
                drMuscle = (ChatPage)this.Parent;
                if (messageList.Count == 0 && drMuscle != null)
                    drMuscle.Title = "Group chat";
            }
            catch (Exception ex)
            {

            }

            if (LocalDBManager.Instance.GetDBSetting("email") == null)
            {
                messageList.Clear();
                return;
            }
            if (IsLoading)
                return;
            IsLoading = true;


            try
            {
                if (CurrentLog.Instance.GroupChats == null)
                    CurrentLog.Instance.GroupChats = new List<GroupChatModel>();
                var chatMessages = messageList.Count == 0 && CurrentLog.Instance.GroupChats.Count != 0 ? CurrentLog.Instance.GroupChats : await DrMuscleRestClient.Instance.FetchGroupMessages(new GroupChatModel() { UpdatedAt = messageList.Count == 0 ? DateTime.UtcNow : messageList.LastOrDefault().CreatedDate });
                try
                {
                    drMuscle = (ChatPage)this.Parent;
                    if (messageList.Count == 0 && drMuscle != null)
                        drMuscle.Title = "Group chat";
                }
                catch (Exception ex)
                {

                }

                if (Device.RuntimePlatform.Equals(Device.Android))
                {
                    foreach (GroupChatModel msg in chatMessages)
                    {
                        messageList.Add(new DrMaxMuscle.Helpers.Messages()
                        {
                            Message = msg.Message,
                            UserId = msg.SenderEmail,
                            Nickname = IsAdmin && !msg.SenderEmail.ToLower().Equals("<EMAIL>") ? $"{msg.SenderName} ({msg.SenderEmail})" : msg.SenderName,
                            CreatedDate = msg.UpdatedAt,
                            ProfileUrl = "",
                            MessageId = msg.Id,
                            SenderId = msg.SenderId,

                        });
                    }
                    if (chatMessages.Count == 0)
                    {
                        IsLoading = false;
                        IsLoadMore = false;
                        return;
                    }
                    IsLoading = false;


                    if (messageList.Count < 26)
                    {
                        int active = messageList.Select(x => x.SenderId).Distinct().Count();
                        if (drMuscle != null)
                            drMuscle.Title = $"Group chat ({Math.Ceiling(active * (double)1.5)} active)";
                    }
                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        UserDialogs.Instance.HideLoading();
                        if (messageList != null && messageList.Count > 0)
                        {
                            lstChats.ItemsSource = messageList;
                        }
                        else
                        {
                            lstChats.ItemsSource = null; // Or handle gracefully
                        }
                        if (messageList?.Count < 11)
                        {
                            lstChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);
                        }
                    });
                }
                else
                {
                    foreach (GroupChatModel msg in chatMessages)
                    {
                        messageList.Add(new DrMaxMuscle.Helpers.Messages()
                        {
                            Message = msg.Message,
                            UserId = msg.SenderEmail,
                            Nickname = IsAdmin && !msg.SenderEmail.ToLower().Equals("<EMAIL>") ? $"{msg.SenderName} ({msg.SenderEmail})" : msg.SenderName,
                            CreatedDate = msg.UpdatedAt,
                            ProfileUrl = "",
                            MessageId = msg.Id,
                            SenderId = msg.SenderId
                        });
                    }
                    if (chatMessages.Count == 0)
                    {
                        IsLoading = false;
                        IsLoadMore = false;
                        return;
                    }
                    IsLoading = false;
                    UserDialogs.Instance.HideLoading();

                    if (messageList.Count < 26)
                    {
                        int active = messageList.Select(x => x.SenderId).Distinct().Count();
                        if (drMuscle != null)
                            drMuscle.Title = $"Group chat ({Math.Ceiling(active * (double)1.5)} active)";
                    }
                    if (messageList != null && messageList?.Count > 0)
                    {
                        lstChats.ItemsSource = messageList;
                    }
                    else
                    {
                        lstChats.ItemsSource = null; // Or handle gracefully
                    }
                    if (messageList?.Count < 11)
                    {
                        lstChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);
                    }
                }


                IsLoading = false;
                IsLoadMore = false;
                CurrentLog.Instance.GroupChats = await DrMuscleRestClient.Instance.FetchGroupMessages(new GroupChatModel() { UpdatedAt = DateTime.UtcNow });
            }
            catch (Exception ex)
            {
                //UserDialogs.Instance.HideLoading();
            }
            //});

        }
        catch (Exception ex)
        {

        }

    }

    private async void BtnSendTapGestureRecognizer_Tapped(object sender, EventArgs ea)
    {

        if (Connectivity.NetworkAccess != NetworkAccess.Internet)
        {
            ConnectionError();
            return;
        }
        try
        {

            if (string.IsNullOrEmpty(chatInput.MessageText.Trim()))
                return;

            var gcModel = await DrMuscleRestClient.Instance.SendGroupMessage(new GroupChatModel()
            {
                Message = chatInput.MessageText.Trim(),
            });
            LoadJustSentGroupMessage();
            LocalDBManager.Instance.SetDBSetting("IsFirstMessage", null);
            LocalDBManager.Instance.SetDBSetting("IsSecondMessage", null);
            LocalDBManager.Instance.SetDBSetting("IsFirstMessageSend", null);
            if (gcModel != null)
            {
                messageList.Insert(0, new DrMaxMuscle.Helpers.Messages()
                {
                    Message = gcModel.Message,
                    ProfileUrl = "",
                    Nickname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value,
                    UserId = LocalDBManager.Instance.GetDBSetting("email")?.Value,
                    CreatedDate = gcModel.UpdatedAt,
                    MessageId = gcModel.Id
                });
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    if (messageList != null && messageList.Count > 0)
                    {
                        lstChats.ItemsSource = messageList;
                    }
                    else
                    {
                        lstChats.ItemsSource = null; // Or handle gracefully
                    }
                    lstChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);
                });
            }
            //});
            chatInput.MessageText = "";

        }
        catch (Exception ex)
        {

        }
    }

    private async void LoadJustSentGroupMessage()
    {
        CurrentLog.Instance.GroupChats = await DrMuscleRestClient.Instance.FetchGroupMessages(new GroupChatModel() { UpdatedAt = DateTime.UtcNow });
    }

    private async void LoadJustSentSupportMessage()
    {
        CurrentLog.Instance.SupportChats = await DrMuscleRestClient.Instance.FetchChatBoxWithoutLoader(new ChatModel() { CreatedAt = DateTime.UtcNow, ReceiverId = AppThemeConstants.ChatReceiverId });
    }

    private void TxtMsg_TextChanged(object sender, TextChangedEventArgs e)
    {
        //btnSend.IsEnabled = e.NewTextValue.Length > 0 && CrossConnectivity.Current.IsConnected;
    }

    void Handle_ItemAppearing(object sender, ItemVisibilityEventArgs e)
    {
        var itemTypeObject = e.Item as DrMaxMuscle.Helpers.Messages;
        try
        {
            if (messageList.Count == 0)
                return;
            if (IsLoading == true)
                return;
            if (IsLoadMore)
                return;
            if (messageList.LastOrDefault() == itemTypeObject)
            {
                IsLoadMore = true;
                if (!IsSupportMessagesLoaded && (App.IsV1User || IsAdmin || App.IsFreePlan))
                    GetMessages();
                else
                    GetSupportMessages();
            }


        }
        catch (Exception ex)
        {

        }
    }

    async Task ConnectionError()
    {
        // await UserDialogs.Instance.AlertAsync(new AlertConfig()
        // {
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     Message = AppResources.PleaseCheckInternetConnection,
        //     Title = AppResources.ConnectionError,
        //     OkText = "Try again"
        // });

        await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                       AppResources.PleaseCheckInternetConnection,"Try again","");
    }
    void Handle_ItemDisappearing(object sender, ItemVisibilityEventArgs e)
    {

    }

    public void OnListTapped(object sender, ItemTappedEventArgs e)
    {
        chatInput.UnFocusEntry();
    }

    public async Task<bool> CanGoFurtherWithoughtLoader()
    {
        try
        {
            if (LocalDBManager.Instance.GetDBSetting("creation_date") == null)
                return false;

            BooleanModel isV1User = await DrMuscleRestClient.Instance.IsV1UserWithoutLoader();

            if (isV1User != null)
            {

                if (isV1User.Result)
                {
                    App.IsFreePlan = !isV1User.IsTraining;
                    return true;
                }

                // Uncomment code please
                //var _drMuscleSubcription = (IDrMuscleSubcription)MauiProgram.ServiceProvider.GetService(typeof(IDrMuscleSubcription));
                //if (_drMuscleSubcription.IsActiveSubscriptions())
                //    return true;
            }
        }
        catch (Exception ex)
        {

        }
        return false;
    }

    ///========================
    ///Support start
    ///========================

    private async void BtnSendSupportTapGestureRecognizer_Tapped(object sender, EventArgs ea)
    {
        try
        {
            var txt = chatSupportInput.MessageText.Trim();
            
                chatSupportInput.UnFocusEntry();
                await Task.Delay(500);
            
            if (string.IsNullOrEmpty(txt))
                return;
            if (Connectivity.NetworkAccess != NetworkAccess.Internet)
            {
                ConnectionError();
                return;
            }


            DrMuscleRestClient.Instance.SendMessage(new ChatModel()
            {
                ReceiverId = AppThemeConstants.ChatReceiverId,
                Message = txt
            }).ContinueWith(a => { LoadJustSentSupportMessage(); });

            messageList.Insert(0, new DrMaxMuscle.Helpers.Messages()
            {
                Message = txt,
                ProfileUrl = "",
                Nickname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value,
                UserId = LocalDBManager.Instance.GetDBSetting("email")?.Value,
                CreatedDate = DateTime.UtcNow,
            });

            if (!App.IsFreePlan)
                SendMessagestoChatBot(txt, true, 4);
            else
            {
                FreePlanUserMessage();

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    if (messageList?.Count == 1)
                    {
                        lstChats.ItemsSource = messageList;
                        lstChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);
                    }
                    if (messageList?.Count <= 4 && !IsAdmin && messageList.Where(x => x.UserId.Equals(LocalDBManager.Instance.GetDBSetting("email").Value)).ToList().Count == 1 && messageList.FirstOrDefault().UserId.Equals(LocalDBManager.Instance.GetDBSetting("email").Value))
                    {

                    }
                });
                lstSupportChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);

                if (Device.RuntimePlatform.Equals(Device.Android) && messageList.Count > 1)
                {
                    await Task.Delay(300);
                    lstSupportChats.ScrollTo(messageList[1], ScrollToPosition.End, animate: false);
                    lstSupportChats.ScrollTo(messageList[0], ScrollToPosition.Start, animate: false);

                }

            }
            LocalDBManager.Instance.SetDBSetting("IsFirstMessage", null);
            LocalDBManager.Instance.SetDBSetting("IsSecondMessage", null);
            LocalDBManager.Instance.SetDBSetting("IsFirstMessageSend", null);


            MainThread.BeginInvokeOnMainThread(() =>
            {
                if (messageList?.Count == 1)
                {
                    lstChats.ItemsSource = messageList;
                    lstChats.ScrollTo(messageList[0], ScrollToPosition.Start, animate: false);
                }


            });
            lstSupportChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);

            chatSupportInput.MessageText = "";
            if (Device.RuntimePlatform.Equals(Device.Android) && messageList.Count > 1)
            {
                await Task.Delay(300);
                lstSupportChats.ScrollTo(messageList[1], ScrollToPosition.End, animate: false);
                lstSupportChats.ScrollTo(messageList[0], ScrollToPosition.Start, animate: false);

            }

        }
        catch (Exception e)
        {

        }
    }

    private void FreePlanUserMessage()
    {
        messageList.Insert(0, new DrMaxMuscle.Helpers.Messages()
        {
            Message = "Thanks for using our service! A human will respond within 1 day, as AI chat is a premium feature and your trial has expired.\n\nSubscribe to resume AI: https://muscle.thrivecart.com/dr-muscle\n\nHuge thanks!",
            ProfileUrl = "",
            Nickname = "Dr. Muscle AI",
            UserId = "<EMAIL>",
            CreatedDate = DateTime.UtcNow,
            IsFromAI = true
        });
        DrMuscleRestClient.Instance.SendAdminMessageWithoutLoader(new ChatModel()
        {
            ReceiverId = AppThemeConstants.ChatReceiverId,
            Message = "Thanks for using our service! A human will respond within 1 day, as AI chat is a premium feature and your trial has expired.\n\nSubscribe to resume AI: https://muscle.thrivecart.com/dr-muscle\n\nHuge thanks!",
            IsFromAI = true
        }).ContinueWith(result => LoadJustSentSupportMessage());
    }

    public decimal TruncateDecimal(decimal value, int precision)
    {
        decimal step = (decimal)Math.Pow(10, precision);
        decimal tmp = Math.Truncate(step * value);
        return tmp / step;
    }
    //
    async void SendMessagestoChatBot(string msg, bool isFromUser, int noOfAttempt)
    {
        try
        {
            //var url = ;

            string chatBotId = AppThemeConstants.ChatBotId;
            string chatBotSecretKey = AppThemeConstants.ChatBotSecretKey;
            string prompt = "";

            HttpClientHandler clientHandler = new HttpClientHandler();
            clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };


            //Message
            var name = LocalDBManager.Instance.GetDBSetting("firstname")?.Value;
            //Integrate previous two messages:
            var msgHistory = new List<GptMessage>() { new GptMessage() { role = "user", content = msg } };
            try
            {

                if (messageList?.Count > 2)
                {
                    var messages = new DrMaxMuscle.Helpers.Messages[messageList.Count()];
                    Array.Copy(messageList.ToArray(), messages, messageList.Count());
                    var msgList = messages.ToList();
                    //msgList.Remove(msgList.First());

                    bool isAssistent = false;
                    foreach (var item in msgList.Take(2))
                    {
                        if (!string.IsNullOrWhiteSpace(item.Message))
                        {
                            if (item.IsFromAI && isAssistent)
                                continue;
                            else
                            {
                                isAssistent = false;
                            }
                            if (item.IsFromAI)
                                isAssistent = true;
                            msgHistory.Add(new GptMessage() { role = item.IsFromAI ? "assistant" : "user", content = item.Message });
                        }
                    }


                }

            }
            catch (Exception ex)
            {

            }

            DrMuscleTyping.IsVisible = true;
            //TO SEND INFORMATION ABOUT SOME USER INFORMATION

            try
            {
                bool isKg = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ? true : false;
                decimal _userBodyWeight = 0;
                if (LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value != null)
                {
                    _userBodyWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value, CultureInfo.InvariantCulture);
                    Config.CurrentWeight = _userBodyWeight.ToString();
                }

                decimal goalWeight = 0;

                if (LocalDBManager.Instance.GetDBSetting("WeightGoal")?.Value != null)
                {
                    goalWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("WeightGoal")?.Value.Replace(",", "."), CultureInfo.InvariantCulture);

                }

                var userstats = "";
                if (isKg)
                {
                    userstats = string.Format("Client name: {0}\nBody weight:  {1:0.##} {2}", name, Math.Round(_userBodyWeight, 2), "kg");
                    if (goalWeight != 0)
                        userstats += string.Format("\nTarget body weight: {0:0.##} {1}", Math.Round(goalWeight, 2), "kg");
                }
                else
                {

                    var truncateWeight = TruncateDecimal(_userBodyWeight, 3);
                    var lbWeight = new MultiUnityWeight(truncateWeight, "kg").Lb;
                    userstats = string.Format("Client name: {0}\nBody weight:  {1:0.##} {2}", name, Math.Round(lbWeight, 2), "lbs");
                    if (goalWeight != 0)
                    {
                        userstats += string.Format("\nTarget body weight: {0:0.##} {1}", Math.Round(new MultiUnityWeight((decimal)goalWeight, "kg").Lb, 2), "lbs");
                    }
                }
                if (_userBodyWeight < goalWeight)
                {
                    userstats += "\nGoal: gain muscle mass";
                }
                else
                {
                    userstats += "\nGoal: build lean muscle and burn fat";
                }

                userstats += $"\nAge: {LocalDBManager.Instance.GetDBSetting("Age")?.Value}";

                var homePagedata = await GetStatsMessage();
                var OneRMData = await Get1RMMessage();
                if (!string.IsNullOrEmpty(homePagedata))
                    userstats += $"{homePagedata}";
                //if (!string.IsNullOrEmpty(OneRMData))
                //    userstats += $"\n\nBelow are 1RM history (1 RM, one RM, 1 reps max, max strength)\n\n{OneRMData}";
                //
                if (!string.IsNullOrEmpty(OneRMData) && OneRMData.Count() > 3000)
                    OneRMData = OneRMData.Remove(2999);
                msgHistory.Add(new GptMessage() { role = "system", content = $"{userstats} 1RM of last 3 workouts for 15 most recent exercises (1 RM, one RM, 1 reps max, max strength): {OneRMData}" });
                //msgHistory.Add(new GptMessage() { role = "system", content = $"" });

            }
            catch (Exception ex)
            {


            }
            msgHistory?.Reverse();
            try
            {
                using (var _httpClient = new HttpClient(clientHandler))
                {
                    _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", chatBotSecretKey);
                    _httpClient.Timeout = TimeSpan.FromSeconds(15);
                    //string model = "gpt-3.5-turbo";//"text-davinci-002"; // model for GPT-3.5 Turbo
                    string model = "gpt-4o-mini";//"text-davinci-002"; // model for GPT-3.5 Turbo
                    var requestUrl = "https://www.chatbase.co/api/v1/chat";
                    var requestBody = new
                    {
                        messages = msgHistory,
                        model = model,
                        temperature = 0,
                        stream = true,
                        chatbotId = chatBotId,
                        conversationId = LocalDBManager.Instance.GetDBSetting("email")?.Value
                    };
                    var requestBodyJson = Newtonsoft.Json.JsonConvert.SerializeObject(requestBody);
                    var requestContent = new StringContent(requestBodyJson, System.Text.Encoding.UTF8, "application/json");

                    var response = await _httpClient.SendAsync(new HttpRequestMessage(HttpMethod.Post, requestUrl)
                    {
                        Content = requestContent
                    });

                    // var response = await _httpClient.PostAsync(requestUrl, requestContent);


                    if (response == null)
                    {
                        if (noOfAttempt > 0)
                        {
                            noOfAttempt -= 1;
                            SendMessagestoChatBot(msg, isFromUser, noOfAttempt);
                            return;
                        }

                        await ConnectionError();
                        noOfAttempt = 4;
                        SendMessagestoChatBot(msg, isFromUser, noOfAttempt);
                        //DrMuscleTyping.IsVisible = false;
                        return;
                    }
                    DrMuscleTyping.IsVisible = false;
                    var responseBodyJson = await response.Content.ReadAsStringAsync();

                    var chatResponse = responseBodyJson;


                    if (string.IsNullOrEmpty(chatResponse))
                    {

                    }
                    else
                    {
                        if (chatResponse.ToLower().Contains("message\":\"incorrect api key"))
                        {
                            SendMessagestoChatBot(msg, isFromUser, noOfAttempt);
                            return;
                        }
                        else if (chatResponse.ToLower().Contains("\"code\":\"error\""))
                        {
                            messageList.Insert(0, new DrMaxMuscle.Helpers.Messages()
                            {
                                Message = "Oops! Could not load message. Please try again later. Contact us if the issue persists.",
                                ProfileUrl = "",
                                Nickname = "Dr. Muscle AI",
                                UserId = "<EMAIL>",
                                CreatedDate = DateTime.UtcNow,
                                IsFromAI = true
                            });
                        }
                        else
                        {
                            //Show message to user and send it to our server
                            var txt = chatSupportInput.MessageText.Trim();

                            messageList.Insert(0, new DrMaxMuscle.Helpers.Messages()
                            {
                                Message = chatResponse,
                                ProfileUrl = "",
                                Nickname = "Dr. Muscle AI",
                                UserId = "<EMAIL>",
                                CreatedDate = DateTime.UtcNow,
                                IsFromAI = true
                            });
                        }
                        
                        //#if ANDROID
                        //                        (lstSupportChats.Handler as CustomCollectionViewHandler)?.ScrollByPixels(-350);
                        //#elif IOS
                        //                        //(lstSupportChats.Handler as CustomCollectionViewHandler)?.ScrollByPixels(-350);
                        //#endif
                        if (messageList?.Count > 1)
                        {
                            lstChats.ScrollTo(messageList[1], ScrollToPosition.End, animate: false);
                            await Task.Delay(150);
                            lstChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.End, animate: true);

                        }
                        MainThread.BeginInvokeOnMainThread(() =>
                        {
                            if (messageList?.Count == 1)
                            {
                                lstChats.ItemsSource = messageList;
                                lstChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);
                            }
                            if (messageList?.Count <= 4 && !IsAdmin && messageList.Where(x => x.UserId.Equals(LocalDBManager.Instance.GetDBSetting("email").Value)).ToList().Count == 1 && messageList.FirstOrDefault().UserId.Equals(LocalDBManager.Instance.GetDBSetting("email").Value))
                            {

                            }
                        });

                        lstSupportChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);

                        //if (Device.RuntimePlatform.Equals(Device.Android) && messageList.Count > 1)
                        //{
                        //    await Task.Delay(300);
                        //    lstSupportChats.ScrollTo(messageList[1], ScrollToPosition.End, animate: false);
                        //    lstSupportChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);

                        //}
                        DrMuscleRestClient.Instance.SendAdminMessageWithoutLoader(new ChatModel()
                        {
                            ReceiverId = AppThemeConstants.ChatReceiverId,
                            Message = chatResponse,
                            IsFromAI = true
                        }).ContinueWith(result => LoadJustSentSupportMessage());


                    }
                }
            }
            catch (Exception ex)
            {
                if (noOfAttempt > 0)
                {
                    noOfAttempt -= 1;
                    SendMessagestoChatBot(msg, isFromUser, noOfAttempt);
                    return;
                }
                await ConnectionError();
                noOfAttempt = 4;
                SendMessagestoChatBot(msg, isFromUser, noOfAttempt);

            }
        }
        catch (Exception exc)
        {

        }
    }

    async Task<string> GetStatsMessage()
    {
        var stasts = "";
        try
        {
            //Weight Charts changes 
            if (!string.IsNullOrEmpty(CurrentLog.Instance.WeightChangedPercentage))
                stasts += $"\nBody weight progress: {CurrentLog.Instance.WeightChangedPercentage}";

            // Coach Tips Card 
            if (!string.IsNullOrEmpty(CurrentLog.Instance.CoachTipsText))
                stasts += $" ({CurrentLog.Instance.CoachTipsText})";

        }
        catch (Exception e)
        {

        }

        //GET Calories, Fat, Protein, Carbs
        try
        {
            decimal _userBodyWeight = 0;
            if (LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value != null)
            {
                _userBodyWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value, CultureInfo.InvariantCulture);
                Config.CurrentWeight = _userBodyWeight.ToString();
            }
            decimal _targetIntake = 0;
            if (LocalDBManager.Instance.GetDBSetting("TargetIntake")?.Value != null)
            {
                _targetIntake = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("TargetIntake").Value.ReplaceWithDot(), CultureInfo.InvariantCulture);
            }
            string macros = $"Target caloric intake: {Math.Round(_targetIntake)}";
            //macros += $"\nCarbs range: " + Math.Round((double)_targetIntake * 0.45 / 3.87) + "-" + Math.Round((double)_targetIntake * 0.65 / 3.87) + " g";


            //macros += $"\nProtein range: ";

            //if (LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg")
            //{
            //    macros += Math.Round(new MultiUnityWeight((decimal)_userBodyWeight, "kg").Kg * (decimal)1.6) + "-" + Math.Round(new MultiUnityWeight((decimal)_userBodyWeight, "kg").Kg * (decimal)2.2) + " g";
            //}
            //else
            //{
            //    macros += Math.Round(new MultiUnityWeight((decimal)_userBodyWeight, "kg").Lb * (decimal)0.7) + "-" + Math.Round(new MultiUnityWeight((decimal)_userBodyWeight, "kg").Lb * (decimal)1.0) + " g";
            //}

            //macros += $"\nFats range: ";
            //macros += Math.Round((double)_targetIntake * 0.2 / 9) + "-" + Math.Round((double)_targetIntake * 0.35 / 9) + " g";

            var protein = $"{Math.Round(_userBodyWeight * (decimal)1.8)} - {Math.Round(_userBodyWeight * (decimal)2.5)} g";
            macros += $"\nProtein range: {protein}";
            var newLowerTargetIntake = _targetIntake - (Math.Round(_userBodyWeight * (decimal)2.15) * 4);
            var newHigherTargetIntake = _targetIntake - (Math.Round(_userBodyWeight * (decimal)2.15) * 4);

            var carbs = $"{Math.Round((double)newLowerTargetIntake * 0.2 / 4)} - {Math.Round((double)newHigherTargetIntake * 0.8 / 4)} g";
            macros += $"\nCarbs range: {carbs}";

            var fats = $"{Math.Round((double)newLowerTargetIntake * 0.2 / 9)} - {Math.Round((double)newHigherTargetIntake * 0.8 / 9)} g";
            macros += $"\nFats range: {fats}";

            stasts += $"\n{macros}";
        }
        catch (Exception e)
        {
            Console.WriteLine(e);

        }

        try
        {
            var statsModel = new BotModel()
            {
                Type = BotType.Stats
            };
            var workouts = ((App)Application.Current).UserWorkoutContexts.workouts;
            var levelUpBotModel = new BotModel();
            bool IsEstimated = false;
            levelUpBotModel.Type = BotType.LevelUp;

            DateTime? creationDate = null;
            try
            {
                creationDate = new DateTime(Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("creation_date").Value));
            }
            catch (Exception)
            {

            }
            //TODO: changed for New UI
            //lstChats.IsVisible = true;
            lstChats.IsVisible = false;

            if (workouts != null)
            {
                if (workouts.Sets != null)
                {
                    if (workouts.Averages.Count > 1)
                    {
                        if (workouts.Averages[1].Average.Kg == 0)
                            IsEstimated = true;
                        OneRMAverage last = workouts.Averages.ToList()[workouts.Averages.Count - 1];
                        OneRMAverage before = workouts.Averages.ToList()[workouts.Averages.Count - 2];
                        decimal progresskg = (last.Average.Kg - before.Average.Kg) * 100 / (before.Average.Kg < 1 ? 1 : before.Average.Kg);
                        bool inKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg";
                        // strProgress += String.Format("- {0}: {1}{2} ({3}%)\n", AppResources.MaxStrength, (last.Average.Kg - before.Average.Kg) > 0 ? "+" : "", inKg ? Math.Round(last.Average.Kg - before.Average.Kg) + " kg" : Math.Round(last.Average.Lb - before.Average.Lb) + " lbs", Math.Round(progresskg)).ReplaceWithDot();
                        if ((last.Average.Kg - before.Average.Kg) >= 0)
                        {
                            statsModel.StrengthPerText = String.Format("Last 3 weeks, your strength went {0}{1}% (on average).", (last.Average.Kg - before.Average.Kg) >= 0 ? "+" : "", Math.Round(progresskg)).ReplaceWithDot();


                            //StrengthArrowText.Text =  statsModel.StrengthPerText;
                            //statsModel.StrengthMessage = String.Format(" {0}{1} {2}", (last.Average.Kg - before.Average.Kg) >= 0 ? "+ " : "", inKg ? Math.Round(last.Average.Kg - before.Average.Kg) + " kg" : Math.Round(last.Average.Lb - before.Average.Lb) + " lbs", AppResources.MaxStrength).ReplaceWithDot();
                            statsModel.StrengthImage = "up_arrow.png";
                            //Green
                            statsModel.StrengthTextColor = Color.FromHex("#5CD196");
                            //StrengthArrowText.TextColor = Color.FromHex("#5CD196");
                            var perceStr = "";
                            if (before.Average.Kg == 0)
                            {
                                perceStr = String.Format("{0}!", Math.Round(inKg ? last.Average.Kg : last.Average.Lb)).ReplaceWithDot();
                                stasts += String.Format("\nStrength progress: {0} {1}{2} over the past few weeks", "Average 1RM up", (last.Average.Kg - before.Average.Kg) >= 0 ? "" : "", Math.Round(inKg ? last.Average.Kg : last.Average.Lb)).ReplaceWithDot();
                            }
                            else
                            {
                                perceStr = String.Format("{0}%!", Math.Round(progresskg)).ReplaceWithDot();
                                stasts += String.Format("\nStrength progress: {0} {1}{2}% over the past few weeks", "Average 1RM up", (last.Average.Kg - before.Average.Kg) >= 0 ? "" : "", Math.Round(progresskg)).ReplaceWithDot();
                            }

                        }
                        else
                        {
                            var perceStr = "";
                            perceStr = String.Format("{0}%", Math.Round(progresskg)).ReplaceWithDot().Replace("-", "");
                            //LblStrengthProgress.Text = String.Format("{0} {1}{2}!", LblStrengthProgress.Text, (last.Average.Kg - before.Average.Kg) >= 0 ? "+" : "", Math.Round(progresskg)).ReplaceWithDot();
                            //statsModel.StrengthMessage = String.Format(" {0}{1} {2}", (last.Average.Kg - before.Average.Kg) >= 0 ? "+ " : "", inKg ? Math.Round(last.Average.Kg - before.Average.Kg) + " kg" : Math.Round(last.Average.Lb - before.Average.Lb) + " lbs", AppResources.MaxStrength).ReplaceWithDot();
                            //Red
                            //StrengthArrowText.TextColor = Color.FromHex("#BA1C31");
                            stasts = String.Format("\nStrength progress: {0} {1}{2}% over the past few weeks", "Average 1RM down", (last.Average.Kg - before.Average.Kg) >= 0 ? "" : "", Math.Round(progresskg)).ReplaceWithDot().Replace("-", "");


                        }
                        //statsModel.StrengthMessage = AppResources.MaxStrength;
                        workouts.Sets.Reverse();
                        workouts.SetsDate.Reverse();

                        if (workouts.Sets?.Count > 1)
                        {
                            bool isflg = false;
                            foreach (var set in workouts.Sets)
                            {
                                if (set != 0)
                                    isflg = true;
                            }

                            int firstSets = workouts.Sets[workouts.Sets.Count - 1];
                            int lastSets = workouts.Sets[workouts.Sets.Count - 2];
                            try
                            {
                                decimal progressSets = (firstSets - lastSets) * 100 / (lastSets == 0 ? 1 : lastSets);
                                if (firstSets == 0)
                                {
                                    progressSets = lastSets;
                                }
                                // strProgress += String.Format("- {0}: {1}{2} ({3}%)\n", AppResources.WorkSetsNoColon, (firstSets - lastSets) >= 0 ? "+" : "", firstSets - lastSets, Math.Round(progressSets)).ReplaceWithDot();
                                if ((firstSets - lastSets) >= 0)
                                {

                                    if (lastSets == 0)
                                        stasts += String.Format("\nVolume progress: {0} {1}{2} in the last 7 days", "Work sets up", (firstSets - lastSets) >= 0 ? "" : "", firstSets);
                                    else
                                        stasts += String.Format("\nVolume progress: {0} {1}{2}% in the last 7 days", "Work sets up", (firstSets - lastSets) >= 0 ? "" : "", Math.Round(progressSets));
                                }
                                else
                                {

                                    if (firstSets == 0)
                                        stasts += String.Format("\nVolume progress: {0} {1}{2} in the last 7 days", "Work sets down", (firstSets - lastSets) >= 0 ? "" : "", firstSets);
                                    else
                                        stasts += String.Format("\nVolume progress:{0} {1}{2}% in the last 7 days", "Work sets down", (firstSets - lastSets) >= 0 ? "" : "", Math.Round(progressSets)).Replace("-", "");
                                }

                            }
                            catch (Exception ex)
                            {
                            }
                        }

                        workouts.Sets.Reverse();
                        workouts.SetsDate.Reverse();

                        try
                        {
                            levelUpBotModel.Type = BotType.LevelUp;



                            var exerciseModel = workouts.HistoryExerciseModel;
                            if (exerciseModel != null)
                            {
                                var unit = inKg ? AppResources.Kg.ToLower() : AppResources.Lbs.ToLower();
                                var weightLifted = inKg ? exerciseModel.TotalWeight.Kg : exerciseModel.TotalWeight.Lb;
                                stasts += $"\nTotals: {exerciseModel.TotalWorkoutCompleted} workouts done";
                                stasts += inKg
                                    ? $" ({Math.Round(weightLifted)} kg lifted)"
                                    : $" ({Math.Round(weightLifted)} lbs lifted)";
                            }
                        }
                        catch (Exception ex)
                        {

                        }

                        try
                        {


                            if (!string.IsNullOrWhiteSpace(workouts?.GetUserProgramInfoResponseModel?.RecommendedProgram?.Label))
                            {
                                stasts += $"\nCurrent program {workouts?.GetUserProgramInfoResponseModel?.RecommendedProgram?.Label}";
                            }
                        }
                        catch (Exception ex)
                        {

                        }

                        try
                        {
                            if (workouts != null && workouts.GetUserProgramInfoResponseModel != null)
                            {
                                if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram != null)
                                {
                                    if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.RemainingToLevelUp != null)
                                        stasts += $" ({(int)workouts.GetUserProgramInfoResponseModel.RecommendedProgram.RemainingToLevelUp} workouts before next program)";
                                }
                            }
                        }
                        catch (Exception exception)
                        {

                        }

                        //Calculate week streak
                        if (workouts.ConsecutiveWeeks != null && workouts.ConsecutiveWeeks?.Count > 0)
                        {
                            var lastTime = workouts.ConsecutiveWeeks.LastOrDefault();
                            var year = Convert.ToString(lastTime.MaxWeek).Substring(0, 4);
                            var weekOfYear = Convert.ToString(lastTime.MaxWeek).Substring(4, 2);
                            CultureInfo myCI = new CultureInfo("en-US");
                            Calendar cal = myCI.Calendar;

                            if (int.Parse(year) == DateTime.Now.Year)
                            {
                                var currentWeekOfYear = cal.GetWeekOfYear(DateTime.Now, CalendarWeekRule.FirstDay, DayOfWeek.Monday);
                                if (int.Parse(weekOfYear) >= currentWeekOfYear)
                                {
                                    levelUpBotModel.ChainCount = Convert.ToString(lastTime.ConsecutiveWeeks);
                                }

                                else if (int.Parse(weekOfYear) == currentWeekOfYear - 1)
                                {
                                    levelUpBotModel.ChainCount = Convert.ToString(lastTime.ConsecutiveWeeks);
                                }
                            }

                            stasts += $"\nWorkout week streak: {levelUpBotModel.ChainCount}";
                        }


                        try
                        {

                            bool IsInserted = false;
                            //await AddAnswer(AppResources.GotIt);
                            //Today workout
                            TimeSpan timeSpan;
                            String dayStr = "days";
                            int days = 0;
                            int hours = 0;
                            int minutes = 0;
                            timeSpan = new TimeSpan(days, hours, minutes);
                            CurrentLog.Instance.IsRest = false;
                            if (workouts.LastWorkoutDate != null)
                            {

                                days = (int)(DateTime.Now - ((DateTime)workouts.LastWorkoutDate).ToLocalTime())
                                    .TotalDays;
                                hours = (int)(DateTime.Now - ((DateTime)workouts.LastWorkoutDate).ToLocalTime())
                                    .TotalHours;
                                minutes = (int)(DateTime.Now -
                                                ((DateTime)workouts.LastWorkoutDate).ToLocalTime())
                                    .TotalMinutes;
                                if (days > 0)
                                    dayStr = days == 1 ? "day" : "days";
                                else if (hours > 0 && hours < 72)
                                    dayStr = hours <= 1 ? "hour" : "hours";
                                else if (minutes < 60)
                                    dayStr = minutes <= 1 ? "minute" : "minutes";

                                var d = 0;
                                if (days > 0)
                                    d = days;
                                else
                                {
                                    d = timeSpan.Days;
                                    //hours = (int)timeSpan.TotalHours;
                                    //minutes = (int)timeSpan.TotalMinutes;
                                    if (days > 0)
                                        dayStr = d == 1 ? "day" : "days";
                                    else if (hours > 0 && hours < 72)
                                        dayStr = hours <= 1 ? "hour" : "hours";
                                    else if (minutes < 60)
                                        dayStr = minutes <= 1 ? "minute" : "minutes";


                                }
                            }
                            else if (workouts.Averages.Count > 1)
                            {
                                timeSpan = DateTime.Now.ToLocalTime()
                                    .Subtract(workouts.Averages[0].Date.ToLocalTime());
                                days = timeSpan.Days;
                                dayStr = timeSpan.Days == 1 ? "day" : "days";
                            }

                            var lifted = new BotModel();
                            lifted.Type = BotType.Lifted;


                            if (workouts.LastConsecutiveWorkoutDays > 1 && workouts.LastWorkoutDate != null &&
                                (DateTime.Now - ((DateTime)workouts.LastWorkoutDate).ToLocalTime()).TotalDays <
                                2 && workouts != null && workouts.GetUserProgramInfoResponseModel != null &&
                                workouts.GetUserProgramInfoResponseModel.RecommendedProgram != null &&
                                workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate != null &&
                                workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.IsSystemExercise)
                            {
                                var RequiredHours = 18;
                                if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower()
                                        .Contains("bodyweight") ||
                                    workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower()
                                        .Contains("mobility") ||
                                    workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower()
                                        .Contains("powerlifting") ||
                                    workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower()
                                        .Contains("full-body") || workouts.GetUserProgramInfoResponseModel
                                        .RecommendedProgram.Label.ToLower().Contains("bands"))
                                {
                                    RequiredHours = 42;
                                }
                                else if
                                    (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower()
                                         .Contains("[home] push") ||
                                     workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower()
                                         .Contains("[home] pull") ||
                                     workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower()
                                         .Contains("[home] legs") ||
                                     workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower()
                                         .Contains("[gym] push") ||
                                     workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower()
                                         .Contains("[gym] pull") || workouts.GetUserProgramInfoResponseModel
                                         .RecommendedProgram.Label.ToLower().Contains("[gym] legs"))

                                {
                                    RequiredHours = 18;
                                    if (workouts.LastConsecutiveWorkoutDays > 5 &&
                                        workouts.LastWorkoutDate != null &&
                                        (DateTime.Now - ((DateTime)workouts.LastWorkoutDate).ToLocalTime())
                                        .TotalDays < 3)
                                    {
                                        RequiredHours = 42;
                                    }
                                }
                                else if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                         .ToLower().Contains("split"))
                                {
                                    RequiredHours = 42;
                                }

                                if (hours < RequiredHours)
                                {
                                    lifted.StrengthTextColor = AppThemeConstants.DarkRedColor;
                                    var h = RequiredHours - hours <= 1 ? "hour" : "hours";
                                    lifted.TrainRest = "Rest";
                                    CurrentLog.Instance.IsRest = true;
                                    lifted.SinceTime = $"{hours} {h} since last workout (suggested recovery time: {RequiredHours} hours, so continue resting)"; ;
                                    lifted.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                                    //lifted.TrainRestText = $"More to recover".ToLower().FirstCharToUpper();
                                    lifted.TrainRestText =
                                        "Coach says"; // "Fatigued";//$"At least {RequiredHours - hours} {h} more to recover".ToLower().FirstCharToUpper();

                                }
                                else
                                {
                                    lifted.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                                    lifted.StrengthTextColor = AppThemeConstants.GreenColor;
                                    var h = RequiredHours - hours <= 1 ? "hour" : "hours";
                                    lifted.TrainRest = "Train";
                                    lifted.SinceTime = $"{hours} {h} since last workout (suggested recovery time: {RequiredHours} hours, so train now)"; ; //hours.ToString();
                                                                                                                                                           //lifted.TrainRestText = $"More to recover".ToLower().FirstCharToUpper();
                                    lifted.TrainRestText = $"Coach says";

                                }

                                //BotList.Add(restModel);
                                IsInserted = true;
                                // await AddQuestion($"{AppResources.YouHaveBeenWorkingOut} {workouts.LastConsecutiveWorkoutDays} {AppResources.DaysInARowISuggestTalkingADayOffAreYouSureYouWantToWorkOutToday} Up next is {workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.Label}.");
                            }
                            else if (workouts.LastWorkoutDate != null)
                            {
                                if (days > 0 && hours >= 42)
                                {
                                    lifted.SinceTime = $"{days} {dayStr}, so train now";
                                    lifted.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                                    lifted.TrainRest = "Train";
                                    lifted.StrengthTextColor = AppThemeConstants.GreenColor;
                                    lifted.TrainRestText =
                                        "Coach says"; // "Recovered";// (days > 9 ? "I may recommend lighter weights" : "You should have recovered").ToLower().FirstCharToUpper();
                                                      //BotList.Add(restModel);
                                    IsInserted = true;
                                    //await AddQuestion(days > 9 ? $"{AppResources.YourLastWorkoutWas} {days} {dayStr} ago. I may recommend a light session. Start planned workout?" : $"Your last workout was {days} {dayStr} ago. You should have recovered. Up next is {workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.Label}.");
                                }
                                else if (hours > 0)
                                {
                                    var RequiredHours = 18;
                                    if (workouts != null && workouts.GetUserProgramInfoResponseModel != null &&
                                        workouts.GetUserProgramInfoResponseModel.RecommendedProgram != null &&
                                        workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate != null &&
                                        workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate
                                            .IsSystemExercise)
                                    {
                                        if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                .ToLower().Contains("bodyweight") ||
                                            workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                .ToLower().Contains("mobility") ||
                                            workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                .ToLower().Contains("powerlifting") ||
                                            workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                .ToLower().Contains("full-body") ||
                                            workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                .ToLower().Contains("bands"))
                                        {
                                            RequiredHours = 42;
                                            if (!string.IsNullOrEmpty(LocalDBManager.Instance
                                                    .GetDBSetting("Age")?.Value))
                                            {
                                                if (int.Parse(
                                                        LocalDBManager.Instance.GetDBSetting("Age")?.Value) <
                                                    30)
                                                    RequiredHours = 18;
                                            }

                                            if (workouts.LastConsecutiveWorkoutDays > 1 &&
                                                workouts.LastWorkoutDate != null &&
                                                (DateTime.Now -
                                                 ((DateTime)workouts.LastWorkoutDate).ToLocalTime()).TotalDays <
                                                2)
                                                RequiredHours = 42;
                                        }
                                        else if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram
                                                 .Label.ToLower().Contains("split"))
                                        {
                                            RequiredHours = 18;
                                            if (!string.IsNullOrEmpty(LocalDBManager.Instance
                                                    .GetDBSetting("Age")?.Value))
                                            {
                                                if (int.Parse(
                                                        LocalDBManager.Instance.GetDBSetting("Age")?.Value) >
                                                    50)
                                                    RequiredHours = 42;
                                            }
                                        }
                                    }

                                    if (hours < RequiredHours)
                                    {

                                        lifted.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                                        lifted.StrengthTextColor = AppThemeConstants.DarkRedColor;
                                        var h = RequiredHours - hours <= 1 ? "hour" : "hours";
                                        lifted.TrainRest = "Rest";
                                        CurrentLog.Instance.IsRest = true;
                                        lifted.SinceTime =
                                            $"{hours} {h} since last workout (suggested recovery time: {RequiredHours} hours, so  continue resting)"; //$"{hours}/{RequiredHours} {h}"; //hours.ToString();
                                                                                                                                                      //lifted.TrainRestText = $"More to recover".ToLower().FirstCharToUpper();
                                        lifted.TrainRestText =
                                            "Coach says"; // "Fatigued";//$"At least {RequiredHours - hours} {h} more to recover".ToLower().FirstCharToUpper();

                                        //BotList.Add(restModel);
                                        IsInserted = true;
                                        //await AddQuestion($"Your last workout was {hours} {dayStr} ago. I'm not sure it makes sense to work out again now... Up next is {workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.Label}.");
                                    }
                                    else
                                    {
                                        var h = hours <= 1 ? "hour" : "hours";
                                        lifted.SinceTime = $"{hours} {h} since last workout (suggested recovery time: {RequiredHours} hours, so train now)"; ;
                                        lifted.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                                        lifted.TrainRest = "Train";
                                        lifted.StrengthTextColor = AppThemeConstants.GreenColor;
                                        lifted.TrainRestText =
                                            "Coach says"; // "Recovered";// "You should have recovered".ToLower().FirstCharToUpper();
                                                          //BotList.Add(restModel);
                                        IsInserted = true;
                                        // await AddQuestion($"Your last workout was {hours} {dayStr} ago. You should have recovered. Up next is {workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.Label}.");
                                    }

                                }
                                else
                                {
                                    var RequiredHours = 18;
                                    if (workouts != null && workouts.GetUserProgramInfoResponseModel != null &&
                                        workouts.GetUserProgramInfoResponseModel.RecommendedProgram != null &&
                                        workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate != null &&
                                        workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate
                                            .IsSystemExercise)
                                    {
                                        if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                .ToLower().Contains("bodyweight") ||
                                            workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                .ToLower().Contains("mobility") ||
                                            workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                .ToLower().Contains("powerlifting") ||
                                            workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                .ToLower().Contains("full-body") ||
                                            workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                .ToLower().Contains("bands"))
                                        {
                                            RequiredHours = 42;
                                            if (!string.IsNullOrEmpty(LocalDBManager.Instance
                                                    .GetDBSetting("Age")?.Value))
                                            {
                                                if (int.Parse(
                                                        LocalDBManager.Instance.GetDBSetting("Age")?.Value) <
                                                    30)
                                                    RequiredHours = 18;
                                            }

                                            if (workouts.LastConsecutiveWorkoutDays > 1 &&
                                                workouts.LastWorkoutDate != null &&
                                                (DateTime.Now -
                                                 ((DateTime)workouts.LastWorkoutDate).ToLocalTime()).TotalDays <
                                                2)
                                                RequiredHours = 42;
                                        }
                                        else if
                                            (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                 .ToLower().Contains("[home] push") ||
                                             workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                 .ToLower().Contains("[home] pull") ||
                                             workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                 .ToLower().Contains("[home] legs") ||
                                             workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                 .ToLower().Contains("[gym] push") ||
                                             workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                 .ToLower().Contains("[gym] pull") ||
                                             workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label
                                                 .ToLower().Contains("[gym] legs"))
                                        {
                                            RequiredHours = 18;
                                            if (workouts.LastConsecutiveWorkoutDays > 5 &&
                                                workouts.LastWorkoutDate != null &&
                                                (DateTime.Now -
                                                 ((DateTime)workouts.LastWorkoutDate).ToLocalTime()).TotalDays <
                                                3)

                                            {
                                                RequiredHours = 42;
                                            }
                                        }
                                        else if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram
                                                 .Label.ToLower().Contains("split"))
                                        {
                                            RequiredHours = 18;
                                            if (!string.IsNullOrEmpty(LocalDBManager.Instance
                                                    .GetDBSetting("Age")?.Value))
                                            {
                                                if (int.Parse(
                                                        LocalDBManager.Instance.GetDBSetting("Age")?.Value) >
                                                    50)
                                                    RequiredHours = 42;
                                            }

                                        }
                                    }

                                    lifted.SinceTime = $"0/{RequiredHours} hours, so continue resting"; //minutes.ToString();
                                    lifted.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                                    lifted.TrainRest = "Rest";
                                    CurrentLog.Instance.IsRest = true;
                                    lifted.StrengthTextColor = AppThemeConstants.DarkRedColor;
                                    lifted.TrainRestText =
                                        "Coach says"; // "Fatigued";//$"{RequiredHours} hours more to recover".ToLower().FirstCharToUpper();

                                    //BotList.Add(restModel);
                                    IsInserted = true;
                                    //await AddQuestion($"Your last workout was {minutes} {dayStr} ago. I'm not sure it makes sense to work out again today... Up next is {workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.Label}.");
                                }
                            }

                            if (!IsInserted)
                            {

                                if (LocalDBManager.Instance.GetDBSetting("BodyWeight") != null)
                                {
                                    var value = Convert.ToDecimal(
                                        LocalDBManager.Instance.GetDBSetting("BodyWeight").Value
                                            .Replace(",", "."),
                                        System.Globalization.CultureInfo.InvariantCulture);
                                    var weight1 = new MultiUnityWeight(value, "kg");
                                    lifted.LbsLifted = string.Format("{0:0.##}",
                                        LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg"
                                            ? weight1.Kg
                                            : weight1.Lb);
                                }
                                else
                                    lifted.LbsLifted = "N/A"; //_weightLifted;

                                lifted.LbsLiftedText = "Body weight";

                                lifted.SinceTime = "N/A";
                                lifted.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                                lifted.TrainRest = "Train";
                                lifted.StrengthTextColor = AppThemeConstants.GreenColor;
                                lifted.TrainRestText =
                                    "Coach says"; // "Recovered";// "No workout yet".ToLower().FirstCharToUpper();
                                                  //BotList.Add(restModel);
                            }

                            stasts += $"\nRecovery time: {lifted.SinceTime}";

                        }
                        catch (Exception ex)
                        {
                        }
                    }


                }







                if (workouts?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate?.Exercises?.Count > 0)
                {
                    var count = workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.Exercises.Count;
                    var exe = count > 1 ? "exercises" : "exercise";
                    stasts +=
                        $"\nNext workout:{workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.Label}";
                    stasts +=
                        $" with {count} {exe} (estimated duration: {count * 8} min)";
                }


            }

        }
        catch (IndexOutOfRangeException ex)
        {

        }
        catch (Exception ex)
        {

        }
        return stasts;
    }

    async Task<string> Get1RMMessage()
    {
        try
        {
            var lists = LocalDBManager.Instance.GetDBAllExercise1RM();
            var OneRMList = "";
            bool isKg = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg";
            lists.Reverse();
            List<List<OneRMModel>> mainList = new List<List<OneRMModel>>();
            foreach (var item in lists)
            {
                var oneRMModel = JsonConvert.DeserializeObject<List<OneRMModel>>(item.Last1RM);
                if (oneRMModel.FirstOrDefault(x => x.IsAllowDelete) == null)
                    continue;
                mainList.Add(oneRMModel);
            }

            //Let's sort it
            List<List<OneRMModel>> sortedList = mainList
    .OrderByDescending(innerList => innerList.Max(item => item.LastLogDate))
    .Take(15).ToList();
            foreach (var oneRMModel in sortedList)
            {
                OneRMList += $"{oneRMModel.FirstOrDefault()?.Label}: ";
                foreach (var lastOneRM in oneRMModel)
                {
                    if (lastOneRM.IsAllowDelete)
                        OneRMList += isKg ? $"{string.Format("{0:0.##}", lastOneRM.OneRM.Kg).ReplaceWithDot()} kg, " : $"{string.Format("{0:0.##}", lastOneRM.OneRM.Lb).ReplaceWithDot()} lbs, ";
                }
                OneRMList += "\n";
            }
            OneRMList = Regex.Replace(OneRMList, @"[^\u0000-\u007F]+", string.Empty);
            return OneRMList;

        }
        catch (Exception ex)
        {
            return "";
        }
    }

    async void FullReport_Clicked()
    {
        AiButton.IsVisible = false;

        var txt = "More tips";
        DrMuscleRestClient.Instance.SendMessage(new ChatModel()
        {
            ReceiverId = AppThemeConstants.ChatReceiverId,
            Message = txt
        });
        //if (!IsAdmin)
        if (!App.IsFreePlan)
        {
            try
            {
                bool isKg = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ? true : false;
                decimal _userBodyWeight = 0;
                if (LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value != null)
                {
                    _userBodyWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value, CultureInfo.InvariantCulture);
                    Config.CurrentWeight = _userBodyWeight.ToString();
                }

                decimal goalWeight = 0;

                if (LocalDBManager.Instance.GetDBSetting("WeightGoal")?.Value != null)
                {
                    goalWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("WeightGoal")?.Value.Replace(",", "."), CultureInfo.InvariantCulture);

                }
                var userstats = "";
                if (isKg)
                {
                    userstats = string.Format("My body weight {0:0.##} {1}", Math.Round(_userBodyWeight, 2), "kg");
                    if (goalWeight != 0)
                        userstats += string.Format("\nMy target body weight {0:0.##} {1}", Math.Round(goalWeight, 2), "kg");
                }
                else
                {

                    var truncateWeight = TruncateDecimal(_userBodyWeight, 3);
                    var lbWeight = new MultiUnityWeight(truncateWeight, "kg").Lb;
                    userstats = string.Format("My body weight {0:0.##} {1}", Math.Round(lbWeight, 2), "lbs");
                    if (goalWeight != 0)
                    {
                        userstats += string.Format("\nMy target body weight {0:0.##} {1}", Math.Round(new MultiUnityWeight((decimal)goalWeight, "kg").Lb, 2), "lbs");
                    }
                }
                if (_userBodyWeight > goalWeight)
                {
                    userstats += "\nGoal to gain weight";
                }
                else
                {
                    userstats += "\nGoal to build muscle and burn weight";
                }

                try
                {
                    var workouts = ((App)Application.Current).UserWorkoutContexts.workouts;

                    if (!string.IsNullOrWhiteSpace(workouts?.GetUserProgramInfoResponseModel?.RecommendedProgram?.Label))
                    {
                        userstats += $"\nCurrent program {workouts?.GetUserProgramInfoResponseModel?.RecommendedProgram?.Label}";
                    }
                }
                catch (Exception ex)
                {

                }
                txt = $"Give me more tips about my progress report? \n {userstats}";

            }
            catch (Exception ex)
            {

            }
            SendMessagestoChatBot(txt, true, 4);
            LoadJustSentSupportMessage();
            LocalDBManager.Instance.SetDBSetting("IsFirstMessage", null);
            LocalDBManager.Instance.SetDBSetting("IsSecondMessage", null);
            LocalDBManager.Instance.SetDBSetting("IsFirstMessageSend", null);

            messageList.Insert(0, new DrMaxMuscle.Helpers.Messages()
            {
                Message = "More tips",
                ProfileUrl = "",
                Nickname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value,
                UserId = LocalDBManager.Instance.GetDBSetting("email")?.Value,
                CreatedDate = DateTime.UtcNow,
            });
        }
        else
        {
            messageList.Insert(0, new DrMaxMuscle.Helpers.Messages()
            {
                Message = "More tips",
                ProfileUrl = "",
                Nickname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value,
                UserId = LocalDBManager.Instance.GetDBSetting("email")?.Value,
                CreatedDate = DateTime.UtcNow,
            });

            FreePlanUserMessage();
        }
        MainThread.BeginInvokeOnMainThread(() =>
        {
            if (messageList?.Count == 1)
            {
                lstChats.ItemsSource = messageList;
                lstChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);
            }
            if (messageList.Count <= 4 && !IsAdmin && messageList.Where(x => x.UserId.Equals(LocalDBManager.Instance.GetDBSetting("email").Value)).ToList().Count == 1 && messageList.FirstOrDefault().UserId.Equals(LocalDBManager.Instance.GetDBSetting("email").Value))
            {

            }
        });
        if (messageList?.Count > 1 && lstSupportChats?.ItemsSource != null)
            lstSupportChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);

        chatSupportInput.MessageText = "";
        if (Device.RuntimePlatform.Equals(Device.Android) && messageList.Count > 1 && lstSupportChats?.ItemsSource != null)
        {
            await Task.Delay(300);
            lstSupportChats.ScrollTo(messageList[1], ScrollToPosition.End, animate: false);
            lstSupportChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);

        }
    }
    async void HelpWithGoal_Clicked()
    {
        AiButton.IsVisible = false;

        var txt = "Help with goal";
        DrMuscleRestClient.Instance.SendMessage(new ChatModel()
        {
            ReceiverId = AppThemeConstants.ChatReceiverId,
            Message = txt
        });
        //if (!IsAdmin)
        if (!App.IsFreePlan)
        {
            try
            {
                bool isKg = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ? true : false;
                decimal _userBodyWeight = 0;
                if (LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value != null)
                {
                    _userBodyWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value, CultureInfo.InvariantCulture);
                    Config.CurrentWeight = _userBodyWeight.ToString();
                }

                decimal goalWeight = 0;

                if (LocalDBManager.Instance.GetDBSetting("WeightGoal")?.Value != null)
                {
                    goalWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("WeightGoal")?.Value.Replace(",", "."), CultureInfo.InvariantCulture);

                }
                var userstats = "";
                if (isKg)
                {
                    userstats = string.Format("My body weight {0:0.##} {1}", Math.Round(_userBodyWeight, 2), "kg");
                    if (goalWeight != 0)
                        userstats += string.Format("\nMy target body weight {0:0.##} {1}", Math.Round(goalWeight, 2), "kg");
                }
                else
                {

                    var truncateWeight = TruncateDecimal(_userBodyWeight, 3);
                    var lbWeight = new MultiUnityWeight(truncateWeight, "kg").Lb;
                    userstats = string.Format("My body weight {0:0.##} {1}", Math.Round(lbWeight, 2), "lbs");
                    if (goalWeight != 0)
                    {
                        userstats += string.Format("\nMy target body weight {0:0.##} {1}", Math.Round(new MultiUnityWeight((decimal)goalWeight, "kg").Lb, 2), "lbs");
                    }
                }
                if (_userBodyWeight > goalWeight)
                {
                    userstats += "\nGoal to gain weight";
                }
                else
                {
                    userstats += "\nGoal to build muscle and burn weight";
                }

                try
                {
                    var workouts = ((App)Application.Current).UserWorkoutContexts.workouts;

                    if (!string.IsNullOrWhiteSpace(workouts?.GetUserProgramInfoResponseModel?.RecommendedProgram?.Label))
                    {
                        userstats += $"\nCurrent program {workouts?.GetUserProgramInfoResponseModel?.RecommendedProgram?.Label}";
                    }
                }
                catch (Exception ex)
                {

                }
                txt = $"How I achieve my goal? \n {userstats}";

            }
            catch (Exception ex)
            {

            }
            SendMessagestoChatBot(txt, true, 4);
            LoadJustSentSupportMessage();
            LocalDBManager.Instance.SetDBSetting("IsFirstMessage", null);
            LocalDBManager.Instance.SetDBSetting("IsSecondMessage", null);
            LocalDBManager.Instance.SetDBSetting("IsFirstMessageSend", null);

            messageList.Insert(0, new DrMaxMuscle.Helpers.Messages()
            {
                Message = "Help with goal",
                ProfileUrl = "",
                Nickname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value,
                UserId = LocalDBManager.Instance.GetDBSetting("email")?.Value,
                CreatedDate = DateTime.UtcNow,
            });
        }
        else
        {
            messageList.Insert(0, new DrMaxMuscle.Helpers.Messages()
            {
                Message = "Help with goal",
                ProfileUrl = "",
                Nickname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value,
                UserId = LocalDBManager.Instance.GetDBSetting("email")?.Value,
                CreatedDate = DateTime.UtcNow,
            });

            FreePlanUserMessage();
        }
        MainThread.BeginInvokeOnMainThread(() =>
        {
            if (messageList?.Count == 1)
            {
                lstChats.ItemsSource = messageList;
                lstChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);
            }
            if (messageList?.Count <= 4 && !IsAdmin && messageList.Where(x => x.UserId.Equals(LocalDBManager.Instance.GetDBSetting("email").Value)).ToList().Count == 1 && messageList.FirstOrDefault().UserId.Equals(LocalDBManager.Instance.GetDBSetting("email").Value))
            {

            }
        });

        if (messageList?.Count > 1)
            lstSupportChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);

        chatSupportInput.MessageText = "";
        if (Device.RuntimePlatform.Equals(Device.Android) && messageList?.Count > 1)
        {
            await Task.Delay(300);
            lstSupportChats.ScrollTo(messageList[1], ScrollToPosition.End, animate: false);
            lstSupportChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);

        }
    }

    async void AIAnalysis_Clicked(bool isBodyWeight = false, bool isVolume = false, bool isStrength = false, bool isInspireMe = false)
    {
        try
        {
            AiButton.IsVisible = false;

            var txt = "Analyze my recent body weight progress.";
            if (isVolume)
                txt = "Analyze my recent volume progress.";
            if (isStrength)
                txt = "Analyze my recent strength progress.";
            if (isInspireMe)
                txt = "Inspire me";

            await Task.Delay(300);

            DrMuscleRestClient.Instance.SendMessage(new ChatModel()
            {
                ReceiverId = AppThemeConstants.ChatReceiverId,
                Message = txt
            });

            if (!App.IsFreePlan)
            {
                //if (!IsAdmin)
                var msgToAi = txt;
                if (isBodyWeight)
                    msgToAi = "Analyze my recent body weight progress. Mention my body weight, target body weight, body weight progress, goal, caloric intake, and macros.";
                if (isVolume)
                    msgToAi = "Analyze my recent volume progress. Mention my volume progress, goal, workouts done, and total weight lifted.";
                if (isStrength)
                    msgToAi = "Analyze my recent strength progress. Mention my strength progress over the past few weeks, goal, workouts done, and total weight lifted.";

                SendMessagestoChatBot($"{msgToAi}", true, 4);

                LoadJustSentSupportMessage();
                LocalDBManager.Instance.SetDBSetting("IsFirstMessage", null);
                LocalDBManager.Instance.SetDBSetting("IsSecondMessage", null);
                LocalDBManager.Instance.SetDBSetting("IsFirstMessageSend", null);

                // Check messageList before accessing it
                if (messageList != null)
                {
                    messageList.Insert(0, new DrMaxMuscle.Helpers.Messages()
                    {
                        Message = txt,
                        ProfileUrl = "",
                        Nickname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value,
                        UserId = LocalDBManager.Instance.GetDBSetting("email")?.Value,
                        CreatedDate = DateTime.UtcNow,
                    });
                }
            }
            else
            {
                // Free Plan message logic
                if (messageList != null)
                {
                    messageList.Insert(0, new DrMaxMuscle.Helpers.Messages()
                    {
                        Message = txt,
                        ProfileUrl = "",
                        Nickname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value,
                        UserId = LocalDBManager.Instance.GetDBSetting("email")?.Value,
                        CreatedDate = DateTime.UtcNow,
                    });

                    FreePlanUserMessage();
                }
            }
            MainThread.BeginInvokeOnMainThread(() =>
            {
                if (messageList != null  && messageList.Count == 1)
                {
                    
                    lstChats.ItemsSource = messageList;
                    if (lstChats.ItemsSource != null)
                        lstChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);
                }
                if (messageList != null && messageList.Count <= 4 && !IsAdmin && messageList.Where(x => x.UserId.Equals(LocalDBManager.Instance.GetDBSetting("email").Value)).ToList().Count == 1 && messageList.First().UserId.Equals(LocalDBManager.Instance.GetDBSetting("email").Value))
                {

                }
            });
            if (messageList != null  && messageList.Count > 1 && lstSupportChats.ItemsSource != null)
                lstSupportChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);

            chatSupportInput.MessageText = "";
            if (Device.RuntimePlatform.Equals(Device.Android) && messageList != null && messageList.Count > 1)
            {
                await Task.Delay(300);
                if (lstSupportChats.ItemsSource != null)
                {
                    lstSupportChats.ScrollTo(messageList[1], ScrollToPosition.End, animate: false);
                    lstSupportChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);
                }
            }
        }
        catch (Exception ex)
        {
        }
    }

    async void AiButton_Clicked(System.Object sender, System.EventArgs e)
    {
        try
        {
            AiButton.IsVisible = false;

            var txt = "How do I build muscle?";

            DrMuscleRestClient.Instance.SendMessage(new ChatModel()
            {
                ReceiverId = AppThemeConstants.ChatReceiverId,
                Message = txt
            });
            if (!App.IsFreePlan)
            {
                //if (!IsAdmin)
                SendMessagestoChatBot(txt, true, 4);
                LoadJustSentSupportMessage();
                LocalDBManager.Instance.SetDBSetting("IsFirstMessage", null);
                LocalDBManager.Instance.SetDBSetting("IsSecondMessage", null);
                LocalDBManager.Instance.SetDBSetting("IsFirstMessageSend", null);
                //if (!isadded.Result)
                //    return;
                messageList.Insert(0, new DrMaxMuscle.Helpers.Messages()
                {
                    Message = txt,
                    ProfileUrl = "",
                    Nickname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value,
                    UserId = LocalDBManager.Instance.GetDBSetting("email")?.Value,
                    CreatedDate = DateTime.UtcNow,
                });
            }
            else
            {
                messageList.Insert(0, new DrMaxMuscle.Helpers.Messages()
                {
                    Message = txt,
                    ProfileUrl = "",
                    Nickname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value,
                    UserId = LocalDBManager.Instance.GetDBSetting("email")?.Value,
                    CreatedDate = DateTime.UtcNow,
                });

                FreePlanUserMessage();
            }
            MainThread.BeginInvokeOnMainThread(() =>
            {
                if (messageList?.Count == 1)
                {
                    lstChats.ItemsSource = messageList;
                    lstChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);
                }
                if (messageList?.Count <= 4 && !IsAdmin && messageList.Where(x => x.UserId.Equals(LocalDBManager.Instance.GetDBSetting("email").Value)).ToList().Count == 1 && messageList.First().UserId.Equals(LocalDBManager.Instance.GetDBSetting("email").Value))
                {

                }
            });
            if(messageList?.Count>0)
                lstSupportChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);
            //});
            chatSupportInput.MessageText = "";
            if (Device.RuntimePlatform.Equals(Device.Android) && messageList.Count > 1)
            {
                await Task.Delay(300);
                lstSupportChats.ScrollTo(messageList[1], ScrollToPosition.End, animate: false);
                lstSupportChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);

            }
        }
        catch (Exception ex)
        {

        }
    }

    private void lstChats_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        chatInput.UnFocusEntry();
    }
}
public class MutedList
{
    public object phone_number { get; set; }
    public int remaining_duration { get; set; }
    public bool require_auth_for_profile_image { get; set; }
    public string description { get; set; }
    public int end_at { get; set; }
    public string user_id { get; set; }
    public string nickname { get; set; }
    public string profile_url { get; set; }
    public int seconds { get; set; } = -1;
    public Metadata metadata { get; set; }
}

public class Muted
{
    public List<MutedList> muted_list { get; set; }
    public string next { get; set; }
}
