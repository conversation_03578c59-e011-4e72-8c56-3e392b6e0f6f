import XCTest
import <PERSON><PERSON>
@testable import DrMuscleWatchApp

class ErrorBannerViewTests: XCTestCase {
    
    func testErrorBannerViewInitialization() {
        // Given
        let message = "Test error message"
        let action = {}
        
        // When
        let errorBanner = ErrorBannerView(message: message, action: action)
        
        // Then
        XCTAssertEqual(errorBanner.message, message)
        XCTAssertNotNil(errorBanner.action)
    }
    
    func testErrorBannerViewWithoutAction() {
        // Given
        let message = "Test error message"
        
        // When
        let errorBanner = ErrorBannerView(message: message)
        
        // Then
        XCTAssertEqual(errorBanner.message, message)
        XCTAssertNil(errorBanner.action)
    }
    
    func testErrorBannerViewDismissAfterDelay() {
        // Given
        let message = "Test error message"
        let expectation = XCTestExpectation(description: "Banner should auto-dismiss")
        
        // When
        let errorBanner = ErrorBannerView(message: message, autoDismiss: true, dismissAfter: 0.1)
        
        // Then
        // Wait for the banner to auto-dismiss
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            XCTAssertFalse(errorBanner.isVisible)
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 0.3)
    }
}
