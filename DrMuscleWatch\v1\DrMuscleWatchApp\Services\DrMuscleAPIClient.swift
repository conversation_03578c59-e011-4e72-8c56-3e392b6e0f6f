import Foundation

/// API client for communicating with the Dr. Muscle backend
class DrMuscleAPIClient {
    // MARK: - Properties

    /// Base URL for the API
    private let baseURL: String = "https://api.drmuscleapp.com/"

    /// Authentication token
    private var token: String?

    /// Shared instance (singleton)
    static let shared = DrMuscleAPIClient()

    // MARK: - Initialization

    private init() {
        // Load token from secure storage if available
        loadToken()
    }

    // MARK: - Token Management

    /// Store the authentication token securely
    /// - Parameter token: The token to store
    func storeToken(_ token: String) {
        self.token = token

        // Store in Keychain
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: "DrMuscleAuthToken",
            kSecValueData as String: token.data(using: .utf8)!,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlocked
        ]

        // Delete any existing token before saving
        SecItemDelete(query as CFDictionary)
        SecItemAdd(query as CFDictionary, nil)
    }

    /// Load the authentication token from secure storage
    private func loadToken() {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: "DrMuscleAuthToken",
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]

        var dataTypeRef: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &dataTypeRef)

        if status == errSecSuccess, let retrievedData = dataTypeRef as? Data, let retrievedString = String(data: retrievedData, encoding: .utf8) {
            self.token = retrievedString
        }
    }

    /// Clear the stored authentication token
    func clearToken() {
        self.token = nil

        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: "DrMuscleAuthToken"
        ]

        SecItemDelete(query as CFDictionary)
    }

    // MARK: - API Methods

    /// Sign in with Apple
    /// - Parameter idToken: The Apple ID token
    /// - Returns: User information including authentication token
    func signInWithApple(idToken: String) async throws -> UserInfosModel {
        return try await postJSON(route: "api/Account/RegisterWithApple", model: ["token": idToken])
    }

    /// Get the user's workout groups
    /// - Returns: Workout template group model
    func getUserWorkoutGroup() async throws -> WorkoutTemplateGroupModel {
        return try await postJSON(route: "api/Workout/GetUserWorkoutTemplateGroup", model: nil)
    }

    /// Get a specific workout
    /// - Parameter workoutId: The ID of the workout to fetch
    /// - Returns: Workout template model
    func getUserWorkout(workoutId: Int) async throws -> WorkoutTemplateModel {
        return try await postJSON(route: "api/Workout/GetUserWorkout", model: ["workoutId": workoutId])
    }

    /// Get recommendations for an exercise
    /// - Parameter model: The recommendation request model
    /// - Returns: Recommendation model with sets
    func getRecommendationForExercise(model: RecommendationRequestModel) async throws -> RecommendationModel {
        return try await postJSON(route: "api/Exercise/GetRecommendationForExercise", model: model)
    }

    /// Log a completed set
    /// - Parameter model: The workout log serie model
    /// - Returns: Boolean indicating success
    func addWorkoutLogSerie(model: WorkoutLogSerieModel) async throws -> BooleanModel {
        return try await postJSON(route: "api/Exercise/AddWorkoutLogSerieNew", model: model)
    }

    /// Save a completed workout
    /// - Parameter model: The save workout model
    /// - Returns: Boolean indicating success
    func saveWorkoutV3(model: SaveWorkoutModel) async throws -> BooleanModel {
        return try await postJSON(route: "api/Workout/SaveWorkoutV3Pro", model: model)
    }

    /// Get historical data for an exercise
    /// - Parameter exerciseId: The ID of the exercise
    /// - Returns: List of historical data for the exercise
    func getLastExerciseHistory(exerciseId: Int64) async throws -> [HistoryModel] {
        return try await postJSON(route: "api/Exercise/GetLastExerciseWorkoutHistory", model: exerciseId)
    }

    /// Get one-rep max data for an exercise
    /// - Parameter model: The request model containing the exercise ID
    /// - Returns: List of one-rep max data for the exercise
    func getOneRMForExercise(model: GetOneRMforExerciseModel) async throws -> [OneRMModel] {
        return try await postJSON(route: "api/Exercise/GetOneRMForExercise", model: model)
    }

    // MARK: - Generic Request Handler

    /// Generic method to make POST requests to the API
    /// - Parameters:
    ///   - route: The API route
    ///   - model: The request model (optional)
    /// - Returns: Decoded response of type T
    private func postJSON<T: Decodable, U: Encodable>(route: String, model: U?) async throws -> T {
        // Create URL
        guard let url = URL(string: baseURL + route) else {
            throw APIError.invalidURL
        }

        // Create request
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")

        // Add authorization header if token is available
        if let token = token {
            request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }

        // Add body if model is provided
        if let model = model {
            let encoder = JSONEncoder()
            request.httpBody = try encoder.encode(model)
        }

        // Make request
        let (data, response) = try await URLSession.shared.data(for: request)

        // Check response
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.invalidResponse
        }

        // Check status code
        guard 200...299 ~= httpResponse.statusCode else {
            throw APIError.serverError(statusCode: httpResponse.statusCode)
        }

        // Decode response
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601

        do {
            return try decoder.decode(T.self, from: data)
        } catch {
            throw APIError.decodingError(error: error)
        }
    }
}

// MARK: - API Errors

/// Errors that can occur during API requests
enum APIError: Error {
    case invalidURL
    case invalidResponse
    case serverError(statusCode: Int)
    case decodingError(error: Error)
    case networkError(error: Error)

    var localizedDescription: String {
        switch self {
        case .invalidURL:
            return "Invalid URL"
        case .invalidResponse:
            return "Invalid response from server"
        case .serverError(let statusCode):
            return "Server error with status code: \(statusCode)"
        case .decodingError(let error):
            return "Failed to decode response: \(error.localizedDescription)"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        }
    }
}
